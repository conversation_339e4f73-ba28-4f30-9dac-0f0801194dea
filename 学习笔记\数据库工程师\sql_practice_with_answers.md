# SQL专项练习题及详细解析

## 数据库背景说明

为了便于练习，我们设计一个简单的公司管理系统数据库，包含以下表：

### 1. 员工表（employees）
```sql
CREATE TABLE employees (
    emp_id INT PRIMARY KEY,
    emp_name VARCHAR(50) NOT NULL,
    dept_id INT,
    manager_id INT,
    salary DECIMAL(10,2),
    hire_date DATE,
    job_title VARCHAR(50),
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20)
);
```

### 2. 部门表（departments）
```sql
CREATE TABLE departments (
    dept_id INT PRIMARY KEY,
    dept_name VARCHAR(50) NOT NULL,
    location VARCHAR(100),
    budget DECIMAL(12,2)
);
```

### 3. 项目表（projects）
```sql
CREATE TABLE projects (
    project_id INT PRIMARY KEY,
    project_name VARCHAR(100) NOT NULL,
    start_date DATE,
    end_date DATE,
    budget DECIMAL(12,2),
    status VARCHAR(20)
);
```

### 4. 员工项目关系表（employee_projects）
```sql
CREATE TABLE employee_projects (
    emp_id INT,
    project_id INT,
    role VARCHAR(50),
    hours_worked INT,
    PRIMARY KEY (emp_id, project_id)
);
```

### 5. 薪资历史表（salary_history）
```sql
CREATE TABLE salary_history (
    history_id INT PRIMARY KEY AUTO_INCREMENT,
    emp_id INT,
    old_salary DECIMAL(10,2),
    new_salary DECIMAL(10,2),
    change_date DATE,
    reason VARCHAR(200)
);
```

### 示例数据
```sql
-- 部门数据
INSERT INTO departments VALUES
(1, '技术部', '北京', 1000000),
(2, '销售部', '上海', 800000),
(3, '人事部', '北京', 500000),
(4, '财务部', '深圳', 600000);

-- 员工数据
INSERT INTO employees VALUES
(101, '张三', 1, NULL, 25000, '2020-01-15', '技术总监', '<EMAIL>', '13800138001'),
(102, '李四', 1, 101, 18000, '2020-03-20', '高级工程师', '<EMAIL>', '13800138002'),
(103, '王五', 1, 101, 15000, '2021-06-10', '工程师', '<EMAIL>', '13800138003'),
(104, '赵六', 2, NULL, 22000, '2019-11-05', '销售总监', '<EMAIL>', '13800138004'),
(105, '钱七', 2, 104, 12000, '2021-02-28', '销售经理', '<EMAIL>', '13800138005'),
(106, '孙八', 3, NULL, 20000, '2020-08-15', '人事总监', '<EMAIL>', '13800138006'),
(107, '周九', 1, 102, 13000, '2022-01-10', '工程师', '<EMAIL>', '13800138007'),
(108, '吴十', 4, NULL, 23000, '2019-05-20', '财务总监', '<EMAIL>', '13800138008');

-- 项目数据
INSERT INTO projects VALUES
(1001, '电商平台升级', '2023-01-01', '2023-06-30', 500000, '进行中'),
(1002, '数据分析系统', '2023-03-15', '2023-12-31', 800000, '进行中'),
(1003, '客户管理系统', '2022-06-01', '2023-02-28', 300000, '已完成'),
(1004, '移动APP开发', '2023-02-01', '2023-08-31', 600000, '进行中');

-- 员工项目关系数据
INSERT INTO employee_projects VALUES
(101, 1001, '项目经理', 200),
(102, 1001, '技术负责人', 300),
(103, 1001, '开发人员', 400),
(102, 1002, '项目经理', 250),
(103, 1002, '开发人员', 350),
(107, 1002, '开发人员', 300),
(101, 1003, '技术顾问', 100),
(104, 1003, '业务负责人', 150);
```

---

## 练习题部分

### 基础查询题（1-10题）

#### 题目1：查询所有员工的姓名和工资
**要求**：显示所有员工的姓名和工资信息

#### 题目2：查询工资大于15000的员工
**要求**：显示工资大于15000的员工的所有信息

#### 题目3：查询技术部的所有员工
**要求**：显示技术部（部门ID为1）的员工姓名、工资和职位

#### 题目4：查询2020年入职的员工
**要求**：显示2020年入职的员工姓名和入职日期

#### 题目5：统计每个部门的员工数量
**要求**：显示部门ID和对应的员工数量

### 连接查询题（11-20题）

#### 题目11：查询员工及其所在部门名称
**要求**：显示员工姓名、工资和所在部门名称

#### 题目12：查询有下属的经理信息
**要求**：显示经理的姓名和其下属数量

#### 题目13：查询参与项目的员工信息
**要求**：显示员工姓名、项目名称和在项目中的角色

#### 题目14：查询每个部门的平均工资
**要求**：显示部门名称和该部门的平均工资

#### 题目15：查询没有参与任何项目的员工
**要求**：显示没有参与项目的员工姓名和部门

### 高级查询题（21-30题）

#### 题目21：查询工资排名前3的员工
**要求**：显示工资最高的3名员工信息

#### 题目22：查询各部门工资最高的员工
**要求**：显示每个部门中工资最高的员工姓名和工资

#### 题目23：查询工资高于其所在部门平均工资的员工
**要求**：显示员工姓名、工资和其部门平均工资

#### 题目24：查询项目参与人数和总工时
**要求**：显示每个项目的参与人数和总工时

#### 题目25：查询同时参与多个项目的员工
**要求**：显示参与2个或以上项目的员工及其参与的项目数

### 数据操作题（31-40题）

#### 题目31：插入新员工记录
**要求**：插入一名新员工，姓名"陈十一"，技术部，工资16000

#### 题目32：更新员工工资
**要求**：将所有工程师的工资增加10%

#### 题目33：删除已完成的项目记录
**要求**：删除状态为"已完成"的项目

#### 题目34：批量插入数据
**要求**：使用一条INSERT语句插入多条薪资历史记录

#### 题目35：更新部门预算
**要求**：将技术部的预算增加20%

### 复杂查询题（41-50题）

#### 题目41：使用子查询找出最高工资的部门
**要求**：找出平均工资最高的部门名称

#### 题目42：使用窗口函数进行排名
**要求**：为每个部门的员工按工资进行排名

#### 题目43：递归查询组织结构
**要求**：查询某个经理的所有下属（包括间接下属）

#### 题目44：使用CASE语句进行工资等级划分
**要求**：根据工资划分等级：高级(>20000)、中级(15000-20000)、初级(<15000)

#### 题目45：查询连续日期的数据
**要求**：找出连续3天以上都有员工入职的日期段

### 性能优化题（46-50题）

#### 题目46：创建合适的索引
**要求**：为经常查询的字段创建索引

#### 题目47：优化慢查询
**要求**：优化一个复杂的连接查询

#### 题目48：使用EXPLAIN分析查询
**要求**：分析查询执行计划并优化

#### 题目49：避免全表扫描
**要求**：改写查询避免全表扫描

#### 题目50：批量操作优化
**要求**：优化大批量数据插入

---

## 答案及详细解析

### 基础查询题解析（1-10题）

#### 答案1：查询所有员工的姓名和工资
```sql
SELECT emp_name, salary 
FROM employees;
```
**解析**：
- 这是最基础的SELECT查询
- SELECT后面跟要查询的列名，多个列用逗号分隔
- FROM指定要查询的表
- 分号(;)表示SQL语句结束

#### 答案2：查询工资大于15000的员工
```sql
SELECT * 
FROM employees 
WHERE salary > 15000;
```
**解析**：
- WHERE子句用于筛选满足条件的记录
- 比较运算符：> (大于)、< (小于)、>= (大于等于)、<= (小于等于)、= (等于)、<> 或 != (不等于)
- * 表示查询所有列

#### 答案3：查询技术部的所有员工
```sql
SELECT emp_name, salary, job_title 
FROM employees 
WHERE dept_id = 1;
```
**解析**：
- 通过dept_id = 1筛选技术部员工
- 只选择需要的列（emp_name, salary, job_title）而不是用*，这是好习惯
- 可以提高查询效率，减少网络传输

#### 答案4：查询2020年入职的员工
```sql
-- 方法1：使用YEAR函数
SELECT emp_name, hire_date 
FROM employees 
WHERE YEAR(hire_date) = 2020;

-- 方法2：使用日期范围
SELECT emp_name, hire_date 
FROM employees 
WHERE hire_date >= '2020-01-01' AND hire_date <= '2020-12-31';

-- 方法3：使用BETWEEN
SELECT emp_name, hire_date 
FROM employees 
WHERE hire_date BETWEEN '2020-01-01' AND '2020-12-31';
```
**解析**：
- YEAR()函数提取日期的年份部分
- BETWEEN...AND...包含边界值，相当于 >= AND <=
- 日期字符串格式：'YYYY-MM-DD'
- 方法1最简洁，但可能无法使用索引；方法2和3可以利用索引

#### 答案5：统计每个部门的员工数量
```sql
SELECT dept_id, COUNT(*) as employee_count 
FROM employees 
GROUP BY dept_id;
```
**解析**：
- GROUP BY用于分组，将相同dept_id的记录分为一组
- COUNT(*)统计每组的记录数
- as employee_count给统计结果起别名，使结果更易读
- 聚合函数还包括：SUM(), AVG(), MAX(), MIN()

### 连接查询题解析（11-20题）

#### 答案11：查询员工及其所在部门名称
```sql
-- 使用INNER JOIN
SELECT e.emp_name, e.salary, d.dept_name 
FROM employees e 
INNER JOIN departments d ON e.dept_id = d.dept_id;

-- 使用传统写法
SELECT e.emp_name, e.salary, d.dept_name 
FROM employees e, departments d 
WHERE e.dept_id = d.dept_id;
```
**解析**：
- INNER JOIN只返回两表都有匹配的记录
- ON子句指定连接条件
- 表别名(e, d)简化书写，提高可读性
- 两种写法效果相同，但JOIN语法更清晰

#### 答案12：查询有下属的经理信息
```sql
SELECT 
    m.emp_name as manager_name,
    COUNT(e.emp_id) as subordinate_count
FROM employees m
INNER JOIN employees e ON m.emp_id = e.manager_id
GROUP BY m.emp_id, m.emp_name;
```
**解析**：
- 自连接：同一张表连接自己
- m代表经理表，e代表员工表
- 通过manager_id关联找出上下级关系
- GROUP BY分组统计每个经理的下属数量

#### 答案13：查询参与项目的员工信息
```sql
SELECT 
    e.emp_name,
    p.project_name,
    ep.role
FROM employees e
INNER JOIN employee_projects ep ON e.emp_id = ep.emp_id
INNER JOIN projects p ON ep.project_id = p.project_id
ORDER BY e.emp_name, p.project_name;
```
**解析**：
- 三表连接：员工表 → 关系表 → 项目表
- employee_projects是中间表，建立多对多关系
- ORDER BY对结果排序，先按姓名后按项目名
- 多表连接时注意连接顺序和条件

#### 答案14：查询每个部门的平均工资
```sql
SELECT 
    d.dept_name,
    AVG(e.salary) as avg_salary
FROM departments d
LEFT JOIN employees e ON d.dept_id = e.dept_id
GROUP BY d.dept_id, d.dept_name
ORDER BY avg_salary DESC;
```
**解析**：
- LEFT JOIN保证所有部门都显示，即使没有员工
- AVG()计算平均值，自动忽略NULL
- GROUP BY需要包含SELECT中的所有非聚合列
- ORDER BY avg_salary DESC按平均工资降序排列

#### 答案15：查询没有参与任何项目的员工
```sql
-- 方法1：使用LEFT JOIN
SELECT 
    e.emp_name,
    d.dept_name
FROM employees e
LEFT JOIN departments d ON e.dept_id = d.dept_id
LEFT JOIN employee_projects ep ON e.emp_id = ep.emp_id
WHERE ep.emp_id IS NULL;

-- 方法2：使用NOT IN子查询
SELECT 
    e.emp_name,
    d.dept_name
FROM employees e
LEFT JOIN departments d ON e.dept_id = d.dept_id
WHERE e.emp_id NOT IN (
    SELECT DISTINCT emp_id 
    FROM employee_projects
);

-- 方法3：使用NOT EXISTS
SELECT 
    e.emp_name,
    d.dept_name
FROM employees e
LEFT JOIN departments d ON e.dept_id = d.dept_id
WHERE NOT EXISTS (
    SELECT 1 
    FROM employee_projects ep 
    WHERE ep.emp_id = e.emp_id
);
```
**解析**：
- 方法1：LEFT JOIN后检查NULL，找出没有匹配的记录
- 方法2：NOT IN子查询，注意子查询结果不能包含NULL
- 方法3：NOT EXISTS更高效，特别是大数据量时
- 三种方法各有优劣，根据实际情况选择

### 高级查询题解析（21-30题）

#### 答案21：查询工资排名前3的员工
```sql
-- MySQL/PostgreSQL
SELECT emp_name, salary 
FROM employees 
ORDER BY salary DESC 
LIMIT 3;

-- SQL Server
SELECT TOP 3 emp_name, salary 
FROM employees 
ORDER BY salary DESC;

-- Oracle (旧版本)
SELECT emp_name, salary 
FROM (
    SELECT emp_name, salary 
    FROM employees 
    ORDER BY salary DESC
) 
WHERE ROWNUM <= 3;

-- 通用方法：使用窗口函数
SELECT emp_name, salary
FROM (
    SELECT 
        emp_name, 
        salary,
        ROW_NUMBER() OVER (ORDER BY salary DESC) as rn
    FROM employees
) t
WHERE rn <= 3;
```
**解析**：
- 不同数据库的语法略有差异
- LIMIT是MySQL的语法，最简洁
- 窗口函数ROW_NUMBER()是SQL标准，通用性好
- 注意处理工资相同的情况，可能需要RANK()或DENSE_RANK()

#### 答案22：查询各部门工资最高的员工
```sql
-- 方法1：使用窗口函数
SELECT emp_name, dept_id, salary
FROM (
    SELECT 
        emp_name, 
        dept_id, 
        salary,
        ROW_NUMBER() OVER (PARTITION BY dept_id ORDER BY salary DESC) as rn
    FROM employees
) t
WHERE rn = 1;

-- 方法2：使用相关子查询
SELECT e1.emp_name, e1.dept_id, e1.salary
FROM employees e1
WHERE e1.salary = (
    SELECT MAX(e2.salary)
    FROM employees e2
    WHERE e2.dept_id = e1.dept_id
);

-- 方法3：使用JOIN
SELECT e1.emp_name, e1.dept_id, e1.salary
FROM employees e1
INNER JOIN (
    SELECT dept_id, MAX(salary) as max_salary
    FROM employees
    GROUP BY dept_id
) e2 ON e1.dept_id = e2.dept_id AND e1.salary = e2.max_salary;
```
**解析**：
- PARTITION BY在窗口函数中用于分组
- 方法1最灵活，可以轻松获取前N名
- 方法2是相关子查询，每行都执行子查询，效率较低
- 方法3先计算最高工资再连接，效率较高

#### 答案23：查询工资高于其所在部门平均工资的员工
```sql
-- 方法1：使用窗口函数
SELECT 
    emp_name, 
    salary,
    dept_avg_salary
FROM (
    SELECT 
        emp_name, 
        salary,
        AVG(salary) OVER (PARTITION BY dept_id) as dept_avg_salary
    FROM employees
) t
WHERE salary > dept_avg_salary;

-- 方法2：使用JOIN
SELECT 
    e.emp_name, 
    e.salary,
    da.avg_salary as dept_avg_salary
FROM employees e
INNER JOIN (
    SELECT dept_id, AVG(salary) as avg_salary
    FROM employees
    GROUP BY dept_id
) da ON e.dept_id = da.dept_id
WHERE e.salary > da.avg_salary;
```
**解析**：
- 窗口函数可以在同一行显示明细和汇总数据
- PARTITION BY相当于GROUP BY，但不会减少行数
- 方法2通过子查询先计算平均值，再连接比较

#### 答案24：查询项目参与人数和总工时
```sql
SELECT 
    p.project_name,
    COUNT(DISTINCT ep.emp_id) as participant_count,
    SUM(ep.hours_worked) as total_hours
FROM projects p
LEFT JOIN employee_projects ep ON p.project_id = ep.project_id
GROUP BY p.project_id, p.project_name
ORDER BY total_hours DESC;
```
**解析**：
- COUNT(DISTINCT ep.emp_id)统计不重复的员工数
- SUM()计算总工时
- LEFT JOIN确保没有员工参与的项目也显示
- 按总工时降序排列，便于找出工作量大的项目

#### 答案25：查询同时参与多个项目的员工
```sql
SELECT 
    e.emp_name,
    COUNT(ep.project_id) as project_count,
    GROUP_CONCAT(p.project_name SEPARATOR ', ') as projects
FROM employees e
INNER JOIN employee_projects ep ON e.emp_id = ep.emp_id
INNER JOIN projects p ON ep.project_id = p.project_id
GROUP BY e.emp_id, e.emp_name
HAVING COUNT(ep.project_id) >= 2
ORDER BY project_count DESC;
```
**解析**：
- HAVING用于对分组后的结果进行筛选
- WHERE在分组前筛选，HAVING在分组后筛选
- GROUP_CONCAT()是MySQL函数，将多行合并为一行
- 其他数据库有类似函数：STRING_AGG()(PostgreSQL)、LISTAGG()(Oracle)

### 数据操作题解析（31-40题）

#### 答案31：插入新员工记录
```sql
-- 完整插入
INSERT INTO employees (emp_id, emp_name, dept_id, manager_id, salary, hire_date, job_title, email, phone)
VALUES (109, '陈十一', 1, 101, 16000, CURDATE(), '工程师', '<EMAIL>', '13800138009');

-- 部分列插入（其他列使用默认值或NULL）
INSERT INTO employees (emp_id, emp_name, dept_id, salary, hire_date, job_title)
VALUES (109, '陈十一', 1, 16000, CURDATE(), '工程师');
```
**解析**：
- 列出所有列名可以避免列顺序变化导致的错误
- CURDATE()返回当前日期
- 未指定的列将使用默认值或NULL
- 主键和NOT NULL列必须提供值

#### 答案32：更新员工工资
```sql
-- 更新前先查看将影响的数据
SELECT emp_id, emp_name, salary, salary * 1.1 as new_salary
FROM employees
WHERE job_title = '工程师';

-- 执行更新
UPDATE employees
SET salary = salary * 1.1
WHERE job_title = '工程师';

-- 记录薪资变更历史
INSERT INTO salary_history (emp_id, old_salary, new_salary, change_date, reason)
SELECT 
    emp_id, 
    salary as old_salary,
    salary * 1.1 as new_salary,
    CURDATE(),
    '年度调薪：工程师岗位上调10%'
FROM employees
WHERE job_title = '工程师';
```
**解析**：
- 更新前先用SELECT确认影响范围，避免误操作
- salary = salary * 1.1 表示在原值基础上增加10%
- 重要的薪资变更应该记录历史，便于审计
- 可以使用事务确保数据一致性

#### 答案33：删除已完成的项目记录
```sql
-- 查看将要删除的数据
SELECT * FROM projects WHERE status = '已完成';

-- 检查是否有关联数据
SELECT p.project_name, COUNT(ep.emp_id) as employee_count
FROM projects p
LEFT JOIN employee_projects ep ON p.project_id = ep.project_id
WHERE p.status = '已完成'
GROUP BY p.project_id, p.project_name;

-- 先删除关联表数据（如果有外键约束）
DELETE FROM employee_projects
WHERE project_id IN (
    SELECT project_id FROM projects WHERE status = '已完成'
);

-- 再删除主表数据
DELETE FROM projects
WHERE status = '已完成';

-- 或者使用级联删除（需要预先设置外键级联）
DELETE FROM projects
WHERE status = '已完成';
```
**解析**：
- 删除前必须检查关联数据，避免破坏参照完整性
- 如果有外键约束，需要先删除子表数据
- 可以设置ON DELETE CASCADE自动级联删除
- 重要数据建议软删除（标记删除）而非物理删除

#### 答案34：批量插入数据
```sql
-- 一次插入多行
INSERT INTO salary_history (emp_id, old_salary, new_salary, change_date, reason)
VALUES 
    (101, 23000, 25000, '2023-01-01', '年度调薪'),
    (102, 16000, 18000, '2023-01-01', '年度调薪'),
    (103, 13000, 15000, '2023-01-01', '年度调薪'),
    (104, 20000, 22000, '2023-01-01', '年度调薪');

-- 从查询结果插入
INSERT INTO salary_history (emp_id, old_salary, new_salary, change_date, reason)
SELECT 
    emp_id,
    salary as old_salary,
    CASE 
        WHEN salary < 15000 THEN salary * 1.15
        WHEN salary < 20000 THEN salary * 1.10
        ELSE salary * 1.05
    END as new_salary,
    '2023-07-01',
    '年中调薪：按工资区间调整'
FROM employees;
```
**解析**：
- VALUES后可以跟多组数据，用逗号分隔
- INSERT...SELECT可以从查询结果批量插入
- CASE语句实现条件判断，不同工资区间不同涨幅
- 批量插入比逐条插入效率高很多

#### 答案35：更新部门预算
```sql
-- 查看当前预算
SELECT dept_name, budget, budget * 1.2 as new_budget
FROM departments
WHERE dept_id = 1;

-- 更新预算
UPDATE departments
SET budget = budget * 1.2
WHERE dept_id = 1;

-- 使用事务确保数据一致性
START TRANSACTION;

-- 记录预算变更日志（假设有预算历史表）
INSERT INTO budget_history (dept_id, old_budget, new_budget, change_date)
SELECT dept_id, budget, budget * 1.2, CURDATE()
FROM departments
WHERE dept_id = 1;

-- 更新预算
UPDATE departments
SET budget = budget * 1.2
WHERE dept_id = 1;

COMMIT;
```
**解析**：
- 财务相关操作应该使用事务保证原子性
- START TRANSACTION开始事务
- COMMIT提交事务，ROLLBACK回滚事务
- 预算变更应该有审计记录

### 复杂查询题解析（41-50题）

#### 答案41：使用子查询找出最高工资的部门
```sql
-- 方法1：使用子查询和LIMIT
SELECT dept_name
FROM departments
WHERE dept_id = (
    SELECT dept_id
    FROM employees
    GROUP BY dept_id
    ORDER BY AVG(salary) DESC
    LIMIT 1
);

-- 方法2：使用WITH子句（CTE）
WITH dept_avg AS (
    SELECT 
        dept_id,
        AVG(salary) as avg_salary
    FROM employees
    GROUP BY dept_id
),
max_avg AS (
    SELECT MAX(avg_salary) as max_avg_salary
    FROM dept_avg
)
SELECT d.dept_name, da.avg_salary
FROM departments d
INNER JOIN dept_avg da ON d.dept_id = da.dept_id
INNER JOIN max_avg ma ON da.avg_salary = ma.max_avg_salary;

-- 方法3：使用窗口函数
SELECT dept_name
FROM (
    SELECT 
        d.dept_name,
        AVG(e.salary) as avg_salary,
        RANK() OVER (ORDER BY AVG(e.salary) DESC) as rank_num
    FROM departments d
    INNER JOIN employees e ON d.dept_id = e.dept_id
    GROUP BY d.dept_id, d.dept_name
) t
WHERE rank_num = 1;
```
**解析**：
- CTE (Common Table Expression) 提高可读性
- WITH子句可以定义临时结果集
- RANK()处理并列第一的情况
- 子查询嵌套不宜过深，影响可读性和性能

#### 答案42：使用窗口函数进行排名
```sql
SELECT 
    emp_name,
    dept_id,
    salary,
    ROW_NUMBER() OVER (PARTITION BY dept_id ORDER BY salary DESC) as row_num,
    RANK() OVER (PARTITION BY dept_id ORDER BY salary DESC) as rank_num,
    DENSE_RANK() OVER (PARTITION BY dept_id ORDER BY salary DESC) as dense_rank_num,
    PERCENT_RANK() OVER (PARTITION BY dept_id ORDER BY salary DESC) as percent_rank
FROM employees
ORDER BY dept_id, salary DESC;
```
**解析**：
- ROW_NUMBER()：连续编号，1,2,3,4...
- RANK()：相同值同排名，跳过后续