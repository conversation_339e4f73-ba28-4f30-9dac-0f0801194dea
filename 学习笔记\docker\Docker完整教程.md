# 🐳 Docker 完整使用教程

> 🎯 从零基础到生产环境，一站式Docker学习指南！

## 📚 目录
1. [🔰 Docker基础概念](#docker基础概念)
2. [⚙️ Docker安装](#docker安装)
3. [🛠️ Docker基本命令](#docker基本命令)
4. [📝 Dockerfile详解](#dockerfile详解)
5. [🎼 Docker Compose](#docker-compose)
6. [🌐 容器网络管理](#容器网络管理)
7. [💾 数据卷管理](#数据卷管理)
8. [🚀 实战案例](#实战案例)
   - [☕ Java前后端项目部署](#java前后端项目部署)
   - [⚡ Spark集群部署](#spark集群部署)
   - [🗄️ 数据库容器化](#数据库容器化)
   - [🏗️ 微服务架构](#微服务架构)
9. [🏭 生产环境最佳实践](#生产环境最佳实践)
10. [📊 监控和日志](#监控和日志)
11. [🔧 故障排除](#故障排除)

---

## 🔰 Docker基础概念

### 🤔 什么是Docker？

想象一下，你要搬家 🏠➡️🏠。传统的方式是把所有东西一件件打包，到了新家再一件件摆放。但如果有一个神奇的"集装箱"，能把你的整个房间（包括家具、装修、甚至空气）完整地装进去，到哪里都能一模一样地还原出来，那该多好！

**Docker就是软件世界的"集装箱"！** 🚢📦

它让你可以把应用程序和它需要的所有东西（代码、运行环境、系统工具、库文件等）打包成一个"容器"，这个容器可以在任何支持Docker的机器上运行，效果完全一样！

### 🎯 核心概念

让我们用生活中的比喻来理解这些概念：

- **🍰 镜像(Image)**: 就像蛋糕的"配方"，告诉你需要什么材料、怎么制作
- **🎂 容器(Container)**: 就是按照配方做出来的"真正的蛋糕"，可以吃的那个
- **📋 Dockerfile**: 就是写配方的"食谱本"，一步步告诉你怎么做
- **🏪 仓库(Repository)**: 就像"配方商店"，存放各种蛋糕配方的地方
- **🌍 Docker Hub**: 全世界最大的"配方商店"，官方认证！

### 🏗️ Docker架构图

想象Docker就像一个高效的"快递公司"：
- **Docker客户端**：你（顾客）下订单的地方
- **Docker守护进程**：快递公司的分拣中心，真正处理业务
- **Docker镜像**：标准化的"包装模板"
- **Docker容器**：装好货物的"快递包裹"，正在运输中
- **Docker Hub**：全球最大的"包装模板商店"

## 🚀 Docker基本命令

### 镜像管理
```bash
# 搜索镜像
docker search nginx

# 拉取镜像
docker pull nginx:latest

# 查看本地镜像
docker images

# 删除镜像
docker rmi nginx:latest

# 构建镜像
docker build -t myapp:v1.0 .

# 给镜像打标签
docker tag myapp:v1.0 myapp:latest
```

### 容器管理
```bash
# 运行容器
docker run -d --name mynginx -p 80:80 nginx

# 查看运行中的容器
docker ps

# 查看所有容器（包括停止的）
docker ps -a

# 停止容器
docker stop mynginx

# 启动容器
docker start mynginx

# 重启容器
docker restart mynginx

# 删除容器
docker rm mynginx

# 进入容器
docker exec -it mynginx /bin/bash

# 查看容器日志
docker logs mynginx

# 查看容器详细信息
docker inspect mynginx
```

### 系统管理
```bash
# 查看Docker版本
docker version

# 查看Docker系统信息
docker info

# 清理未使用的资源
docker system prune

# 查看磁盘使用情况
docker system df
```

---

## Dockerfile详解

### Dockerfile基本结构
```dockerfile
# 基础镜像
FROM ubuntu:20.04

# 维护者信息
LABEL maintainer="<EMAIL>"

# 设置环境变量
ENV APP_HOME=/app
ENV JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64

# 设置工作目录
WORKDIR $APP_HOME

# 复制文件
COPY . $APP_HOME

# 安装依赖
RUN apt-get update && \
    apt-get install -y openjdk-11-jdk && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 暴露端口
EXPOSE 8080

# 设置启动命令
CMD ["java", "-jar", "app.jar"]
```

### 常用指令详解

#### FROM
指定基础镜像
```dockerfile
FROM node:16-alpine
FROM ubuntu:20.04
FROM scratch  # 空白镜像
```

#### RUN
执行命令并创建新的镜像层
```dockerfile
# Shell形式
RUN apt-get update && apt-get install -y curl

# Exec形式
RUN ["apt-get", "update"]
```

#### COPY vs ADD
```dockerfile
# COPY：简单复制文件
COPY src/ /app/src/

# ADD：支持URL和自动解压
ADD https://example.com/file.tar.gz /tmp/
ADD archive.tar.gz /tmp/  # 自动解压
```

#### ENV
设置环境变量
```dockerfile
ENV NODE_ENV=production
ENV PATH=$PATH:/app/bin
```

#### EXPOSE
声明端口（仅声明，不实际暴露）
```dockerfile
EXPOSE 3000
EXPOSE 8080/tcp
EXPOSE 53/udp
```

#### VOLUME
创建挂载点
```dockerfile
VOLUME ["/data", "/logs"]
```

#### USER
指定运行用户
```dockerfile
RUN groupadd -r appuser && useradd -r -g appuser appuser
USER appuser
```

### 多阶段构建
```dockerfile
# 构建阶段
FROM node:16-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

# 运行阶段
FROM node:16-alpine AS runtime
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

---

## Docker Compose

### 什么是Docker Compose？
Docker Compose是用于定义和运行多容器Docker应用程序的工具。

### docker-compose.yml基本结构
```yaml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    depends_on:
      - db
    volumes:
      - ./src:/app/src
    networks:
      - app-network

  db:
    image: postgres:13
    environment:
      POSTGRES_DB: myapp
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network

volumes:
  postgres_data:

networks:
  app-network:
    driver: bridge
```

### Docker Compose命令
```bash
# 启动服务
docker-compose up

# 后台启动
docker-compose up -d

# 停止服务
docker-compose down

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs

# 重新构建
docker-compose build

# 扩展服务
docker-compose up --scale web=3
```

---

## 容器网络管理

### 网络类型
- **bridge**: 默认网络驱动，适用于单主机
- **host**: 使用主机网络栈
- **overlay**: 用于多主机通信
- **macvlan**: 为容器分配MAC地址
- **none**: 禁用网络

### 网络管理命令
```bash
# 查看网络
docker network ls

# 创建网络
docker network create mynetwork
docker network create --driver bridge --subnet=**********/16 mynetwork

# 查看网络详情
docker network inspect mynetwork

# 连接容器到网络
docker network connect mynetwork container_name

# 断开网络连接
docker network disconnect mynetwork container_name

# 删除网络
docker network rm mynetwork
```

### 网络实例
```bash
# 创建自定义网络
docker network create --driver bridge app-network

# 在网络中运行容器
docker run -d --name web --network app-network nginx
docker run -d --name db --network app-network postgres

# 容器间通信（通过容器名）
docker exec -it web ping db
```

---

## 数据卷管理

### 数据卷类型
1. **匿名卷**: Docker自动管理
2. **命名卷**: 用户指定名称
3. **绑定挂载**: 挂载主机目录

### 数据卷命令
```bash
# 查看数据卷
docker volume ls

# 创建数据卷
docker volume create myvolume

# 查看数据卷详情
docker volume inspect myvolume

# 删除数据卷
docker volume rm myvolume

# 清理未使用的数据卷
docker volume prune
```

### 数据卷使用示例
```bash
# 命名卷
docker run -d --name db -v postgres_data:/var/lib/postgresql/data postgres

# 绑定挂载
docker run -d --name web -v /host/path:/container/path nginx

# 只读挂载
docker run -d --name web -v /host/path:/container/path:ro nginx

# 临时文件系统
docker run -d --name web --tmpfs /tmp nginx
```

---

## 实战案例

### Java前后端项目部署

#### 项目结构
```
java-fullstack/
├── backend/
│   ├── src/
│   ├── pom.xml
│   └── Dockerfile
├── frontend/
│   ├── src/
│   ├── package.json
│   └── Dockerfile
├── nginx/
│   └── nginx.conf
└── docker-compose.yml
```

#### 后端Dockerfile
```dockerfile
# backend/Dockerfile
FROM maven:3.8.4-openjdk-11 AS builder

WORKDIR /app
COPY pom.xml .
RUN mvn dependency:go-offline

COPY src ./src
RUN mvn clean package -DskipTests

FROM openjdk:11-jre-slim
WORKDIR /app
COPY --from=builder /app/target/*.jar app.jar

EXPOSE 8080
ENTRYPOINT ["java", "-jar", "app.jar"]
```

#### 前端Dockerfile
```dockerfile
# frontend/Dockerfile
FROM node:16-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
```

#### Nginx配置
```nginx
# nginx/nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:8080;
    }

    server {
        listen 80;

        location / {
            root /usr/share/nginx/html;
            try_files $uri $uri/ /index.html;
        }

        location /api/ {
            proxy_pass http://backend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}
```

#### Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  backend:
    build: ./backend
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - DATABASE_URL=*******************************
    depends_on:
      - db
    networks:
      - app-network

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - app-network

  db:
    image: postgres:13
    environment:
      POSTGRES_DB: myapp
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network

volumes:
  postgres_data:

networks:
  app-network:
    driver: bridge
```

#### 部署命令
```bash
# 构建并启动
docker-compose up --build -d

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### Spark集群部署

#### 项目结构
```
spark-cluster/
├── docker-compose.yml
├── spark-master/
│   └── Dockerfile
├── spark-worker/
│   └── Dockerfile
└── jupyter/
    └── Dockerfile
```

#### Spark Master Dockerfile
```dockerfile
# spark-master/Dockerfile
FROM openjdk:11-jre-slim

ENV SPARK_VERSION=3.3.0
ENV HADOOP_VERSION=3

RUN apt-get update && \
    apt-get install -y curl python3 python3-pip && \
    curl -O https://archive.apache.org/dist/spark/spark-${SPARK_VERSION}/spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}.tgz && \
    tar xzf spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}.tgz && \
    mv spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION} /opt/spark && \
    rm spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}.tgz

ENV SPARK_HOME=/opt/spark
ENV PATH=$PATH:$SPARK_HOME/bin:$SPARK_HOME/sbin

WORKDIR /opt/spark

EXPOSE 8080 7077

CMD ["/opt/spark/bin/spark-class", "org.apache.spark.deploy.master.Master"]
```

#### Spark Worker Dockerfile
```dockerfile
# spark-worker/Dockerfile
FROM openjdk:11-jre-slim

ENV SPARK_VERSION=3.3.0
ENV HADOOP_VERSION=3

RUN apt-get update && \
    apt-get install -y curl python3 python3-pip && \
    curl -O https://archive.apache.org/dist/spark/spark-${SPARK_VERSION}/spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}.tgz && \
    tar xzf spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}.tgz && \
    mv spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION} /opt/spark && \
    rm spark-${SPARK_VERSION}-bin-hadoop${HADOOP_VERSION}.tgz

ENV SPARK_HOME=/opt/spark
ENV PATH=$PATH:$SPARK_HOME/bin:$SPARK_HOME/sbin

WORKDIR /opt/spark

EXPOSE 8081

CMD ["/opt/spark/bin/spark-class", "org.apache.spark.deploy.worker.Worker", "spark://spark-master:7077"]
```

#### Jupyter Notebook Dockerfile
```dockerfile
# jupyter/Dockerfile
FROM jupyter/pyspark-notebook:latest

USER root

# 安装额外的Python包
RUN pip install --no-cache-dir \
    pandas \
    numpy \
    matplotlib \
    seaborn \
    plotly

USER $NB_UID

EXPOSE 8888
```

#### Spark集群Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  spark-master:
    build: ./spark-master
    container_name: spark-master
    hostname: spark-master
    ports:
      - "8080:8080"  # Spark Master Web UI
      - "7077:7077"  # Spark Master Port
    environment:
      - SPARK_MODE=master
      - SPARK_RPC_AUTHENTICATION_ENABLED=no
      - SPARK_RPC_ENCRYPTION_ENABLED=no
      - SPARK_LOCAL_STORAGE_ENCRYPTION_ENABLED=no
      - SPARK_SSL_ENABLED=no
    networks:
      - spark-network

  spark-worker-1:
    build: ./spark-worker
    container_name: spark-worker-1
    hostname: spark-worker-1
    ports:
      - "8081:8081"
    environment:
      - SPARK_MODE=worker
      - SPARK_MASTER_URL=spark://spark-master:7077
      - SPARK_WORKER_MEMORY=2g
      - SPARK_WORKER_CORES=2
    depends_on:
      - spark-master
    networks:
      - spark-network

  spark-worker-2:
    build: ./spark-worker
    container_name: spark-worker-2
    hostname: spark-worker-2
    ports:
      - "8082:8081"
    environment:
      - SPARK_MODE=worker
      - SPARK_MASTER_URL=spark://spark-master:7077
      - SPARK_WORKER_MEMORY=2g
      - SPARK_WORKER_CORES=2
    depends_on:
      - spark-master
    networks:
      - spark-network

  jupyter:
    build: ./jupyter
    container_name: spark-jupyter
    ports:
      - "8888:8888"
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - SPARK_MASTER=spark://spark-master:7077
    volumes:
      - ./notebooks:/home/<USER>/work
    depends_on:
      - spark-master
    networks:
      - spark-network

networks:
  spark-network:
    driver: bridge

volumes:
  notebooks:
```

#### 启动Spark集群
```bash
# 构建并启动集群
docker-compose up --build -d

# 查看服务状态
docker-compose ps

# 访问Web界面
# Spark Master UI: http://localhost:8080
# Jupyter Notebook: http://localhost:8888

# 扩展Worker节点
docker-compose up --scale spark-worker-1=3 -d

# 查看日志
docker-compose logs spark-master
```

#### Spark作业示例
```python
# 在Jupyter Notebook中运行
from pyspark.sql import SparkSession

# 创建Spark会话
spark = SparkSession.builder \
    .appName("DockerSparkExample") \
    .master("spark://spark-master:7077") \
    .getOrCreate()

# 创建示例数据
data = [("Alice", 25), ("Bob", 30), ("Charlie", 35)]
columns = ["Name", "Age"]

df = spark.createDataFrame(data, columns)
df.show()

# 简单的数据处理
result = df.filter(df.Age > 25).select("Name")
result.show()

spark.stop()
```

### 数据库容器化

#### MySQL集群部署
```yaml
# mysql-cluster/docker-compose.yml
version: '3.8'

services:
  mysql-master:
    image: mysql:8.0
    container_name: mysql-master
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: testdb
      MYSQL_USER: user
      MYSQL_PASSWORD: password
    ports:
      - "3306:3306"
    volumes:
      - mysql_master_data:/var/lib/mysql
      - ./mysql-master.cnf:/etc/mysql/conf.d/mysql.cnf
    networks:
      - mysql-network

  mysql-slave:
    image: mysql:8.0
    container_name: mysql-slave
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: testdb
      MYSQL_USER: user
      MYSQL_PASSWORD: password
    ports:
      - "3307:3306"
    volumes:
      - mysql_slave_data:/var/lib/mysql
      - ./mysql-slave.cnf:/etc/mysql/conf.d/mysql.cnf
    depends_on:
      - mysql-master
    networks:
      - mysql-network

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: phpmyadmin
    environment:
      PMA_HOST: mysql-master
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: rootpassword
    ports:
      - "8080:80"
    depends_on:
      - mysql-master
    networks:
      - mysql-network

volumes:
  mysql_master_data:
  mysql_slave_data:

networks:
  mysql-network:
    driver: bridge
```

#### PostgreSQL高可用部署
```yaml
# postgres-ha/docker-compose.yml
version: '3.8'

services:
  postgres-primary:
    image: postgres:13
    container_name: postgres-primary
    environment:
      POSTGRES_DB: myapp
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_REPLICATION_USER: replicator
      POSTGRES_REPLICATION_PASSWORD: replicator_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_primary_data:/var/lib/postgresql/data
      - ./postgresql.conf:/etc/postgresql/postgresql.conf
      - ./pg_hba.conf:/etc/postgresql/pg_hba.conf
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    networks:
      - postgres-network

  postgres-replica:
    image: postgres:13
    container_name: postgres-replica
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      PGUSER: postgres
      POSTGRES_PRIMARY_HOST: postgres-primary
      POSTGRES_PRIMARY_PORT: 5432
      POSTGRES_REPLICATION_USER: replicator
      POSTGRES_REPLICATION_PASSWORD: replicator_password
    ports:
      - "5433:5432"
    volumes:
      - postgres_replica_data:/var/lib/postgresql/data
    depends_on:
      - postgres-primary
    networks:
      - postgres-network

  pgadmin:
    image: dpage/pgadmin4
    container_name: pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres-primary
    networks:
      - postgres-network

volumes:
  postgres_primary_data:
  postgres_replica_data:
  pgadmin_data:

networks:
  postgres-network:
    driver: bridge
```

#### MongoDB副本集部署
```yaml
# mongodb-replica/docker-compose.yml
version: '3.8'

services:
  mongo1:
    image: mongo:5.0
    container_name: mongo1
    command: mongod --replSet rs0 --bind_ip_all
    ports:
      - "27017:27017"
    volumes:
      - mongo1_data:/data/db
    networks:
      - mongo-network

  mongo2:
    image: mongo:5.0
    container_name: mongo2
    command: mongod --replSet rs0 --bind_ip_all
    ports:
      - "27018:27017"
    volumes:
      - mongo2_data:/data/db
    networks:
      - mongo-network

  mongo3:
    image: mongo:5.0
    container_name: mongo3
    command: mongod --replSet rs0 --bind_ip_all
    ports:
      - "27019:27017"
    volumes:
      - mongo3_data:/data/db
    networks:
      - mongo-network

  mongo-express:
    image: mongo-express
    container_name: mongo-express
    environment:
      ME_CONFIG_MONGODB_SERVER: mongo1
      ME_CONFIG_MONGODB_PORT: 27017
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin
    ports:
      - "8081:8081"
    depends_on:
      - mongo1
    networks:
      - mongo-network

volumes:
  mongo1_data:
  mongo2_data:
  mongo3_data:

networks:
  mongo-network:
    driver: bridge
```

### 微服务架构部署

#### 项目结构
```
microservices/
├── api-gateway/
│   ├── Dockerfile
│   └── nginx.conf
├── user-service/
│   ├── Dockerfile
│   └── src/
├── order-service/
│   ├── Dockerfile
│   └── src/
├── notification-service/
│   ├── Dockerfile
│   └── src/
├── docker-compose.yml
└── docker-compose.prod.yml
```

#### API网关配置
```dockerfile
# api-gateway/Dockerfile
FROM nginx:alpine

COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
```

```nginx
# api-gateway/nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream user-service {
        server user-service:3001;
    }

    upstream order-service {
        server order-service:3002;
    }

    upstream notification-service {
        server notification-service:3003;
    }

    server {
        listen 80;

        location /api/users/ {
            proxy_pass http://user-service/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        location /api/orders/ {
            proxy_pass http://order-service/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        location /api/notifications/ {
            proxy_pass http://notification-service/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}
```

#### 用户服务Dockerfile
```dockerfile
# user-service/Dockerfile
FROM node:16-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3001

USER node

CMD ["npm", "start"]
```

#### 微服务Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  # API网关
  api-gateway:
    build: ./api-gateway
    ports:
      - "80:80"
    depends_on:
      - user-service
      - order-service
      - notification-service
    networks:
      - microservices-network

  # 用户服务
  user-service:
    build: ./user-service
    environment:
      - NODE_ENV=development
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=users
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - postgres
      - redis
    networks:
      - microservices-network

  # 订单服务
  order-service:
    build: ./order-service
    environment:
      - NODE_ENV=development
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=orders
      - RABBITMQ_HOST=rabbitmq
    depends_on:
      - postgres
      - rabbitmq
    networks:
      - microservices-network

  # 通知服务
  notification-service:
    build: ./notification-service
    environment:
      - NODE_ENV=development
      - RABBITMQ_HOST=rabbitmq
      - EMAIL_SERVICE_URL=http://email-service:3004
    depends_on:
      - rabbitmq
    networks:
      - microservices-network

  # 数据库
  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: microservices
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - microservices-network

  # 缓存
  redis:
    image: redis:6-alpine
    volumes:
      - redis_data:/data
    networks:
      - microservices-network

  # 消息队列
  rabbitmq:
    image: rabbitmq:3-management
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin
    ports:
      - "15672:15672"  # 管理界面
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - microservices-network

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:

networks:
  microservices-network:
    driver: bridge
```

---

## 监控和日志

### ELK Stack部署
```yaml
# elk-stack/docker-compose.yml
version: '3.8'

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.15.0
    container_name: elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - elk-network

  logstash:
    image: docker.elastic.co/logstash/logstash:7.15.0
    container_name: logstash
    volumes:
      - ./logstash/config:/usr/share/logstash/pipeline
    ports:
      - "5044:5044"
    depends_on:
      - elasticsearch
    networks:
      - elk-network

  kibana:
    image: docker.elastic.co/kibana/kibana:7.15.0
    container_name: kibana
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - elk-network

  filebeat:
    image: docker.elastic.co/beats/filebeat:7.15.0
    container_name: filebeat
    user: root
    volumes:
      - ./filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    depends_on:
      - logstash
    networks:
      - elk-network

volumes:
  elasticsearch_data:

networks:
  elk-network:
    driver: bridge
```

### Prometheus + Grafana监控
```yaml
# monitoring/docker-compose.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - monitoring-network

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - monitoring-network

  node-exporter:
    image: prom/node-exporter:latest
    container_name: node-exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.ignored-mount-points=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - monitoring-network

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: cadvisor
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
    networks:
      - monitoring-network

volumes:
  prometheus_data:
  grafana_data:

networks:
  monitoring-network:
    driver: bridge
```

#### Prometheus配置
```yaml
# prometheus/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']

  - job_name: 'app-metrics'
    static_configs:
      - targets: ['app:3000']
```

---

## CI/CD集成

### Jenkins with Docker
```yaml
# jenkins/docker-compose.yml
version: '3.8'

services:
  jenkins:
    image: jenkins/jenkins:lts
    container_name: jenkins
    ports:
      - "8080:8080"
      - "50000:50000"
    volumes:
      - jenkins_data:/var/jenkins_home
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - JAVA_OPTS=-Djenkins.install.runSetupWizard=false
    networks:
      - jenkins-network

  jenkins-agent:
    image: jenkins/ssh-agent:latest
    container_name: jenkins-agent
    environment:
      - JENKINS_AGENT_SSH_PUBKEY=your-public-key-here
    networks:
      - jenkins-network

volumes:
  jenkins_data:

networks:
  jenkins-network:
    driver: bridge
```

### GitLab CI/CD配置
```yaml
# .gitlab-ci.yml
stages:
  - build
  - test
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"

services:
  - docker:20.10.16-dind

before_script:
  - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY

build:
  stage: build
  script:
    - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA .
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA

test:
  stage: test
  script:
    - docker run --rm $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA npm test

deploy:
  stage: deploy
  script:
    - docker pull $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
    - docker tag $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA $CI_REGISTRY_IMAGE:latest
    - docker-compose -f docker-compose.prod.yml up -d
  only:
    - main
```

### GitHub Actions工作流
```yaml
# .github/workflows/docker.yml
name: Docker Build and Deploy

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Login to DockerHub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}

    - name: Build and push
      uses: docker/build-push-action@v3
      with:
        context: .
        push: true
        tags: |
          user/app:latest
          user/app:${{ github.sha }}

    - name: Deploy to staging
      if: github.ref == 'refs/heads/main'
      run: |
        echo "Deploying to staging environment"
        # 部署脚本
```

---

## 生产环境最佳实践

### 安全最佳实践

#### 1. 使用非root用户
```dockerfile
# 创建非特权用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 设置文件权限
COPY --chown=appuser:appuser . /app

# 切换到非root用户
USER appuser
```

#### 2. 最小化镜像
```dockerfile
# 使用多阶段构建
FROM node:16-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:16-alpine AS runtime
RUN apk add --no-cache dumb-init
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .

# 使用dumb-init作为PID 1
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "server.js"]
```

#### 3. 扫描漏洞
```bash
# 使用Trivy扫描镜像
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy image myapp:latest

# 使用Clair扫描
docker run -d --name clair-db arminc/clair-db:latest
docker run -p 6060:6060 --link clair-db:postgres -d --name clair arminc/clair-local-scan:latest
```

#### 4. 密钥管理
```yaml
# 使用Docker Secrets
version: '3.8'
services:
  app:
    image: myapp:latest
    secrets:
      - db_password
      - api_key
    environment:
      - DB_PASSWORD_FILE=/run/secrets/db_password

secrets:
  db_password:
    file: ./secrets/db_password.txt
  api_key:
    external: true
```

### 性能优化

#### 1. 镜像优化
```dockerfile
# 使用.dockerignore
# .dockerignore
node_modules
npm-debug.log
.git
.gitignore
README.md
.env
.nyc_output
coverage
.nyc_output

# 合并RUN指令
RUN apt-get update && \
    apt-get install -y \
        curl \
        vim \
        git && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 使用缓存挂载
RUN --mount=type=cache,target=/var/cache/apt \
    apt-get update && apt-get install -y git
```

#### 2. 资源限制
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    image: myapp:latest
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
```

#### 3. 健康检查
```dockerfile
# Dockerfile中的健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1
```

```yaml
# docker-compose.yml中的健康检查
services:
  app:
    image: myapp:latest
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

### 日志管理

#### 1. 结构化日志
```dockerfile
# 配置日志驱动
FROM node:16-alpine
# 应用代码...
CMD ["node", "server.js"]
```

```bash
# 运行时配置日志驱动
docker run -d \
  --log-driver=json-file \
  --log-opt max-size=10m \
  --log-opt max-file=3 \
  myapp:latest
```

#### 2. 集中化日志
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    image: myapp:latest
    logging:
      driver: "fluentd"
      options:
        fluentd-address: localhost:24224
        tag: myapp.logs

  fluentd:
    image: fluent/fluentd:v1.12-debian-1
    ports:
      - "24224:24224"
    volumes:
      - ./fluentd/conf:/fluentd/etc
      - fluentd-log:/var/log
```

### 备份和恢复

#### 1. 数据卷备份
```bash
# 备份数据卷
docker run --rm \
  -v myapp_data:/data \
  -v $(pwd):/backup \
  alpine tar czf /backup/backup.tar.gz -C /data .

# 恢复数据卷
docker run --rm \
  -v myapp_data:/data \
  -v $(pwd):/backup \
  alpine tar xzf /backup/backup.tar.gz -C /data
```

#### 2. 数据库备份
```bash
# PostgreSQL备份
docker exec postgres pg_dump -U postgres mydb > backup.sql

# MySQL备份
docker exec mysql mysqldump -u root -p mydb > backup.sql

# MongoDB备份
docker exec mongo mongodump --db mydb --out /backup
```

---

## 故障排除

### 常见问题诊断

#### 1. 容器启动失败
```bash
# 查看容器日志
docker logs container_name

# 查看详细信息
docker inspect container_name

# 进入容器调试
docker run -it --entrypoint /bin/sh image_name

# 查看容器进程
docker exec container_name ps aux
```

#### 2. 网络连接问题
```bash
# 测试容器间连通性
docker exec container1 ping container2

# 查看网络配置
docker network ls
docker network inspect network_name

# 查看端口映射
docker port container_name

# 测试端口连通性
docker exec container_name telnet host port
```

#### 3. 存储问题
```bash
# 查看磁盘使用
docker system df

# 清理未使用资源
docker system prune -a

# 查看数据卷
docker volume ls
docker volume inspect volume_name

# 查看容器文件系统变化
docker diff container_name
```

#### 4. 性能问题
```bash
# 查看容器资源使用
docker stats

# 查看容器进程
docker exec container_name top

# 查看系统调用
docker exec container_name strace -p PID

# 内存分析
docker exec container_name cat /proc/meminfo
```

### 调试技巧

#### 1. 多阶段构建调试
```dockerfile
# 调试特定阶段
FROM node:16-alpine AS debug
WORKDIR /app
COPY package*.json ./
RUN npm install
# 在这里停止构建进行调试

FROM debug AS production
COPY . .
RUN npm run build
```

```bash
# 构建到特定阶段
docker build --target debug -t myapp:debug .
```

#### 2. 容器内调试
```bash
# 安装调试工具
docker exec -it container_name sh
apk add --no-cache curl wget netcat-openbsd

# 查看环境变量
docker exec container_name env

# 查看文件系统
docker exec container_name find / -name "*.log" 2>/dev/null
```

#### 3. 日志分析
```bash
# 实时查看日志
docker logs -f container_name

# 查看特定时间段日志
docker logs --since="2023-01-01T00:00:00" container_name

# 过滤日志
docker logs container_name 2>&1 | grep ERROR
```

### 监控和告警

#### 1. 容器监控脚本
```bash
#!/bin/bash
# monitor.sh
while true; do
  # 检查容器状态
  if ! docker ps | grep -q "myapp"; then
    echo "Container myapp is not running!"
    # 发送告警
  fi

  # 检查资源使用
  CPU_USAGE=$(docker stats --no-stream --format "{{.CPUPerc}}" myapp | sed 's/%//')
  if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
    echo "High CPU usage: $CPU_USAGE%"
  fi

  sleep 60
done
```

#### 2. 健康检查脚本
```bash
#!/bin/bash
# health_check.sh
CONTAINER_NAME="myapp"
HEALTH_URL="http://localhost:3000/health"

# 检查容器是否运行
if ! docker ps | grep -q "$CONTAINER_NAME"; then
  echo "Container $CONTAINER_NAME is not running"
  exit 1
fi

# 检查健康端点
if ! curl -f "$HEALTH_URL" > /dev/null 2>&1; then
  echo "Health check failed for $CONTAINER_NAME"
  exit 1
fi

echo "Container $CONTAINER_NAME is healthy"
```

---

## 📚 学习路径建议

### 🎯 任务清单

#### 基础阶段 (1-2周)
- [ ] 理解Docker基本概念（镜像、容器、仓库）
- [ ] 安装Docker并完成Hello World
- [ ] 掌握Docker基本命令（run, ps, images, logs等）
- [ ] 学会编写简单的Dockerfile
- [ ] 熟悉Docker Compose基本用法

#### 进阶阶段 (2-3周)
- [ ] 掌握多阶段构建技巧
- [ ] 理解Docker网络和存储机制
- [ ] 学会容器编排和服务发现
- [ ] 掌握Docker安全最佳实践
- [ ] 配置容器监控和日志

#### 实战阶段 (3-4周)
- [ ] 部署完整的前后端项目
- [ ] 搭建微服务架构
- [ ] 配置ELK日志系统
- [ ] 实现CI/CD流水线
- [ ] 部署数据库集群

#### 高级阶段 (持续学习)
- [ ] 学习Kubernetes容器编排
- [ ] 掌握云原生技术栈
- [ ] 深入容器安全和合规
- [ ] 优化容器性能和资源使用
- [ ] 探索服务网格技术

### 🚀 下一步学习建议

1. **Kubernetes**: 大规模容器编排平台
   - Pod、Service、Deployment等核心概念
   - 集群管理和自动扩缩容
   - Helm包管理工具

2. **云原生技术栈**:
   - Istio服务网格
   - Prometheus + Grafana监控
   - Jaeger分布式追踪
   - Harbor镜像仓库

3. **DevOps工具链**:
   - Jenkins/GitLab CI持续集成
   - Terraform基础设施即代码
   - Ansible自动化运维

### 📖 推荐资源

- **官方文档**: [Docker官方文档](https://docs.docker.com/)
- **最佳实践**: [Docker最佳实践指南](https://docs.docker.com/develop/dev-best-practices/)
- **安全指南**: [Docker安全指南](https://docs.docker.com/engine/security/)
- **社区**: [Docker Hub](https://hub.docker.com/)

### 💼 实践项目建议

1. **电商系统**: 前端(React) + 后端(Spring Boot) + 数据库(MySQL) + 缓存(Redis)
2. **大数据平台**: Spark + Kafka + Elasticsearch + Kibana
3. **微服务博客**: API网关 + 用户服务 + 文章服务 + 评论服务
4. **监控系统**: Prometheus + Grafana + AlertManager + Node Exporter

---

## 🎉 总结

恭喜你完成了Docker完整教程！通过本教程，你已经掌握了：

### ✅ 已完成的学习内容

1. **🏗️ 基础概念**: 镜像、容器、仓库等核心概念
2. **🚀 基本操作**: 镜像和容器的管理命令
3. **📋 Dockerfile**: 如何编写和优化Dockerfile
4. **🎭 Docker Compose**: 多容器应用的编排
5. **🌐 网络和存储**: 容器间通信和数据持久化
6. **💼 实战案例**: 各种真实场景的部署方案
7. **🔒 最佳实践**: 安全、性能、监控等方面的优化
8. **🛠️ 故障排除**: 常见问题的诊断和解决方法

### 🎯 你现在可以做什么

- ✅ 容器化任何应用程序
- ✅ 搭建完整的开发环境
- ✅ 部署微服务架构
- ✅ 配置监控和日志系统
- ✅ 实现自动化部署流水线
- ✅ 解决常见的容器问题

### 🌟 继续你的容器化之旅

Docker只是容器化技术的开始，继续探索云原生技术栈，成为现代化应用部署的专家！

**记住**: 实践是最好的老师，多动手、多实验、多思考！

🚀 **Happy Dockerizing!** 🚀
