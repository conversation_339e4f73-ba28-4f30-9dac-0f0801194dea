# Spark 知识点汇总

## 一、Spark 核心概念与特性

1. 在 Spark 中，RDD 是一种弹性的分布式数据集，它容错强、可并行操作。
    
2. Spark 的核心组件包括 Driver、SparkContext、Worker 和 ClusterManager 。
    
3. Spark 支持的运行模式有 Standalone 模式、Mesos 模式、YARN 模式和Kubernetes 模式。
    
4. Spark Streaming 是用于处理实时数据的实时计算框架。
    
5. Spark SQL 是 Spark 用于处理结构化数据的模块。
    
6. Spark MLlib 是 Spark 的机器学习库。
    
7. Spark GraphX 是 Spark 的图计算框架。
    
8. RDD 的两种基本操作是转换操作和行动操作。
    
9. 窄依赖指的是子 RDD 的一个分区只依赖于某个父 RDD 中的一个分区。
    
10. 宽依赖指的是子 RDD 的每一个分区都依赖于某个父 RDD 中多个的分区。
    
11. 在 Spark 中，Stage 的划分依据是是否遇到Shuffle操作。
    
12. Spark 中的 Shuffle 过程通常发生在宽依赖的边界。
    
13. SparkContext 是 Spark 的核心，用于连接到 Spark 集群。
    
14. Spark 的部署模式中，Standalone模式适用于在本地单机上运行 Spark 应用程序。
    
15. Spark 的部署模式中，YARN模式适用于在 YARN 集群上运行 Spark 应用程序。
    
16. 在 Spark 中，map() 是一种常见的转换操作，用于对 RDD 中的每个元素应用函数。
    
17. 在 Spark 中，reduceByKey() 是一种对键值对RDD 的聚合操作。
    
18. 在 Spark 中，groupByKey() 是一种对键值对 RDD 按照键进行分组的操作。
    
19. 在 Spark 中，join() 是一种对两个键值对 RDD 按照键进行连接的操作。
    
20. 在 Spark 中，cache() 方法用于将 RDD 数据缓存到内存中。
    
21. 在 Spark 中，persist() 方法可以指定 RDD 的存储策略。
    
22. Spark 的容错机制是通过血统机制实现的。
    
23. Spark 的作业调度采用的是FIFO调度算法。
    
24. 在 Spark 中，DStream 是 Spark Streaming 对实时数据流的抽象描述。
    
25. Spark Streaming 中的窗口操作可以通过window方法实现。
    
26. Spark SQL 中的 DataFrame 是一种结构化的分布式数据集。
    
27. Spark SQL 中的 DataSet 是一种强类型的分布式数据集，提供了更丰富的类型安全操作。
    
28. 在 Spark SQL 中，使用write方法可以将 DataFrame 中的数据写入到外部存储系统。
    
29. Spark MLlib 中的 LabeledPoint 是一种用于表示监督学习数据的类。
    
30. Spark MLlib 中的 RDD 基本操作包括 map、filter、groupByKey、reduceByKey 等。
    
31. Spark GraphX 中的图由顶点和边组成。
    
32. Spark GraphX 中的顶点由顶点 ID 和属性组成。
    
33. Spark GraphX 中的边由源顶点 ID、目标顶点 ID 和属性组成。
    
34. 在 Spark GraphX 中，使用vertices方法可以获取图的顶点 RDD。
    
35. 在 Spark GraphX 中，使用edges方法可以获取图的边 RDD。
    
36. Spark MLlib 中的监督学习算法包括分类算法和回归算法。
    
37. Spark MLlib 中的无监督学习算法包括聚类算法和关联规则算法。
    
38. 在 Spark MLlib 中，使用StandardScaler类可以对数据进行标准化处理。
    
39. 在 Spark MLlib 中，使用MinMaxScaler类可以对数据进行归一化处理。
    
40. Spark MLlib 中的 ALS 是一种常用的协同过滤算法。
    
41. Spark 的配置文件是spark-defaults.conf，用于设置 Spark 的运行参数。
    
42. Spark 的日志文件默认存储在logs目录下。
    
43. 在 Spark 中，使用getPartitions方法可以查看 RDD 的分区数。
    
44. 在 Spark 中，使用repartition方法可以对 RDD 进行重分区操作。
    
45. Spark 的 SparkConf 对象用于设置 Spark 的运行时配置。
    
46. Spark 的任务调度器是TaskScheduler，负责将任务分发到各个工作节点。
    
47. Spark 的资源管理器是ResourceManager，负责分配和管理集群中的资源。
    
48. 在 Spark 中，使用count方法可以获取 RDD 的元素个数。
    
49. 在 Spark 中，使用sortBy方法可以对 RDD 中的元素进行排序操作。
    
50. Spark 的部署模式中，cluster 模式表示 Driver 运行在工作节点节点上。
    

## 二、Spark 填空题

51. Spark 是一种开源的分布式计算系统，它支持内存计算，能够高效处理大规模数据集，其核心是（**RDD**）。
    
52. RDD 具有容错性，能够自动恢复数据丢失，这主要依赖于（**血统机制**）。
    
53. Spark 的运行模式包括本地模式、Standalone 模式、（**YARN**）模式和 Mesos 模式。
    
54. Spark Streaming 是 Spark 用于处理（**实时**）数据的组件，能够实现毫秒级延迟的实时计算。
    
55. Spark SQL 是 Spark 中用于处理（**结构化**）和半结构化数据的模块。
    
56. Spark MLlib 是 Spark 的（**机器学习**）库，包含多种算法，如分类、回归、聚类等。
    
57. Spark GraphX 是 Spark 的（**图计算**）框架，适用于图数据处理。
    
58. RDD 的两种主要操作类型是（**转换**）操作和行动操作。
    
59. 窄依赖指的是子 RDD 的一个分区只依赖于父 RDD 的一个分区，而宽依赖则涉及多个父 RDD 分区，通常会引发（**Shuffle**）操作。
    
60. Spark 中的广播变量用于高效分发大型只读数据集，确保每个节点只有一份数据副本，通过（**broadcast**）方法创建。
    
61. Spark 的配置文件是（**spark-defaults.conf**），用于设置 Spark 的运行参数。
    
62. Spark 的日志文件默认存储在（**logs**）目录下。
    
63. 在 Spark 中，使用（**count**）方法可以获取 RDD 的元素个数。
    
64. 在 Spark 中，使用（**sortBy**）方法可以对 RDD 中的元素进行排序操作。
    
65. Spark 的 Driver 节点负责作业的（**调度**）和任务分发。
    
66. Spark 的作业调度采用的是（**FIFO**）调度算法，即先来先服务。
    
67. 在 Spark 中，map() 是一种常见的（**转换**）操作，用于对 RDD 中的每个元素应用函数。
    
68. 在 Spark 中，reduceByKey() 是一种对（**键值对**）RDD 的聚合操作。
    
69. 在 Spark 中，groupByKey() 是一种对键值对 RDD 按照（**键**）进行分组的操作。
    
70. 在 Spark 中，join() 是一种对两个键值对 RDD 按照（**键**）进行连接的操作。
    
71. Spark 的 SparkConf 对象用于设置 Spark 的（**运行时**）配置。
    
72. Spark 的任务调度器是（**TaskScheduler**），负责将任务分发到各个工作节点。
    
73. Spark 的资源管理器是（**ResourceManager**），负责分配和管理集群中的资源。
    
74. Spark 的部署模式中，（**cluster**）模式表示 Driver 运行在工作节点上。
    
75. Spark 的部署模式中，（**client**）模式表示 Driver 运行在客户端。
    
76. 在 Spark 中，使用（**cache**）方法可以将 RDD 数据缓存到内存中。
    
77. 在 Spark 中，使用（**persist**）方法可以指定 RDD 的存储策略。
    
78. Spark 的容错机制是通过（**血统机制**）实现的，能够自动恢复丢失的数据。
    
79. Spark 的作业是由（**Driver**）节点提交和管理的。
    
80. Spark 的执行器是在工作节点上运行的任务，负责执行具体的计算任务，称为（**Executor**）。
    
81. Spark 的核心组件包括 Driver、SparkContext、Worker 和（**ClusterManager**）。
    
82. Spark 的 SparkContext 是 Spark 的（**核心**），用于连接到 Spark 集群。
    
83. Spark 的 Worker 节点负责管理（**资源**）和运行任务。
    
84. Spark 的 ClusterManager 是集群管理器，负责整个集群的资源管理和调度。
    
85. Spark 的 DAGScheduler 是（**有向无环图**）调度器，负责将作业分解为多个阶段。
    
86. Spark 的 TaskScheduler 是任务调度器，负责将任务分发到各个（**执行器**）上运行。
    
87. Spark 的 Stage 是由多个（**任务**）组成的执行单元。
    
88. Spark 的任务是具体的计算单元，运行在（**执行器**）上。
    
89. Spark 的 Shuffle 过程是指在不同阶段之间对数据进行（**重新分区**）和重新分布的过程。
    
90. Spark 的 Shuffle 过程通常会涉及到大量的（**磁盘 I/O**）和网络传输。
    
91. Spark 的 Shuffle 写操作是将中间结果写入到（**磁盘**）的过程。
    
92. Spark 的 Shuffle 读操作是从其他节点读取 Shuffle 数据的过程。
    
93. Spark 的广播变量是一种（**只读**）变量，可以高效地分发到各个节点。
    
94. Spark 的累积变量用于聚合操作，如计数和求和，常见的类型有（**SumAccumulator**）。
    
95. Spark 的 SparkSession 是 Spark SQL 的入口点，用于创建（**DataFrame**）和执行 SQL 查询。
    
96. Spark 的 DataFrame 是一种结构化的分布式数据集，提供了（**关系型**）接口。
    
97. Spark 的 DataSet 是一种强类型的分布式数据集，提供了更丰富的（**类型安全**）操作。
    
98. Spark 的 MLlib 中的 LabeledPoint 是一种用于表示（**监督学习**）数据的类。
    
99. Spark 的 MLlib 中的监督学习算法包括分类算法和（**回归**）算法。
    
100. Spark 的 MLlib 中的无监督学习算法包括聚类算法和（**关联规则**）算法。
    

## 三、Spark 判断题

101. Spark 只能运行在 YARN 集群管理模式下。（**×**）
    
102. Spark 的 RDD 支持容错机制，能够自动恢复数据。（**√**）
    
103. Spark Streaming 可以处理实时数据流，支持毫秒级延迟的实时计算。（**√**）
    
104. Spark SQL 不支持标准 SQL 查询。（**×**）
    
105. Spark MLlib 的 ALS 算法只能用于协同过滤场景。（**√**）
    
106. Spark 的 Driver 节点负责作业的调度和任务分发。（**√**）
    
107. 在 Spark 中，reduceByKey 是一种行动操作。（**×**）
    
108. Spark 的默认调度模式是 FIFO，即先来先服务。（**√**）
    
109. Spark GraphX 无法处理图数据中的顶点和边属性。（**×**）
    
110. Spark 的配置参数只能通过配置文件进行设置。（**×**）
    
111. Spark 的广播变量可以修改其内容。（**×**）
    
112. Spark 的累积变量用于累加操作，只能进行加法运算。（**×**）
    
113. Spark 的 SparkSession 是 Spark SQL 的入口点，用于创建 DataFrame 和执行 SQL 查询。（**√**）
    
114. Spark 的 DataFrame 是一种结构化的分布式数据集，提供了关系型接口。（**√**）
    
115. Spark 的 DataSet 是一种弱类型的分布式数据集。（**×**）
    
116. Spark 的 MLlib 中的 LabeledPoint 是一种用于表示无监督学习数据的类。（**×**）
    
117. Spark 的 MLlib 中的监督学习算法包括分类算法和聚类算法。（**×**）
    
118. Spark 的 MLlib 中的无监督学习算法包括关联规则算法和回归算法。（**×**）
    
119. Spark 的 SparkContext 是 Spark 的核心，用于连接到 Spark 集群。（**√**）
    
120. Spark 的 Worker 节点负责管理资源和运行任务。（**√**）
    
121. Spark 的 ClusterManager 是集群管理器，负责整个集群的资源管理和调度。（**√**）
    
122. Spark 的 DAGScheduler 是有向无环图调度器，负责将作业分解为多个阶段。（**√**）
    
123. Spark 的 TaskScheduler 是任务调度器，负责将任务分发到各个执行器上运行。（**√**）
    
124. Spark 的 Stage 是由多个任务组成的执行单元。（**√**）
    
125. Spark 的任务是具体的计算单元，运行在执行器上。（**√**）
    
126. Spark 的 Shuffle 过程是指在不同阶段之间对数据进行重新分区和重新分布的过程。（**√**）
    
127. Spark 的 Shuffle 过程通常会涉及到大量的磁盘 I/O 和网络传输。（**√**）
    
128. Spark 的 Shuffle 写操作是将中间结果写入到磁盘的过程。（**√**）
    
129. Spark 的 Shuffle 读操作是从其他节点读取 Shuffle 数据的过程。（**√**）
    
130. Spark 的广播变量是一种只读变量，可以高效地分发到各个节点。（**√**）
    
131. Spark 的累积变量用于聚合操作，如计数和求和。（**√**）
    
132. Spark 的 SparkSession 是 Spark SQL 的入口点，用于创建 DataFrame 和执行 SQL 查询。（**√**）
    
133. Spark 的 DataFrame 是一种结构化的分布式数据集，提供了关系型接口。（**√**）
    
134. Spark 的 DataSet 是一种强类型的分布式数据集，提供了更丰富的类型安全操作。（**√**）
    
135. Spark 的 MLlib 中的监督学习算法包括分类算法和回归算法。（**√**）
    
136. Spark 的 MLlib 中的无监督学习算法包括聚类算法和关联规则算法。（**√**）
    
137. Spark 的配置文件是 spark-defaults.conf，用于设置 Spark 的运行参数。（**√**）
    
138. Spark 的日志文件默认存储在 logs 目录下。（**√**）
    
139. 在 Spark 中，使用 count 方法可以获取 RDD 的元素个数。（**√**）
    
140. 在 Spark 中，使用 sortBy 方法可以对 RDD 中的元素进行排序操作。（**√**）
    
141. Spark 的 Driver 节点负责作业的调度和任务分发。（**√**）
    
142. Spark 的作业调度采用的是 FIFO 调度算法，即先来先服务。（**√**）
    
143. 在 Spark 中，map 是一种常见的转换操作，用于对 RDD 中的每个元素应用函数。（**√**）
    
144. 在 Spark 中，reduceByKey 是一种对键值对 RDD 的聚合操作。（**√**）
    
145. 在 Spark 中，groupByKey 是一种对键值对 RDD 按照键进行分组的操作。（**√**）
    
146. 在 Spark 中，join 是一种对两个键值对 RDD 按照键进行连接的操作。（**√**）
    
147. Spark 的 SparkConf 对象用于设置 Spark 的运行时配置。（**√**）
    
148. Spark 的任务调度器是 TaskScheduler，负责将任务分发到各个工作节点。（**√**）
    
149. Spark 的资源管理器是 ResourceManager，负责分配和管理集群中的资源。（**√**）
    
150. Spark 的部署模式中，cluster 模式表示 Driver 运行在工作节点上。（**√**）