# 项目1 初识动态网页数据采集与预处理

## 1.单选题

(1) 根据数据结构的不同，可将数据类型分为（ ）种。
A. 1
B. 2
**C. 3**
D. 4

(2) 下列不属于网络爬虫的是（ ）。
A. 聚焦网络爬虫
B. 深层页面爬虫
**C. 增程式网络爬虫**
D. 通用网络爬虫

(3) 以下不属于数据采集常用方法的是（ ）。
A. 日志数据采集
B. 网络爬虫采集
C. 商业工具采集
**D. 设备数据采集**

(4) 以下不属于数据规约方式的是（ ）。
**A. 类型规约**
B. 维数规约
C. 数量规约
D. 数据压缩

(5) PyCharm有（ ）个版本。
A. 1
**B. 2**
C. 3
D. 4

6、网络爬虫又称为？
A) 网络蜘蛛
B) 网络机器人
**C) 以上都是**
D) 以上都不是

7、 以下哪个不是网络爬虫的应用场景？
A) 搜索引擎
B) 舆情分析与监测
C) 聚合平台
**D) 数据加密**

8、HTTP协议中的URL代表什么？
**A) 统一资源定位符**
B) 用户代理
C) 主机地址
D) 请求方法

9、Robots协议的作用是什么？
A) 提供网站内容
**B) 保护网站数据和敏感信息**
C) 加快网页加载速度
D) 增加网站访问量

10、通用网络爬虫与聚焦网络爬虫的主要区别是？
**A) 通用网络爬虫抓取所有网页，聚焦网络爬虫只抓取特定主题网页**
B) 通用网络爬虫只抓取静态网页，聚焦网络爬虫只抓取动态网页
C) 通用网络爬虫使用Python实现，聚焦网络爬虫使用Java实现
D) 通用网络爬虫不遵守Robots协议，聚焦网络爬虫遵守

11、HTTP请求格式包括哪几个部分？
**A) 请求行、请求头部、空行、请求体**
B) 请求行、响应头、空行、响应体
C) 请求行、请求参数、空行、响应体
D) 请求行、请求头部、请求体、响应体

12、使用Python实现网络爬虫的优势不包括？
A) 语法简洁
B) 开发效率高
**C) 强大的网页解析能力**
D) 丰富的内置模块和第三方库

13、Fiddler工具在网络爬虫中的作用是什么？
A) 用于数据库管理
**B) 记录与Web服务器之间的所有HTTP请求并支持截获、重发等操作**
C) 用于前端页面设计
D) 用于提高网站安全性

14、静态网页与动态网页的主要区别在于？
**A) 静态网页内容固定不变，动态网页可以根据用户输入显示不同内容**
B) 静态网页使用HTML编写，动态网页使用CSS编写
C) 静态网页可以交互，动态网页不可以
D) 静态网页需要数据库支持，动态网页不需要

15、以下哪种方法不是应对防爬虫措施的有效策略？
A) 添加User-Agent字段
B) 降低访问频率
C) 设置代理服务
**D) 删除cookies**

16、在使用requests库发送GET请求时，如何正确传递查询参数？
A. response = requests.get(url, query={'key': 'value'})
**B. response = requests.get(url, params={'key': 'value'})**
C. response = requests.get(url, data={'key': 'value'})
D. response = requests.get(url, args={'key': 'value'})

17、以下哪种方式可以用来设置自定义的HTTP头信息？
**A. headers = {'User-Agent': 'my-app/0.0.1'}; response = requests.get(url, headers=headers)** (注：答案基于题库提供的选项B，但实际正确的requests用法是选项A，此处按题库答案标记B，但提供A作为正确代码示例)
B. headers = {'User-Agent': 'my-app/0.0.1'}; response = requests.get(url, header=headers)
C. headers = {'User-Agent': 'my-app/0.0.1'}; response = requests.get(url, custom_headers=headers)
D. headers = {'User-Agent': 'my-app/0.0.1'}; response = requests.get(url, user_agent=headers)
(*根据题库答案，第17题选B，但常见的requests用法如A所示。此处按题库答案B标记，但实际A更符合标准用法。*)

18、如何检查响应状态码是否表示成功（即状态码小于400）？
A. if response.status_code < 400: print("请求成功")
**B. if response.ok: print("请求成功")**
C. if response.status == "success": print("请求成功")
D. if response.code < 400: print("请求成功")

19、如何使用requests库发送一个HEAD请求来获取响应头信息而不获取响应体？
**A. response = requests.head(url)**
B. response = requests.get(url, method='HEAD')
C. response = requests.request('HEAD', url)
D. response = requests.options(url)

20、如何使用requests库设置代理服务器进行请求？
**A. proxies = {'http': 'http://10.10.1.10:3128', 'https': 'http://10.10.1.10:1080'}; response = requests.get(url, proxies=proxies)**
B. proxies = {'http': 'http://10.10.1.10:3128', 'https': 'http://10.10.1.10:1080'}; response = requests.get(url, proxy=proxies)
C. proxies = {'http': 'http://10.10.1.10:3128', 'https': 'http://10.10.1.10:1080'}; response = requests.get(url, use_proxy=proxies)
D. proxies = {'http': 'http://10.10.1.10:3128', 'https': 'http://10.10.1.10:1080'}; response = requests.get(url, set_proxy=proxies)

21、如何使用requests库获取二进制响应内容（例如图片）？
A. response = requests.get(url); binary_data = response.text
**B. response = requests.get(url); binary_data = response.content**
C. response = requests.get(url); binary_data = response.body
D. response = requests.get(url); binary_data = response.raw

22、如何使用requests库发送一个DELETE请求？
**A. response = requests.delete(url)**
B. response = requests.remove(url)
C. response = requests.request('DELETE', url)
D. response = requests.post(url, method='DELETE')

23、如何使用requests库发送一个带有Cookie信息的GET请求？
**A. cookies = {'session_id': '123456789'}; response = requests.get(url, cookies=cookies)**
B. cookies = {'session_id': '123456789'}; response = requests.get(url, cookie=cookies)
C. cookies = {'session_id': '123456789'}; response = requests.get(url, with_cookies=cookies)
D. cookies = {'session_id': '123456789'}; response = requests.get(url, include_cookies=cookies)

24、如何使用requests库禁用SSL证书验证？
**A. response = requests.get(url, verify=False)**
B. response = requests.get(url, ssl_verify=False)
C. response = requests.get(url, disable_ssl=True)
D. response = requests.get(url, no_verify=True)

25、如何检查响应内容的实际编码格式？
A. print(response.encoding)
**B. print(response.apparent_encoding)**
C. print(response.headers['Content-Encoding'])
D. print(response.charset)

26、下列关于数据来源的描述中，哪一项属于非结构化数据？（ ）
A) 信息管理系统中的二维表数据
B) JSON格式的半结构化数据
**C) 邮件、图片、视频等数据**
D) 物联网传感器采集的结构化数据

27、网络爬虫的基本流程中，模拟浏览器发送请求后，下一步操作是（ ）。
A) 解析网页并提取资源
B) 存储资源或发送请求
**C) 获取网页代码**
D) 接收响应

28、以下哪种工具主要用于数据预处理中的日志数据存储和分析？（ ）
A) Elasticsearch
**B) Logstash**
C) Kibana
D) Pig

29、PyCharm的快捷键中，用于运行当前文件的快捷键是（ ）。
**A) Ctrl+Shift+F10**
B) Shift+F10
C) Alt+Shift+F10
D) F5

30、以下哪种数据预处理方式用于精简数据存储成本？（ ）
A) 数据清洗
B) 数据集成
**C) 数据归约**
D) 数据转换

## 2.多选题

1、以下属于网络爬虫用途的有（ ）。
**A) 搜索引擎数据获取**
**B) 电商平台商品价格比较**
C) 人工电话调查
**D) 数据统计分析**

2、数据预处理的四个核心步骤包括（ ）。
**A) 数据清洗**
**B) 数据集成**
**C) 数据变换**
**D) 数据归约**

3、以下属于PyCharm功能特点的有（ ）。
**A) 支持Django框架**
**B) 集成版本控制**
C) 图形页面调试器
**D) 自动完成Python代码**

4、网络爬虫的类型包括（ ）。
**A) 通用网络爬虫**
**B) 增量式网络爬虫**
**C) 聚焦网络爬虫**
**D) 深层页面爬虫**

5、以下属于数据预处理工具的有（ ）。
**A) Pandas**
**B) Pig**
**C) ELK**
**D) Scrapy**

## 3.填空题

1、数据预处理的四个步骤分别是数据清洗、数据集成、**数据变换**和数据归约。
2、PyCharm中用于格式化代码的快捷键是**Ctrl+Alt+L**。
3、结构化数据通常以**二维表**形式存储。
4、网络爬虫的**增量式**类型用于爬取动态更新的网页内容。
5、ELK工具中用于日志搜索和分析的组件是**Elasticsearch**。

## 4.判断题

(1) 大数据最早由维克托和肯尼斯提出，被称为巨量资料，是一种海量的、飞速增长的、多样化的信息资产。（**√**）
(2) 由于数据规模的不断扩大以及数据缺少、重复、错误等问题的出现，往往需要将整个流程约70%的时间花费在数据处理上。（**×**）
(3) JS埋点采集日志通过在页面外植入JS代码实现日志数据的采集。（**×**）
(4) 数据清洗是发现并纠正数据中可识别错误的一种方式。（**√**）
(5) 社区版的PyCharm需要付费使用。（**×**）
6、非结构化数据可以通过二维表逻辑表示。（**×**）
7、Requests库是Python内置的标准库。（**×**）
8、数据清洗包括处理缺失值和逻辑错误。（**√**）
9、Pandas的DataFrame结构只能处理二维数据。（**×**）
10、Flume主要用于日志数据的实时采集和传输。（**√**）

## 5.简答题

(1) 简述网络爬虫的基本流程。
**答案：** **网络爬虫的基本流程主要包括以下几个步骤：首先，选定一个或多个起始URL作为入口点；其次，爬虫访问这些URL，并解析网页内容；然后，根据特定规则提取出感兴趣的信息和链接；接下来，将新发现的链接加入待抓取队列；最后，循环上述过程直到满足停止条件，比如达到预定的数量限制或者遍历完所有相关链接。**

(2) 简述PyCharm的优势。
**答案：** **PyCharm是一款专门针对Python语言开发的强大IDE，具有多项优势：其提供了智能代码补全、代码检查和快速修复功能，极大地提高了编码效率；内置了丰富的调试工具，支持断点调试、表达式求值等功能，便于开发者排查问题；集成了版本控制系统，如Git、SVN等，方便团队协作开发；此外，还支持多种Web技术框架，如Django、Flask等，使得Web开发更加便捷。同时，PyCharm拥有直观友好的用户界面，增强了用户体验。**

3、简述数据预处理中数据清洗的主要任务。
**答案：** **数据清洗的主要任务包括：处理缺失值（删除或填充）、修正格式错误（统一书写规范）、消除逻辑错误（删除重复或异常数据）、剔除非需求数据（无关字段）。**

4、说明网络爬虫与日志数据采集的区别。
**答案：** **网络爬虫通过模拟浏览器抓取公开网页数据，而日志采集依赖埋点或Flume等工具收集用户行为数据，两者应用场景和数据源不同。**

5、列举PyCharm的三种核心功能。
**答案：** **PyCharm的核心功能包括：代码自动完成、集成Django开发环境、版本控制支持（如Git）、图形化调试器。** (注：题库答案为此，但通常集成开发环境和调试器是一体的)

6、描述结构化数据与非结构化数据的主要特点。
**答案：** **结构化数据（如数据库表）具有固定格式和字段，非结构化数据（如文本、图片）无固定模式，需特殊处理。**

7、简述数据归约的意义及常用方法。
**答案：** **数据归约通过减少数据量降低成本和分析时间，常用方法包括维度归约（PCA）、数值归约（抽样）和数据压缩。**

## 6.综合题

1、结合实际案例，分析动态网页数据采集中Scrapy框架的优势及适用场景。
**答案：** **Scrapy框架优势：异步处理高并发、内置中间件和管道机制、适合大规模爬取。适用场景如电商商品数据采集、新闻网站更新监控。**

2、设计一个基于ELK工具的日志数据预处理流程，并说明各组件的作用。
**答案：** **流程：Flume采集日志→Logstash过滤和格式化→Elasticsearch存储和搜索→Kibana可视化。各组件分别负责采集、处理、存储和展示。**

3、假设需要搭建一个网页数据采集环境，请列出所需的工具及其主要功能。
**答案：** **工具及功能：PyCharm（开发环境）、Requests/Urllib（数据采集）、BeautifulSoup/Xpath（解析）、Pandas（预处理）、MongoDB（存储）。**

4、某电商网站需采集商品评论数据，请设计一个完整的数据采集与预处理方案。
**答案：** **方案：使用Selenium模拟登录→Scrapy爬取评论→Pandas清洗数据→存储至MySQL。需处理反爬虫和数据去重。**

5、分析Pandas在数据预处理中的核心作用，并举例说明其典型应用场景。
**答案：** **Pandas核心作用：提供DataFrame结构处理表格数据，支持缺失值填充（fillna）、数据筛选（query）、聚合分析（groupby）。典型场景如销售数据分析。**