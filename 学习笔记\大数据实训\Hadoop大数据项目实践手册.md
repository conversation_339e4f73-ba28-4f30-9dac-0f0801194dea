
### 项目整体目标：
掌握从环境搭建、数据导入、MapReduce预处理、Hive数仓建设与HQL分析、Spark/SparkSQL分析与挖掘，到最终可视化与成果汇报的全流程大数据项目实践。

### 前提条件：环境搭建 (文档第 5 节)
在执行具体任务前，需完成环境搭建：
1.  **虚拟机与域名解析**：配置 master, slave1, slave2, slave3 虚拟机的 `/etc/hosts` 及物理机 hosts 文件，实现域名互通。
2.  **Hadoop集群**：
    *   安装指定版本的Linux, JDK, VMware, Hadoop, IDEA, SSH工具。
    *   配置主从架构 (Master: NameNode, ResourceManager, SecondaryNameNode, JobHistoryServer; Slaves: DataNode, NodeManager)。
    *   配置 `HADOOP_HOME`等环境变量。
    *   掌握启动/停止集群命令 (`start-all.sh`, `start historyserver`, `stop-all.sh` 等)。
    *   知晓 Web UI 监控端口 (NN:9870, RM:8088, History:19888)。
3.  **Hive引擎环境**：
    *   安装 Hive, MySQL(on master), MySQL驱动, DBeaver。
    *   配置远程 Metastore 模式 (Metastore & HiveServer2 运行在 slave2)。
    *   配置 `HIVE_HOME`, warehouse 目录。
     *  掌握启动/停止 MySQL, Metastore, HiveServer2 服务命令。
    *   使用 `beeline` 和 DBeaver 工具测试连接 HiveServer2 ( `*************************` )。
4.  **Spark集群环境**：
     *  安装 Spark。
    *   配置分布式集群 (Master: Master, HistoryServer, ThriftService; Slaves: Worker)。 Hive Metastore/HiveServer2 on slave2。
    *   掌握启动/停止 Spark 集群、HistoryServer、ThriftService 命令。
     *   知晓 Web UI 端口，并使用 DBeaver 测试连接 Spark Thrift Server (例如 `jdbc:hive2://master:10016`)。
5. **Maven环境**:
    * 检查/安装JDK。
	* 下载解压Maven。
	* 配置 `MAVEN_HOME` 环境变量并添加到 `PATH`。
	* 使用 `mvn -version` 验证。
 
---
### 项目任务详细步骤：

以下基于文档第 6 节内容：

**6.1 任务 1：使用 hdfs 命令导入数据集**
*   **目标要点**：理解HDFS架构；熟练使用 `hdfs dfs -put`, `-ls`, `-mkdir`, `-tail` 等命令；规划HDFS目录。
*   **需求说明**：将 `vehicle_type.csv`, `vehicle_trails.csv`, `toll.csv`, `service.csv`, `section.csv`, `gantry.csv` 数据集导入 HDFS。
*   **实现步骤**：
    1.  **创建本地目录**：在 master 节点创建本地目录 `/root/highway_data`，并将所有 csv 文件上传至此。使用 `ls -lrt` 查看。
    2.  **创建HDFS目录**：执行 `hdfs dfs -mkdir -p /user/hadoop/highway_data`。
    3.  **上传数据集**：从本地 `/root/highway_data/` 目录，使用 `hdfs dfs -put <file_name>.csv /user/hadoop/highway_data/` 命令将各 csv 文件上传至 HDFS。
    4.  **验证导入**：执行 `hdfs dfs -ls /user/hadoop/highway_data/` 查看文件列表。
    5.  **数据检查**：执行 `hdfs dfs -tail /user/hadoop/highway_data/gantry.csv` 抽查文件末尾内容，验证格式和完整性。

**6.2 任务 2：使用 MR 程序统计无效车牌数量**
*   **目标要点**：理解MapReduce模型；Java编写MR程序；使用正则表达式；统计无效车牌次数。
*   **需求说明**：输入 `vehicle_trails.csv` 的 `vlp` (车牌号) 字段，统计非法、空车牌等无效车牌的数据总数。
*   **实现步骤**：
    1.  **创建并配置工程**：
        *   在 IntelliJ IDEA 创建 Maven 项目 `hadoop-highway`。
        *   在 Project Structure -> Libraries 中导入 Hadoop 安装目录 `/share/hadoop` 下的所有 JAR 包。
        *    修改 `pom.xml` 添加 `log4j`, `slf4j-api`, `commons-logging` 等日志依赖。
        *   将集群的 `core-site.xml`, `hdfs-site.xml`, `mapred-site.xml`, `yarn-site.xml` 和 `log4j.properties` 复制到项目的 `resources` 目录。
		*   配置本地 Windows 的 `HADOOP_HOME` 环境变量，添加 `winutils.exe` 和 `hadoop.dll` 以支持本地提交。
    2.  **Mapper 模块开发**：读取每行数据，提取`vlp`字段，使用给定的正则表达式 `^(WJ)?[京津沪...]...` 匹配 *合法* 车牌，识别并过滤出 *不匹配* 的无效车牌，输出 `<无效车牌, 1>`。
    3.  **Reducer 模块开发**：对Mapper输出的相同无效车牌的 Value(1) 进行汇总求和 `count`，输出 `<无效车牌, count>`。
    4.  **Driver & IDEA 运行**：
        *   编写驱动类，继承 `Configured` 并实现 `Tool` 接口。
        *   在 `run` 方法中配置 MapReduce 作业（输入输出路径、Mapper/Reducer类等）。
        *   在 `main` 方法中调用 `ToolRunner.run()`。
        *   在 IDEA 中直接运行，程序打包并提交到集群。
	 5. **验证结果**: 查看IDEA控制台日志和HDFS输出文件内容。

**6.3 任务 3：使用 Hive 进行数据表创建和数据初始化**
*   **目标要点**：Hive基本操作(建库、建表、导数据)；定义字段与类型，添加注释；`LOAD DATA`；理解外部表。
*   **需求说明**：在Hive中创建对应的数据表结构，表前缀 `ods_gsgl_`；`vehicle_trails` 数据量大，建为外部表 (`EXTERNAL`) 并指定 `LOCATION`；将HDFS数据加载到Hive表，并验证。
*   **实现步骤**：
    1.  **启动并连接Hive**：确保HiveServer2和Metastore运行，使用 DBeaver 连接 `slave2:10000`，创建SQL编辑器。
    2.  **创建数据库**：`CREATE DATABASE IF NOT EXISTS highway; USE highway;`
    3.  **创建各表**：
        *   对 `section`, `vehicle_type`, `toll`, `service`, `gantry`：使用 `CREATE TABLE ods_gsgl_xxx (...) COMMENT '...' ROW FORMAT DELIMITED FIELDS TERMINATED BY ',' STORED AS TEXTFILE;` 创建管理表，每个字段后加 `COMMENT`。
        *   对 `vehicle_trails`：使用 `CREATE EXTERNAL TABLE ods_gsgl_vehicle_trails (...) ... LOCATION '/user/hadoop/highway_data/ods_gsgl_vehicle_trails';` 创建外部表。
        *  检查HDFS上对应目录是否创建成功。
    4.  **加载数据**：执行系列 `LOAD DATA INPATH '/user/hadoop/highway_data/<file_name>.csv' INTO TABLE ods_gsgl_xxx;` 命令。注意：此命令会将HDFS源路径文件 *移动* 到表的 `LOCATION` （外部表）或 warehouse 目录（管理表）。
    5.  **查询验证**：执行 `SELECT COUNT(*) FROM ods_gsgl_vehicle_trails;` 和 `SELECT ... FROM ods_gsgl_section t LIMIT 10;` 等语句验证数据导入正确性。

**6.4 任务 4：使用 HQL 进行高速公路车流量统计**
*   **目标要点**：HiveQL数据清洗(过滤、标准化、去重)；窗口函数(`ROW_NUMBER`, `LAG`)；多表 `JOIN`；时间维度聚合。
*   **需求说明**：
    1. 数据预处理：清洗数据写入 `std_gsgl_vehicle_trails`（过滤无效车牌、格式标准化如`trim`、`replace`、时间戳统一`yyyy-MM-dd HH:mm:ss`、代码转名称；去重：1分钟内同车同方向同门架保留最早记录），重复数据写入 `std_gsgl_vehicle_trails_repeat`。
    2. 车流量统计：基于 `std_` 表，统计路线级/路段级总流量、路线级/路段级每小时流量，结果写入 `dm_gsgl_..._flow` 和 `dm_gsgl_..._flow_hour` 系列表。
*   **实现步骤**：
    1.  **数据预处理**：
        *   过滤无效车牌： `WHERE NOT (vlp REGEXP '...') OR vlp IS NULL OR vlp IN (...)`。
        *   格式标准化：使用 `replace(col, '"','')`, `trim()`, `from_unixtime(unix_timestamp(...,'yyyy/MM/dd HH:mm:ss'),'yyyy-MM-dd HH:mm:ss')`, `CASE WHEN...END` (如 `crossing_type` 转名称)。
        *   去重处理：使用 `LAG(capture_time, 1, capture_time) OVER (PARTITION BY vlp, gantry_code, direction ORDER BY capture_time)` 获取上一条记录时间，计算时间差秒数，筛选时间差 > 60秒 或 第一条记录，存入 `std_gsgl_vehicle_trails`；将重复记录存入 `_repeat` 表。同时清洗维表 `std_gsgl_gantry`，`std_gsgl_section`。
    2.  **路线级车流量统计**：关联 `std_gsgl_vehicle_trails`、 `_gantry`、 `_section` 表，按 `road_code/name` 分组聚合统计，输出至 `dm_gsgl_road_total_flow`。
    3.  **路段级车流量统计**：在步骤2基础上，按 `section_code/name` 分组聚合统计，输出至 `dm_gsgl_section_total_flow`。
    4.  **车流量趋势分析**：在步骤2和3基础上，提取 `capture_time` 的小时部分，增加按小时 `GROUP BY`，输出至 `..._flow_hour` 表。

**6.5 任务 5：使用 HQL 进行高速公路拥堵分析**
*   **目标要点**：HQL清洗转换聚合；窗口/时间/条件函数；计算平均速度、拥堵指数(TI)；数据质量控制；识别高峰和热点。
*   **需求说明**：计算相邻门架平均速度(km/h)；计算拥堵指数TI (`TI = (Vmax-Vavg)/Vmax * 100 + Qactual/Qcapacity * 100`，Vmax=120, Qcapacity=1500)；识别高峰时段和门架级拥堵热点。
*   **实现步骤**：
    1.  **门架桩号格式标准化**：修改 `std_gsgl_gantry` 清洗逻辑，用正则 `CASE WHEN pile_no REGEXP ...` 和 `SUBSTRING`, `LOCATE` 函数将 `K123+456` 格式转换为数值型公里数 `123.456` (如 `pile_no_km`)。
    2.  **车辆轨迹排序**：从 `std_gsgl_vehicle_trails` 关联 `std_gsgl_gantry`(获取`pile_no_km`)，过滤门架类型，使用 `ROW_NUMBER() OVER (PARTITION BY vlp, direction ORDER BY capture_time) as rn` 为每辆车轨迹点排序。结果存入 `dm_gsgl_ranked_trails`。
    3.  **平均速度计算**：
        *   对 `dm_gsgl_ranked_trails` 进行自连接 (如 `t1 JOIN t2 on t1.vlp=t2.vlp and t1.direction=t2.direction and t2.rn = t1.rn + 1`) 获取相邻记录。
        *   计算时间差(秒) `time_seconds = unix_timestamp(t2.time) - unix_timestamp(t1.time)` 和距离差(公里) `distance_km = ABS(t2.pile_no_km - t1.pile_no_km)`。
        *   计算速度：` (distance_km / NULLIF(time_seconds,0) ) * 3600`。
        *   排除无效数据：`time_seconds > 0` 且速度在合理范围 (如 10~200 km/h)。
        *   结果存入 `dm_gsgl_speed_trails`。
    4.  **拥堵指数计算**：
        *   关联 `std_gsgl_vehicle_trails`(按小时提取时间桶), `_gantry`, `_section`，并 `LEFT JOIN dm_gsgl_speed_trails` (匹配起点门架)。
        *   按时间桶、道路、路段、方向 `GROUP BY`。
        *   计算指标：`traffic_volume = COUNT(DISTINCT vlp)`，`avg_speed = AVG(avg_speed_kmph)`。
        *   按公式 `(120 - avg_speed) / 120 * 100 + (traffic_volume / 1500.0) * 100` 计算 `congestion_index`。
        *   结果存入 `dm_gsgl_congestion_index`。
    5. **高峰时段识别**: 基于步骤4的结果按小时、路段统计，按TI排序找出最高时段。结果存入 `dm_gsgl_hourly_congestion`。
    6. **门架级拥堵热点识别**: 统计各门架的车流量和平均速度，计算门架的平均TI，按TI降序排列（如TOP 10）。结果存入 `dm_gsgl_gantry_congestion_index`。

**6.6 任务 6：使用 Spark 进行高速公路车型差异分析**
*   **目标要点**：Spark Core 读取HDFS；解析内容提取字段；车型编码映射中文名；按车型统计数量；输出到控制台和HDFS。
*   **需求说明**：输入HDFS的 `std_gsgl_vehicle_trails` 文件目录（注意分隔符 `\001`），根据 `identify_vtype` 字段，按车型统计通行数量，输出到HDFS路径 `/user/hadoop/highway_data/VehicleTypeCount`。
*   **实现步骤**：
    1.  **创建并配置工程**：
        *   在 IDEA 中安装 Scala 插件。
        *   创建 Scala IDEA 项目 `spark-highway`，选择 JDK 和 Scala SDK。
        *   在 Project Structure -> Libraries 中导入 Spark 安装目录 `/jars` 下的所有 JAR 包。
    2.  **创建 Object 编写程序**：
        *   创建包 `com.highway.scala` 和 Scala Object `VehicleTypeCount`。
        *   参考 WordCount 示例：创建 `SparkConf` 和 `SparkContext`。
        *   `sc.textFile()` 读取输入路径。
        *   `flatMap` 或 `map` 解析行（根据 `\001` 分隔符），提取 `identify_vtype` 字段。
        *   `map` 将车型编码映射为中文名称（参考数据字典）。
        *   `map(x => (x, 1))`。
        *   `reduceByKey((x, y) => x + y)` 统计。
        *   `count.foreach(println)` 打印结果，并使用 `saveAsTextFile` 或 `coalesce(1).saveAsTextFile` 保存到 HDFS 指定输出目录。
    3. **执行与检查**:
        * 赋予HDFS输出目录写入权限: `hdfs dfs -chmod 777 /user/hadoop/highway_data`。
        * 运行程序，检查控制台输出及HDFS目标目录下的 `part-xxxxx` 文件内容。

**6.7 任务 7：使用 SparkSQL 进行高速交通状况画像构建**
*   **目标要点**：使用 Spark Thrift Server, DBeaver, PySpark, PyEcharts；分析时间维度(高峰)、空间维度(热力图)、瓶颈识别(低速)；可视化。
*   **需求说明**：
     1. 时间分布：识别高峰时段（每小时车流量、平均速度、拥堵指数）。
     2. 空间分布：热力图（按路段/门架的车流量、拥堵指数、地理坐标）。
     3. 瓶颈识别：定位低速路段（平均速度低于阈值如80km/h的路段列表、拥堵指数排名）。
	 4. 使用PySpark查询，PyEcharts生成可视化大屏HTML。
*   **实现步骤**：
    1.  **检查中间表**：确保Hive中创建的 `std_gsgl_...` 和 `dm_gsgl_...` 表可以通过 Spark SQL (通过Thrift Server连接Hive Metastore) 访问。使用 DBeaver 连接 Spark Thrift Server 验证。
    2.  **编写 SQL 查询** (可在DBeaver执行或嵌入PySpark)：
        *   时间分布: 查询每小时统计数据，写入 `dm_gsgl_hourly_traffic`。
        *   空间分布: 查询路段统计及经纬度，写入 `dm_gsgl_section_traffic_heatmap`。
        *   瓶颈识别: 查询 `WHERE avg_speed < 80` 的路段数据, 写入 `dm_gsgl_low_speed_sections`。
    3.  **PySpark 查询与 PyEcharts 可视化**：
        *   安装 python 包: `pyspark`, `pyecharts` 及其地图插件等。
        *   Python 脚本:
           * 创建 `SparkSession`，配置 `.config("hive.metastore.uris", "thrift://...")` 并 `.enableHiveSupport()`。
           * 使用 `spark.sql("SELECT * FROM highway.dm_gsgl_...") .toPandas()` 将SQL查询结果加载到 Pandas DataFrame。
           * 定义图表函数：时间分布图(`Line`)、热力图(`Geo`, `ChartType.HEATMAP`)、瓶颈路段图(`Bar`)。
           * 使用 `Page` 布局组合图表。
           * `page.render("highway_traffic_dashboard.html")` 渲染生成HTML文件。

**6.8 任务 8：使用 SparkSQL 进行高速交通流量关联分析**
*    **目标要点**：数据清洗标准化；车型与通行特征、收费站流量、服务区流量统计分析。
*    **需求说明**：
	1. 清洗车型、收费站、服务区数据到 `std_gsgl_` 标准表。
	2. 关联轨迹与车型，分析不同车型的小时通行量和平均速度。
	3. 关联轨迹与收费站，按收费站统计小时车流量。
	4. 关联轨迹与服务区，按服务区统计小时车流量（区分进出口）。
*   **实现步骤** (主要通过SparkSQL执行):
    1.  **相关数据清洗**：编写SQL对车型库、服务区、收费站原始表 (`ods_gsgl_...`)进行去重、空值处理、异常值处理、关键字段标准化，结果存入 `std_gsgl_vehicle_type`, `std_gsgl_toll`, `std_gsgl_service`。
    2.  **车辆类型与通行关系分析**：关联 `std_gsgl_vehicle_trails` 与 `std_gsgl_vehicle_type` （补充车型信息），并可关联速度表 `dm_gsgl_speed_trails`，按车型、时间段(小时)分组 `GROUP BY`，统计通行量 `COUNT(DISTINCT vlp)` 和计算平均速度 `AVG(speed)`。
    3.  **各收费站车流量统计**：关联 `std_gsgl_vehicle_trails` 与 `std_gsgl_toll`，按收费站编码/名称、时间段(小时)分组 `GROUP BY`，统计车流量 `COUNT(DISTINCT vlp)`。
    4.  **各服务区车流量统计**：关联 `std_gsgl_vehicle_trails` 与 `std_gsgl_service`，按服务区编码/名称、时间段(小时)分组 `GROUP BY`，统计车流量，并根据 `direction` 等字段区分入口和出口流量。

**项目总结与成果汇报（文档第1、3节提及）**
* **目标要点**：整理分析结果，制作PPT，团队协作，沟通表达。
* **实现步骤**：
  1. 汇总所有任务的分析结论和可视化结果（如图表、HTML大屏、关键指标）。
  2. 制作项目汇报PPT。
  3. 按小组完成15分钟的汇报展示。
---
这个总结涵盖了从环境准备到各个具体分析任务再到最终汇报的核心步骤和技术要点。
希望对您有帮助！
