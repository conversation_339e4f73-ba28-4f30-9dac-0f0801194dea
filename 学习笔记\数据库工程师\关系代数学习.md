# 关系代数学习文档

## 目录

1. [概述](https://claude.ai/chat/03fe1a9d-093a-4ed5-95ef-b046021074a9#概述)
2. [基本概念](https://claude.ai/chat/03fe1a9d-093a-4ed5-95ef-b046021074a9#基本概念)
3. [基本运算](https://claude.ai/chat/03fe1a9d-093a-4ed5-95ef-b046021074a9#基本运算)
4. [扩展运算](https://claude.ai/chat/03fe1a9d-093a-4ed5-95ef-b046021074a9#扩展运算)
5. [运算优先级与结合律](https://claude.ai/chat/03fe1a9d-093a-4ed5-95ef-b046021074a9#运算优先级与结合律)
6. [实际应用示例](https://claude.ai/chat/03fe1a9d-093a-4ed5-95ef-b046021074a9#实际应用示例)
7. [练习题](https://claude.ai/chat/03fe1a9d-093a-4ed5-95ef-b046021074a9#练习题)
8. [常见错误与注意事项](https://claude.ai/chat/03fe1a9d-093a-4ed5-95ef-b046021074a9#常见错误与注意事项)

------

## 概述

关系代数（Relational Algebra）是一种过程化的查询语言，它是关系数据库理论的数学基础。关系代数由E.F. Codd于1970年提出，为关系数据库的操作提供了理论框架。

### 关系代数的特点

- **过程化**：明确指定如何获得结果
- **形式化**：具有严格的数学定义
- **完备性**：能够表达所有可能的关系查询
- **基础性**：是SQL等查询语言的理论基础

------

## 基本概念

### 关系（Relation）

关系是一个二维表格，由行（元组）和列（属性）组成。

**示例：学生关系**

```
Student
+----+--------+-----+--------+
| ID | Name   | Age | Major  |
+----+--------+-----+--------+
| 1  | 张三   | 20  | 计算机 |
| 2  | 李四   | 21  | 数学   |
| 3  | 王五   | 19  | 计算机 |
+----+--------+-----+--------+
```

### 关系模式（Relation Schema）

关系模式定义了关系的结构，包括属性名和属性域。

- 记为：R(A₁, A₂, ..., Aₙ)
- 例如：Student(ID, Name, Age, Major)

### 元组（Tuple）

元组是关系中的一行，代表一个实体的实例。

### 属性（Attribute）

属性是关系中的一列，代表实体的某个特征。

### 域（Domain）

域是属性的取值范围。

------

## 基本运算

关系代数包含五个基本运算：并、差、笛卡尔积、选择、投影。

### 1. 选择运算（Selection）σ

**定义**：从关系中选择满足给定条件的元组。

**符号**：σ_条件(R)

**语法**：

```
σ_F(R) = {t | t ∈ R ∧ F(t) = true}
```

**示例**：

```
σ_age>20(Student)
```

结果：选择年龄大于20的学生

**条件操作符**：

- 比较操作符：=, ≠, <, ≤, >, ≥
- 逻辑操作符：∧ (AND), ∨ (OR), ¬ (NOT)

### 2. 投影运算（Projection）π

**定义**：从关系中选择指定的属性列，并去除重复元组。

**符号**：π_属性列表(R)

**语法**：

```
π_A₁,A₂,...,Aₖ(R)
```

**示例**：

```
π_Name,Age(Student)
```

结果：

```
+--------+-----+
| Name   | Age |
+--------+-----+
| 张三   | 20  |
| 李四   | 21  |
| 王五   | 19  |
+--------+-----+
```

### 3. 笛卡尔积（Cartesian Product）×

**定义**：两个关系R和S的笛卡尔积是一个新关系，包含R和S中所有元组的组合。

**符号**：R × S

**特点**：

- 如果R有m个元组，S有n个元组，则R × S有m×n个元组
- 如果R有p个属性，S有q个属性，则R × S有p+q个属性

**示例**：

```
Student × Course
```

### 4. 并运算（Union）∪

**定义**：返回属于R或属于S的所有元组。

**符号**：R ∪ S

**前提条件**：

- R和S必须是相容的（属性个数相同，对应属性域相同）

**特点**：

- 结果中不包含重复元组
- 满足交换律：R ∪ S = S ∪ R
- 满足结合律：(R ∪ S) ∪ T = R ∪ (S ∪ T)

### 5. 差运算（Difference）-

**定义**：返回属于R但不属于S的所有元组。

**符号**：R - S

**前提条件**：

- R和S必须是相容的

**特点**：

- 不满足交换律：R - S ≠ S - R
- R - S = R - (R ∩ S)

------

## 扩展运算

### 1. 交运算（Intersection）∩

**定义**：返回既属于R又属于S的所有元组。

**符号**：R ∩ S

**等价表示**：R ∩ S = R - (R - S) = S - (S - R)

### 2. 连接运算（Join）⋈

#### 2.1 θ连接（Theta Join）

**定义**：在笛卡尔积的基础上，选择满足连接条件θ的元组。

**符号**：R ⋈_θ S

**等价表示**：R ⋈_θ S = σ_θ(R × S)

**示例**：

```
Student ⋈_Student.ID=Enrollment.SID Enrollment
```

#### 2.2 等值连接（Equi Join）

**定义**：θ连接的特殊情况，连接条件只包含等号。

**示例**：

```
Student ⋈_ID=SID Enrollment
```

#### 2.3 自然连接（Natural Join）

**定义**：特殊的等值连接，自动连接两个关系中同名属性，并去除重复属性。

**符号**：R ⋈ S

**步骤**：

1. 找出R和S中的同名属性
2. 对这些属性进行等值连接
3. 去除结果中的重复属性列

**示例**： 如果Student(ID, Name, Age)和Enrollment(ID, Course, Grade)有公共属性ID：

```
Student ⋈ Enrollment
```

#### 2.4 外连接（Outer Join）

**左外连接（Left Outer Join）**：保留左关系中的所有元组 **右外连接（Right Outer Join）**：保留右关系中的所有元组 **全外连接（Full Outer Join）**：保留两个关系中的所有元组

### 3. 除运算（Division）÷

**定义**：设R(X,Y)和S(Y)是两个关系，R÷S的结果是一个关于X的关系，其中的元组x满足：对于S中的每个元组y，(x,y)都在R中。

**符号**：R ÷ S

**应用场景**：查询"至少"、"全部"等问题

**示例**： 找出选修了所有课程的学生：

```
π_SID,CID(Enrollment) ÷ π_CID(Course)
```

------

## 运算优先级与结合律

### 运算优先级（从高到低）

1. 选择 σ 和投影 π
2. 笛卡尔积 × 和连接 ⋈
3. 交 ∩
4. 并 ∪ 和差 -

### 结合律

- 并运算：(R ∪ S) ∪ T = R ∪ (S ∪ T)
- 交运算：(R ∩ S) ∩ T = R ∩ (S ∩ T)
- 连接运算：(R ⋈ S) ⋈ T = R ⋈ (S ⋈ T)

### 交换律

- 并运算：R ∪ S = S ∪ R
- 交运算：R ∩ S = S ∩ R
- 连接运算：R ⋈ S = S ⋈ R

------

## 实际应用示例

### 数据库模式

```
Student(SID, SName, Age, Major)
Course(CID, CName, Credits)
Enrollment(SID, CID, Grade)
Teacher(TID, TName, Department)
Teaching(TID, CID)
```

### 查询示例

#### 1. 简单查询

**查询年龄大于20岁的学生姓名**

```
π_SName(σ_Age>20(Student))
```

#### 2. 连接查询

**查询每个学生的选课信息（学生姓名、课程名称、成绩）**

```
π_SName,CName,Grade(Student ⋈ Enrollment ⋈ Course)
```

#### 3. 复合条件查询

**查询计算机专业且年龄小于21岁的学生选课信息**

```
π_SName,CName(σ_Major='计算机'∧Age<21(Student) ⋈ Enrollment ⋈ Course)
```

#### 4. 聚合类查询

**查询选修了所有课程的学生**

```
π_SID(Enrollment) ÷ π_CID(Course)
```

#### 5. 差集查询

**查询没有选修任何课程的学生**

```
π_SID(Student) - π_SID(Enrollment)
```

------

## 练习题

### 基础练习

**题目1**：给定关系Student(SID, SName, Age, Major)，写出下列查询的关系代数表达式：

1. 查询所有学生的姓名和年龄
2. 查询年龄在20-22岁之间的学生
3. 查询计算机专业的学生姓名

**答案**：

1. π_SName,Age(Student)
2. σ_Age≥20∧Age≤22(Student)
3. π_SName(σ_Major='计算机'(Student))

### 中级练习

**题目2**：给定关系Student(SID, SName), Course(CID, CName), Enrollment(SID, CID, Grade)：

1. 查询选修了课程"数据库"的学生姓名
2. 查询成绩优秀(≥90)的学生选课信息
3. 查询既选修了"数据库"又选修了"算法"的学生

**答案**：

1. π_SName(σ_CName='数据库'(Student ⋈ Enrollment ⋈ Course))
2. π_SName,CName,Grade(σ_Grade≥90(Student ⋈ Enrollment ⋈ Course))
3. π_SName(σ_CName='数据库'(Student ⋈ Enrollment ⋈ Course)) ∩ π_SName(σ_CName='算法'(Student ⋈ Enrollment ⋈ Course))

### 高级练习

**题目3**：复杂查询练习

1. 查询至少选修了3门课程的学生
2. 查询没有学生选修的课程
3. 查询选修课程数量最多的学生

这些高级查询需要结合多个运算和一些推理技巧来完成。

------

## 常见错误与注意事项

### 1. 类型相容性错误

**错误**：对不相容的关系进行并、交、差运算 **正确做法**：确保参与运算的关系有相同的属性结构

### 2. 属性名冲突

**错误**：在笛卡尔积中出现同名属性导致歧义 **正确做法**：使用关系名前缀区分属性，如Student.ID, Course.ID

### 3. 选择条件错误

**错误**：在选择运算中使用不存在的属性 **正确做法**：确保条件中的属性在关系中存在

### 4. 投影属性错误

**错误**：投影不存在的属性 **正确做法**：确保投影的属性在关系中存在

### 5. 连接条件设置

**错误**：连接条件不当导致笛卡尔积 **正确做法**：合理设置连接条件，避免产生过大的中间结果

### 6. 运算顺序

**错误**：不注意运算优先级 **正确做法**：适当使用括号明确运算顺序

------

## 关系代数与SQL的对应关系

| 关系代数        | SQL                                       |
| --------------- | ----------------------------------------- |
| σ_condition(R)  | SELECT * FROM R WHERE condition           |
| π_A,B(R)        | SELECT A, B FROM R                        |
| R ∪ S           | SELECT * FROM R UNION SELECT * FROM S     |
| R ∩ S           | SELECT * FROM R INTERSECT SELECT * FROM S |
| R - S           | SELECT * FROM R EXCEPT SELECT * FROM S    |
| R × S           | SELECT * FROM R, S                        |
| R ⋈_condition S | SELECT * FROM R JOIN S ON condition       |
| R ⋈ S           | SELECT * FROM R NATURAL JOIN S            |

------

## 学习建议

### 1. 循序渐进

- 先掌握基本概念和基本运算
- 再学习扩展运算
- 最后学习复杂查询的构造

### 2. 多做练习

- 从简单查询开始
- 逐步增加查询复杂度
- 注意验证查询结果的正确性

### 3. 理论结合实践

- 学会将自然语言查询转换为关系代数表达式
- 理解关系代数与SQL的对应关系
- 通过实际数据库操作验证理论知识

### 4. 注意细节

- 关注关系的相容性
- 注意属性名的一致性
- 理解各种连接的区别和适用场景

------

## 总结

关系代数是关系数据库理论的核心，它提供了一套完整的操作关系的方法。掌握关系代数不仅有助于理解数据库的工作原理，还能帮助我们编写更高效的SQL查询语句。

通过系统学习关系代数的基本概念、运算规则和应用技巧，我们可以更好地理解和使用关系数据库系统，为后续的数据库设计和优化打下坚实的基础。