# Docker 常用命令大全（中文详解）

Docker 是一个开源的应用容器引擎，让开发者可以打包他们的应用以及依赖包到一个可移植的容器中，然后发布到任何流行的 Linux 机器或 Windows 机器上，也可以实现虚拟化。容器是完全使用沙箱机制，相互之间不会有任何接口。

下面是 Docker 常用命令的分类汇总和详细说明：

## 一、镜像管理 (Image Management)

镜像是 Docker 容器运行的基础。镜像是只读的模板，包含了运行容器所需的文件系统和配置。

### 1. `docker images` 或 `docker image ls`

- **用途**：列出本地已有的镜像。
    
- **常用选项**：
    
    - `-a`, `--all`：列出所有镜像（包括中间层镜像）。
        
    - `-q`, `--quiet`：只显示镜像 ID。
        
    - `--digests`：显示镜像的摘要信息。
        
    - `--no-trunc`：显示完整的镜像信息（不截断）。
        
    - `-f`, `--filter <filter>`：根据条件过滤镜像 (例如：`dangling=true` 过滤悬空镜像, `label=key=value` 按标签过滤)。
        
- **示例**：
    
    ```
    docker images
    docker images -q
    docker images --filter "dangling=true"
    ```
    

### 2. `docker pull NAME[:TAG|@DIGEST]`

- **用途**：从镜像仓库（默认为 Docker Hub）拉取或更新指定的镜像。
    
- **参数说明**：
    
    - `NAME`：镜像的名称。
        
    - `TAG`：镜像的标签（版本号），默认为 `latest`。
        
    - `DIGEST`：镜像的摘要值。
        
- **常用选项**：
    
    - `-a`, `--all-tags`：拉取所有标签的镜像。
        
    - `--disable-content-trust`：禁用内容信任（默认为 true）。
        
- **示例**：
    
    ```
    docker pull ubuntu:20.04
    docker pull nginx
    ```
    

### 3. `docker rmi IMAGE_ID_OR_NAME [IMAGE_ID_OR_NAME...]` 或 `docker image rm IMAGE_ID_OR_NAME [IMAGE_ID_OR_NAME...]`

- **用途**：删除一个或多个本地镜像。
    
- **常用选项**：
    
    - `-f`, `--force`：强制删除镜像，即使有容器正在使用它（容器会被先停止并删除）。
        
    - `--no-prune`：不删除未打标签的父镜像。
        
- **示例**：
    
    ```
    docker rmi ubuntu:20.04
    docker rmi $(docker images -q -f "dangling=true") # 删除所有悬空镜像
    ```
    

### 4. `docker tag SOURCE_IMAGE[:TAG] TARGET_IMAGE[:TAG]`

- **用途**：为本地镜像添加一个新的标签（别名）。
    
- **示例**：
    
    ```
    docker tag ubuntu:20.04 myubuntu:latest
    docker tag nginx:latest registry.example.com/mynamespace/nginx:1.0
    ```
    

### 5. `docker build [OPTIONS] PATH | URL | -`

- **用途**：使用 Dockerfile 构建一个新的镜像。
    
- **常用选项**：
    
    - `-t`, `--tag <name:tag>`：指定镜像的名称和标签。
        
    - `-f`, `--file <path/to/Dockerfile>`：指定 Dockerfile 的路径（默认为 `PATH/Dockerfile`）。
        
    - `--build-arg <varname=value>`：设置构建时的变量。
        
    - `--no-cache`：构建镜像时不使用缓存。
        
    - `--pull`：总是尝试拉取最新的镜像版本。
        
    - `--progress <auto|plain|tty>`：设置构建进度输出类型。
        
- **示例**：
    
    ```
    docker build -t myapp:1.0 .  # 使用当前目录下的 Dockerfile 构建
    docker build -t myapp:custom -f /path/to/MyDockerfile .
    ```
    

### 6. `docker history IMAGE`

- **用途**：显示指定镜像的构建历史。
    
- **常用选项**：
    
    - `--no-trunc`：显示完整的历史信息。
        
    - `-H`, `--human`：以人类可读的格式打印大小和日期 (例如 100MB, 3 个月前)。
        
    - `-q`, `--quiet`：只显示镜像ID。
        
- **示例**：
    
    ```
    docker history nginx:latest
    ```
    

### 7. `docker save [OPTIONS] IMAGE [IMAGE...]`

- **用途**：将一个或多个镜像保存到一个 tar 归档文件中（通常用于离线迁移）。
    
- **常用选项**：
    
    - `-o`, `--output <filename.tar>`：指定输出的文件名。
        
- **示例**：
    
    ```
    docker save -o ubuntu_20.04.tar ubuntu:20.04
    docker save myapp:1.0 myotherapp:2.0 > apps.tar
    ```
    

### 8. `docker load [OPTIONS]`

- **用途**：从一个 tar 归档文件或标准输入中加载镜像。
    
- **常用选项**：
    
    - `-i`, `--input <filename.tar>`：指定输入的 tar 文件。
        
    - `-q`, `--quiet`：抑制加载过程的输出。
        
- **示例**：
    
    ```
    docker load -i ubuntu_20.04.tar
    docker load < apps.tar
    ```
    

## 二、容器管理 (Container Management)

容器是镜像的运行实例。可以被创建、启动、停止、移动和删除。

### 1. `docker run [OPTIONS] IMAGE [COMMAND] [ARG...]`

- **用途**：基于指定的镜像创建一个新的容器并运行它。这是 Docker 中最核心的命令之一。
    
- **常用选项**：
    
    - `-d`, `--detach`：后台运行容器，并返回容器 ID。
        
    - `-i`, `--interactive`：保持 STDIN 打开，即使没有附加。
        
    - `-t`, `--tty`：分配一个伪终端。通常 `-it` 一起使用。
        
    - `--name <container_name>`：为容器指定一个名称。
        
    - `-p`, `--publish <host_port>:<container_port>`：端口映射，将主机的端口映射到容器的端口。
        
    - `-P`, `--publish-all`：随机映射容器所有暴露的端口到主机。
        
    - `-v`, `--volume <host_path>:<container_path>[:ro]`：挂载数据卷，将主机的目录/文件挂载到容器内。`:ro` 表示只读。
        
    - `--volumes-from <container_name_or_id>`：从其他容器挂载数据卷。
        
    - `-e`, `--env <KEY=VALUE>`：设置环境变量。
        
    - `--env-file <file_path>`：从文件读取环境变量。
        
    - `--rm`：容器退出时自动删除容器。
        
    - `--restart <no|on-failure[:max-retries]|always|unless-stopped>`：配置容器的重启策略。
        
        - `no`：不自动重启（默认）。
            
        - `on-failure[:N]`：容器非 0 状态退出时重启，可选重启 N 次。
            
        - `always`：无论退出状态如何，总是重启。
            
        - `unless-stopped`：总是重启，除非容器被明确停止。
            
    - `--network <network_name>`：连接容器到指定的网络。
        
    - `--link <container_name_or_id:alias>`：(旧版，推荐使用自定义网络) 添加链接到另一个容器。
        
    - `--add-host <hostname:ip>`：添加自定义的 host 到 IP 的映射到容器的 `/etc/hosts`。
        
    - `--dns <ip_address>`：设置容器的 DNS 服务器。
        
    - `-m`, `--memory <limit>`：设置容器的内存使用限制 (例如 `512m`, `2g`)。
        
    - `--cpus <number>`：设置容器可以使用的 CPU 核心数 (例如 `0.5`, `2`)。
        
    - `--user <name|uid[:group|gid]>`：指定运行容器的用户。
        
    - `--entrypoint <command>`：覆盖镜像的默认 ENTRYPOINT。
        
- **示例**：
    
    ```
    docker run -d -p 8080:80 --name mynginx nginx:latest
    docker run -it --rm ubuntu:20.04 /bin/bash
    docker run -v /mydata:/data --name databox busybox
    ```
    

### 2. `docker ps` 或 `docker container ls`

- **用途**：列出正在运行的容器。
    
- **常用选项**：
    
    - `-a`, `--all`：列出所有容器（包括已停止的）。
        
    - `-q`, `--quiet`：只显示容器 ID。
        
    - `-s`, `--size`：显示容器的总文件大小。
        
    - `-n <number>`：显示最近创建的 N 个容器。
        
    - `-l`, `--latest`：显示最近创建的容器。
        
    - `-f`, `--filter <filter>`：根据条件过滤容器 (例如：`status=exited`, `name=mynginx`)。
        
- **示例**：
    
    ```
    docker ps
    docker ps -a
    docker ps -q -f "status=exited"
    ```
    

### 3. `docker start CONTAINER_ID_OR_NAME [CONTAINER_ID_OR_NAME...]`

- **用途**：启动一个或多个已停止的容器。
    
- **常用选项**：
    
    - `-a`, `--attach`：附加 STDOUT/STDERR 并转发信号。
        
    - `-i`, `--interactive`：附加容器的 STDIN。
        
- **示例**：
    
    ```
    docker start mynginx
    ```
    

### 4. `docker stop CONTAINER_ID_OR_NAME [CONTAINER_ID_OR_NAME...]`

- **用途**：停止一个或多个正在运行的容器（发送 SIGTERM，然后 SIGKILL）。
    
- **常用选项**：
    
    - `-t`, `--time <seconds>`：在发送 SIGKILL 前等待的秒数（默认 10）。
        
- **示例**：
    
    ```
    docker stop mynginx
    ```
    

### 5. `docker restart CONTAINER_ID_OR_NAME [CONTAINER_ID_OR_NAME...]`

- **用途**：重启一个或多个容器。
    
- **常用选项**：
    
    - `-t`, `--time <seconds>`：在停止容器前等待的秒数（默认 10）。
        
- **示例**：
    
    ```
    docker restart mynginx
    ```
    

### 6. `docker rm CONTAINER_ID_OR_NAME [CONTAINER_ID_OR_NAME...]` 或 `docker container rm ...`

- **用途**：删除一个或多个容器。默认情况下，不能删除正在运行的容器。
    
- **常用选项**：
    
    - `-f`, `--force`：强制删除正在运行的容器（使用 SIGKILL）。
        
    - `-v`, `--volumes`：同时删除与容器关联的匿名数据卷。
        
- **示例**：
    
    ```
    docker rm mynginx_stopped
    docker rm -f mynginx_running
    docker rm $(docker ps -aq -f "status=exited") # 删除所有已停止的容器
    ```
    

### 7. `docker logs [OPTIONS] CONTAINER_ID_OR_NAME`

- **用途**：获取容器的日志。
    
- **常用选项**：
    
    - `-f`, `--follow`：持续跟踪日志输出。
        
    - `--tail <number_or_all>`：显示从日志末尾开始的指定行数（默认为 `all`）。
        
    - `-t`, `--timestamps`：显示时间戳。
        
    - `--since <timestamp>`：显示指定时间戳之后的日志。
        
    - `--until <timestamp>`：显示指定时间戳之前的日志。
        
- **示例**：
    
    ```
    docker logs mynginx
    docker logs -f --tail 100 myapp
    ```
    

### 8. `docker exec [OPTIONS] CONTAINER_ID_OR_NAME COMMAND [ARG...]`

- **用途**：在正在运行的容器内执行命令。
    
- **常用选项**：
    
    - `-d`, `--detach`：后台运行命令。
        
    - `-i`, `--interactive`：保持 STDIN 打开，即使没有附加。
        
    - `-t`, `--tty`：分配一个伪终端。通常 `-it` 一起使用。
        
    - `-e`, `--env <KEY=VALUE>`：设置环境变量。
        
    - `-u`, `--user <name|uid>`：指定执行命令的用户。
        
    - `-w`, `--workdir <path>`：指定工作目录。
        
- **示例**：
    
    ```
    docker exec -it mynginx /bin/bash
    docker exec mynginx ls /usr/share/nginx/html
    ```
    

### 9. `docker attach CONTAINER_ID_OR_NAME`

- **用途**：附加到正在运行的容器的标准输入、输出和错误流。
    
- **注意**：如果从这个 stdin 中 exit，会导致容器停止。通常使用 `docker exec -it <container> <shell>` 更安全。
    
- **常用选项**：
    
    - `--detach-keys <sequence>`：指定用于分离的按键序列 (默认 `ctrl-p,ctrl-q`)。
        
    - `--no-stdin`：不附加 STDIN。
        
    - `--sig-proxy`：代理所有接收到的信号到进程 (默认为 true)。
        
- **示例**：
    
    ```
    docker attach myinteractiveapp
    ```
    

### 10. `docker cp SOURCE_PATH CONTAINER:DEST_PATH` 或 `docker cp CONTAINER:SOURCE_PATH DEST_PATH`

```
- **用途**：在容器和本地文件系统之间复制文件/文件夹。
- **示例**：
  ```bash
  docker cp ./myfile.txt mycontainer:/app/myfile.txt # 从本地复制到容器
  docker cp mycontainer:/app/logs/error.log ./error.log # 从容器复制到本地
  ```
```

### 11. `docker inspect [OPTIONS] NAME|ID [NAME|ID...]`

```
- **用途**：获取容器或镜像的详细底层信息（JSON 格式）。
- **常用选项**：
  - `-f`, `--format <Go_template_string>`：使用 Go 模板格式化输出。
  - `-s`, `--size`：如果对象是容器，显示总文件大小。
  - `--type <container|image|network|volume|...>`：指定要检查的对象类型。
- **示例**：
  ```bash
  docker inspect mynginx
  docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' mycontainer
  ```
```

### 12. `docker top CONTAINER_ID_OR_NAME [ps OPTIONS]`

```
- **用途**：显示一个运行中容器的进程信息。
- **示例**：
  ```bash
  docker top mynginx
  ```
```

### 13. `docker commit [OPTIONS] CONTAINER_ID_OR_NAME [REPOSITORY[:TAG]]`

```
- **用途**：从容器的当前状态创建一个新的镜像。
- **常用选项**：
  - `-a`, `--author <string>`：作者 (例如 "John Hannibal Smith <<EMAIL>>")。
  - `-c`, `--change <Dockerfile_instruction>`：应用 Dockerfile 指令来创建镜像。
  - `-m`, `--message <string>`：提交信息。
  - `-p`, `--pause`：在提交期间暂停容器 (默认为 true)。
- **示例**：
  ```bash
  docker commit -m "Added new configuration" -a "My Name" mynginx_modified mynginx_custom:1.0
  ```
```

### 14. `docker diff CONTAINER_ID_OR_NAME`

```
- **用途**：检查容器文件系统中所做的更改（添加、删除、修改）。
- **输出说明**：
  - `A`：添加
  - `D`：删除
  - `C`：修改
- **示例**：
  ```bash
  docker diff mynginx
  ```
```

### 15. `docker pause CONTAINER_ID_OR_NAME [CONTAINER_ID_OR_NAME...]`

```
- **用途**：暂停一个或多个容器内的所有进程。
- **示例**：
  ```bash
  docker pause mynginx
  ```
```

### 16. `docker unpause CONTAINER_ID_OR_NAME [CONTAINER_ID_OR_NAME...]`

```
- **用途**：恢复一个或多个已暂停容器内的所有进程。
- **示例**：
  ```bash
  docker unpause mynginx
  ```
```

### 17. `docker wait CONTAINER_ID_OR_NAME [CONTAINER_ID_OR_NAME...]`

```
- **用途**：阻塞直到一个或多个容器停止，然后打印它们的退出代码。
- **示例**：
  ```bash
  docker wait mybackgroundjob
  echo "Job finished with exit code $?"
  ```
```

### 18. `docker port CONTAINER_ID_OR_NAME [PRIVATE_PORT[/PROTO]]`

```
- **用途**：列出指定的容器的端口映射，或者查找特定端口映射到主机的哪个端口。
- **示例**：
  ```bash
  docker port mynginx # 列出所有端口映射
  docker port mynginx 80/tcp # 查看容器的80/tcp端口映射到主机的哪个端口
  ```
```

## 三、仓库管理 (Registry & Repository Management)

Docker Registry 是存储 Docker 镜像的地方。Docker Hub 是默认的公共 Registry。

### 1. `docker login [OPTIONS] [SERVER]`

- **用途**：登录到一个 Docker 镜像仓库。
    
- **常用选项**：
    
    - `-u`, `--username <string>`：用户名。
        
    - `-p`, `--password <string>`：密码。
        
    - `--password-stdin`：从 STDIN 读取密码。
        
- **示例**：
    
    ```
    docker login
    docker login myregistry.example.com -u myuser -p mypass
    echo "mypass" | docker login myregistry.example.com -u myuser --password-stdin
    ```
    

### 2. `docker logout [SERVER]`

- **用途**：从一个 Docker 镜像仓库登出。
    
- **示例**：
    
    ```
    docker logout
    docker logout myregistry.example.com
    ```
    

### 3. `docker push NAME[:TAG]`

- **用途**：将本地的镜像推送到一个镜像仓库。
    
- **参数说明**：
    
    - `NAME`：镜像名称，通常包含仓库地址 (例如 `myregistry.example.com/mynamespace/myimage`)。
        
    - `TAG`：镜像标签。
        
- **常用选项**：
    
    - `-a`, `--all-tags`：推送所有标签。
        
    - `--disable-content-trust`：禁用内容信任 (默认为 true)。
        
- **示例**：
    
    ```
    docker push myusername/myimage:latest
    docker push myregistry.example.com/mynamespace/myimage:1.0
    ```
    

### 4. `docker search [OPTIONS] TERM`

- **用途**：从 Docker Hub 搜索镜像。
    
- **常用选项**：
    
    - `--filter <filter>` 或 `-f <filter>`：基于条件过滤 (例如 `is-official=true`, `stars=N`)。
        
    - `--limit <number>`：最大搜索结果数量 (默认 25)。
        
    - `--no-trunc`：不截断输出。
        
- **示例**：
    
    ```
    docker search ubuntu
    docker search --filter is-official=true --filter stars=100 nginx
    ```
    

## 四、网络管理 (Network Management)

Docker 允许你创建和管理容器网络，以便容器之间以及容器与外部世界进行通信。

### 1. `docker network ls` 或 `docker network list`

- **用途**：列出 Docker 网络。
    
- **常用选项**：
    
    - `-q`, `--quiet`：只显示网络 ID。
        
    - `-f`, `--filter <filter>`：根据条件过滤。
        
- **示例**：
    
    ```
    docker network ls
    ```
    

### 2. `docker network create [OPTIONS] NETWORK_NAME`

- **用途**：创建一个新的 Docker 网络。
    
- **常用选项**：
    
    - `-d`, `--driver <bridge|host|overlay|macvlan|none>`：指定网络驱动程序 (默认为 `bridge`)。
        
    - `--subnet <subnet>`：指定网络的子网 (例如 `**********/16`)。
        
    - `--gateway <ip>`：指定子网的网关。
        
    - `--ip-range <ip_range>`：指定容器可以分配的 IP 地址范围。
        
    - `-o`, `--opt <key=value>`：设置驱动程序的特定选项。
        
    - `--attachable`：允许手动将容器附加到此网络 (对于 Swarm scope 的 overlay 网络)。
        
- **示例**：
    
    ```
    docker network create my_bridge_network
    docker network create --driver bridge --subnet ***********/24 --gateway *********** my_custom_net
    ```
    

### 3. `docker network rm NETWORK_NAME_OR_ID [NETWORK_NAME_OR_ID...]`

- **用途**：删除一个或多个 Docker 网络。
    
- **注意**：如果网络中有容器连接，则无法删除。
    
- **示例**：
    
    ```
    docker network rm my_bridge_network
    ```
    

### 4. `docker network inspect NETWORK_NAME_OR_ID [NETWORK_NAME_OR_ID...]`

- **用途**：显示一个或多个网络的详细信息。
    
- **常用选项**：
    
    - `-f`, `--format <Go_template_string>`：使用 Go 模板格式化输出。
        
- **示例**：
    
    ```
    docker network inspect bridge
    docker network inspect -f '{{range .Containers}}{{.Name}} {{.IPv4Address}}{{end}}' my_custom_net
    ```
    

### 5. `docker network connect [OPTIONS] NETWORK_NAME_OR_ID CONTAINER_ID_OR_NAME`

- **用途**：将一个正在运行的容器连接到一个网络。
    
- **常用选项**：
    
    - `--ip <ip_address>`：指定容器在该网络中的 IP 地址。
        
    - `--alias <name>`：为容器在该网络中指定一个网络别名。
        
- **示例**：
    
    ```
    docker network connect my_custom_net mynginx
    docker network connect --alias db --ip ************* my_custom_net my_database_container
    ```
    

### 6. `docker network disconnect [OPTIONS] NETWORK_NAME_OR_ID CONTAINER_ID_OR_NAME`

- **用途**：将一个容器从一个网络断开。
    
- **常用选项**：
    
    - `-f`, `--force`：强制断开连接。
        
- **示例**：
    
    ```
    docker network disconnect my_custom_net mynginx
    ```
    

### 7. `docker network prune [OPTIONS]`

```
- **用途**：删除所有未被任何容器使用的网络。
- **常用选项**：
  - `-f`, `--force`：不提示确认。
  - `--filter <filter>`：根据条件过滤要删除的网络 (例如 `until=<timestamp>`)。
- **示例**：
  ```bash
  docker network prune
  docker network prune -f
  ```
```

## 五、数据卷管理 (Volume Management)

数据卷是持久化 Docker 容器数据的推荐方式。

### 1. `docker volume ls` 或 `docker volume list`

- **用途**：列出 Docker 数据卷。
    
- **常用选项**：
    
    - `-q`, `--quiet`：只显示数据卷名称。
        
    - `-f`, `--filter <filter>`：根据条件过滤 (例如 `dangling=true`, `driver=local`)。
        
- **示例**：
    
    ```
    docker volume ls
    docker volume ls -f "dangling=true"
    ```
    

### 2. `docker volume create [OPTIONS] [VOLUME_NAME]`

- **用途**：创建一个新的 Docker 数据卷。
    
- **常用选项**：
    
    - `-d`, `--driver <driver_name>`：指定数据卷驱动程序 (默认为 `local`)。
        
    - `--label <key=value>`：为数据卷设置元数据标签。
        
    - `-o`, `--opt <key=value>`：设置驱动程序的特定选项。
        
- **示例**：
    
    ```
    docker volume create my_data_volume
    docker volume create --driver local --opt type=none --opt device=/my/host/path --opt o=bind my_bind_volume
    ```
    

### 3. `docker volume rm VOLUME_NAME [VOLUME_NAME...]`

- **用途**：删除一个或多个 Docker 数据卷。
    
- **注意**：如果数据卷仍被容器使用，则无法删除。
    
- **常用选项**：
    
    - `-f`, `--force`：强制删除数据卷，即使它正在被使用 (慎用!)。
        
- **示例**：
    
    ```
    docker volume rm my_data_volume
    docker volume rm $(docker volume ls -qf dangling=true) # 删除所有悬空数据卷
    ```
    

### 4. `docker volume inspect VOLUME_NAME [VOLUME_NAME...]`

- **用途**：显示一个或多个数据卷的详细信息。
    
- **常用选项**：
    
    - `-f`, `--format <Go_template_string>`：使用 Go 模板格式化输出。
        
- **示例**：
    
    ```
    docker volume inspect my_data_volume
    ```
    

### 5. `docker volume prune [OPTIONS]`

```
- **用途**：删除所有未被任何容器使用的本地数据卷（匿名数据卷）。
- **常用选项**：
  - `-f`, `--force`：不提示确认。
  - `--filter <filter>`：根据条件过滤要删除的数据卷 (例如 `label!=keep`)。
- **示例**：
  ```bash
  docker volume prune
  docker volume prune -f
  ```
```

## 六、Docker Compose (编排工具)

Docker Compose 是一个用于定义和运行多容器 Docker 应用程序的工具。它使用 YAML 文件来配置应用程序的服务。

- **`docker-compose up [OPTIONS] [SERVICE...]`**: 构建、(重新)创建、启动和附加到服务的容器。
    
    - `-d`: 后台运行。
        
    - `--build`: 在启动容器前构建镜像。
        
    - `--force-recreate`: 强制重新创建容器，即使配置和镜像没有改变。
        
- **`docker-compose down [OPTIONS]`**: 停止并移除由 `up` 创建的容器、网络、数据卷和镜像。
    
    - `-v`, `--volumes`: 移除在 `volumes` 部分定义的命名数据卷以及附加到容器的匿名数据卷。
        
    - `--rmi <all|local>`: 移除镜像。`all` 移除所有镜像，`local` 只移除没有自定义标签的镜像。
        
- **`docker-compose ps [OPTIONS] [SERVICE...]`**: 列出服务中的容器。
    
- **`docker-compose logs [OPTIONS] [SERVICE...]`**: 显示服务的日志输出。
    
    - `-f`, `--follow`: 跟踪日志输出。
        
- **`docker-compose build [OPTIONS] [SERVICE...]`**: 构建或重建服务。
    
- **`docker-compose pull [OPTIONS] [SERVICE...]`**: 拉取服务镜像。
    
- **`docker-compose start [SERVICE...]`**: 启动已存在的服务容器。
    
- **`docker-compose stop [OPTIONS] [SERVICE...]`**: 停止正在运行的服务容器。
    
- **`docker-compose restart [OPTIONS] [SERVICE...]`**: 重启服务容器。
    
- **`docker-compose rm [OPTIONS] [SERVICE...]`**: 移除已停止的服务容器。
    
    - `-f`, `--force`: 强制移除。
        
    - `-s`, `--stop`: 在移除前先停止容器。
        
    - `-v`: 移除与容器关联的匿名数据卷。
        
- **`docker-compose exec [OPTIONS] SERVICE COMMAND [ARGS...]`**: 在正在运行的服务容器内执行命令。
    
    - `-T`: 禁用伪 TTY 分配。当 `stdin` 未连接时使用。
        
- **`docker-compose config [OPTIONS]`**: 验证并查看 Compose 文件。
    
    - `--services`: 打印服务名称。
        
    - `--volumes`: 打印数据卷名称。
        
    - `--hash="*"`: 打印每个服务的配置哈希值。
        

**注意**: 从 Docker Compose V2 开始，命令格式变为 `docker compose ...` (中间没有横杠)。例如 `docker compose up`。旧的 `docker-compose` 仍然可用，但推荐使用新的语法。

## 七、系统信息与清理 (System Info & Pruning)

### 1. `docker info [OPTIONS]`

- **用途**：显示 Docker 系统范围的信息。包括内核版本、容器数量、镜像数量等。
    
- **常用选项**：
    
    - `-f`, `--format <Go_template_string>`：使用 Go 模板格式化输出。
        
- **示例**：
    
    ```
    docker info
    ```
    

### 2. `docker version [OPTIONS]`

- **用途**：显示 Docker 的版本信息（客户端和服务器）。
    
- **常用选项**：
    
    - `-f`, `--format <Go_template_string>`：使用 Go 模板格式化输出。
        
- **示例**：
    
    ```
    docker version
    ```
    

### 3. `docker system df [OPTIONS]`

```
- **用途**：显示 Docker 磁盘使用情况。
- **常用选项**：
  - `-v`, `--verbose`：显示详细输出。
- **示例**：
  ```bash
  docker system df
  ```
```

### 4. `docker system prune [OPTIONS]`

```
- **用途**：删除所有未使用的 Docker 资源：
  - 已停止的容器
  - 未被任何容器使用的网络
  - 悬空镜像（没有标签且不被任何容器引用的镜像）
  - 未被任何容器使用的构建缓存
- **常用选项**：
  - `-a`, `--all`：同时删除所有未使用的镜像（不仅仅是悬空镜像）和未使用的构建缓存。
  - `-f`, `--force`：不提示确认。
  - `--volumes`：同时删除所有未被任何容器使用的本地数据卷 (匿名数据卷)。(慎用!)
  - `--filter <filter>`：根据条件过滤。
- **示例**：
  ```bash
  docker system prune
  docker system prune -a -f --volumes
  ```
```

## 八、Dockerfile 相关

Dockerfile 是一个文本文件，包含了一条条的指令(Instruction)，每一条指令构建一层，因此每一条指令的内容，就是描述该层应当如何构建。

主要的指令包括：

- **`FROM <image>[:<tag>] [AS <name>]`**: 指定基础镜像。
    
- **`RUN <command>`**: 在当前镜像基础上执行命令，并提交为新的一层。
    
- **`CMD ["executable","param1","param2"]` (exec 格式, 推荐)** 或 `CMD command param1 param2` (shell 格式): 容器启动时默认执行的命令。Dockerfile 中可以有多个 CMD 指令，但只有最后一个生效。会被 `docker run` 命令行中的命令覆盖。
    
- **`ENTRYPOINT ["executable", "param1", "param2"]` (exec 格式, 推荐)** 或 `ENTRYPOINT command param1 param2` (shell 格式): 配置容器启动后执行的命令。不会被 `docker run` 命令行中的命令轻易覆盖，除非使用 `--entrypoint` 选项。`CMD` 的内容可以作为 `ENTRYPOINT` 的参数。
    
- **`WORKDIR /path/to/workdir`**: 为后续的 `RUN`, `CMD`, `ENTRYPOINT`, `COPY`, `ADD` 指令设置工作目录。
    
- **`EXPOSE <port> [<port>/<protocol>...]`**: 声明容器运行时监听的端口 (仅为元数据，实际端口映射在 `docker run -p` 中指定)。
    
- **`ENV <key>=<value>` ...**: 设置环境变量。
    
- **`ADD <src>... <dest>`**: 复制文件、目录或远程文件 URL 从 `<src>` 到镜像文件系统的 `<dest>`。如果 `<src>` 是一个可识别的压缩格式 (如 gzip, bzip2, xz)，它会被自动解压。
    
- **`COPY <src>... <dest>`**: 复制文件或目录从 `<src>` 到镜像文件系统的 `<dest>`。与 `ADD` 类似，但不具备自动解压和下载远程文件的功能，更推荐用于本地文件复制。
    
- **`VOLUME ["/data"]`**: 创建一个可以从本地主机或其他容器挂载的挂载点，一般用来存放数据库和需要保持的数据等。
    
- **`USER <user>[:<group>]` 或 `USER <UID>[:<GID>]`**: 指定运行容器时的用户名或 UID，以及可选的用户组或 GID。
    
- **`ARG <name>[=<default value>]`**: 定义构建参数，可以在 `docker build` 时通过 `--build-arg <varname>=<value>` 传递。
    
- **`ONBUILD [INSTRUCTION]`**: 配置当此镜像作为其他镜像的基础镜像时，会执行的指令。
    
- **`STOPSIGNAL signal`**: 设置发送到容器以退出的系统调用信号。
    
- **`HEALTHCHECK [OPTIONS] CMD command` 或 `HEALTHCHECK NONE`**: 告诉 Docker 如何测试容器以检查它是否仍在工作。
    
- **`SHELL ["executable", "parameters"]`**: 允许覆盖用于 `RUN`, `CMD`, `ENTRYPOINT` shell 格式命令的默认 shell。
    

构建镜像的命令是 `docker build` (已在镜像管理部分介绍)。

这只是 Docker 命令的一部分，但涵盖了日常使用中最常用和最重要的命令。对于每个命令，你都可以通过在其后添加 `--help` 来获取更详细的帮助信息，例如 `docker run --help`。

希望这份大全对你有所帮助！