简述关系型数据库在处理海量数据时遇到的主要瓶颈。
	关系型数据库在处理海量数据的情景下，纵向扩展成本高，横向扩展难以保证一致性和分区容错性
	==且事务机制和完整性约束导致性能下降==
答案：** **关系型数据库在海量数据场景下，纵向扩展成本高，横向扩展难以解决分布式一致性、分区容错性等问题，且事务机制和完整性约束导致性能下降。

简要说明NoSQL数据库的“CAP定理”及其在实际应用中的取舍
	CAP原理是一致性、可用性、分区容错性的不可兼得，在实际应用中优先保证CP和AP
	**NoSQL通常优先保证AP或CP，根据业务需求权衡。**

大数据场景中，为什么需要NoSQL数据库？
	NoSql数据库采用分布式数据库，处理海量数据性能高，查询更快？？？？


简述关系型数据库在分布式部署时遇到的主要瓶颈。
	关系型数据库在分布式部署难以保证一致性和分区容错性,对事务处理和一致性造成影响
**关系型数据库在分布式部署时，难以保证强一致性与高可用性，事务处理复杂且性能下降。**
2、简要说明CAP理论与BASE理论的核心区别。
	CAP理论的核心是一致性、可用性、分区容错性的不可兼得，而BASE理论的核心是通过弱一致性（最终一致性）来保证高可用性

3、文档式存储模式的优势体现在哪些方面？

		
1、简述MongoDB中BSON格式的主要优势。

	BSON采用二进制存储字段数据，提高索引查询效率
**答案：** **BSON格式通过二进制存储字段长度，提高检索速度；支持更多数据类型（如Date、Binary）；解析效率高。**
2、MongoDB的CAP理论在分布式系统中如何应用？
	MongoDB采用的是CAP理论中的AP，也就是可用性和分区容错性性

1、简述图数据库的基本原理及其适用场景。
	图数据库通过节点、关系、

2、简述Neo4j与其他图数据库（如JanusGraph）的核心差异。
	Neon4j采用原生图存储，JanusGraph采用分布式存储

1、简述键值对数据库的基本原理及其适用场景。

		键值对数据库采用键和值存储，是用缓存和会话场景
4、简述Redis持久化存储机制（RDB/AOF）的优缺点及恢复方法
	RDB是快照持久化，AOF是日志持久化，恢复优先使用AOF


简述Cassandra的核心特点及其与关系型数据库的区别。


2、解释Cassandra数据模型中的行键、列族和超级列族的概念。


3、描述Cassandra的机架感知策略及其在集群部署中的作用。

