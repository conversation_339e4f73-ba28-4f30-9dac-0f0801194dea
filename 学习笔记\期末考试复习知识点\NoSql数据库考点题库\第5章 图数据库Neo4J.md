# 第5章 图数据库Neo4J

## 一、简答题

1、简述图数据库的基本原理及其适用场景。
**答案：** **图数据库通过节点、关系和属性存储数据，适用于社交网络、推荐系统等场景。**

2、简述Neo4j与其他图数据库（如JanusGraph）的核心差异。
**答案：** **Neo4j采用原生图存储，而JanusGraph基于分布式存储引擎（如HBase/Cassandra）。**

3、解释Cypher语言中"节点-关系-节点"模式的语法结构。
**答案：** **语法结构为(n:Label{属性})-[r:关系类型]->(m:Label{属性})。**

4、简述Neo4j服务端的启动与运行流程。
**答案：** **启动流程：配置环境变量→执行neo4j start→访问7474端口。**

## 二、综合题

1、设计一个基于Neo4j的社交网络分析系统，说明其数据模型和核心操作。
**答案：** **数据模型：用户节点（User）、关注关系（FOLLOW）；操作：MATCH (u1:User)-[:FOLLOW]->(u2:User)。**

2、结合案例说明如何利用Cypher语言实现复杂路径查询。
**答案：** **示例：MATCH p=shortestPath((a:Person)-[*..5]-(b:Person)) WHERE a.name='Alice' AND b.name='Bob' RETURN p.**

3、分析Neo4j在企业知识图谱构建中的技术优势。
**答案：** **技术优势：高效路径查询、灵活的模式扩展、可视化支持。**

## 三、单选题

1、下列关于图数据库的描述中，哪一项是正确的（）？
A) 图数据库不支持事务操作
**B) 图数据库的存储模型基于键值对或列存储**
C) 图数据库的查询性能与数据规模无关
D) 图数据库无法处理海量数据

2、Neo4j的默认端口号是（）？
A) 3306
B) 27017
**C) 7474**
D) 8080

3、Cypher语言中用于查询节点的关键词是（）？
A) SELECT
**B) MATCH**
C) FIND
D) SEARCH

4、Neo4j服务端的默认数据存储路径是（）？
A) /data/db
**B) /var/lib/neo4j/data**
C) C:\Program Files\Neo4j\data
D) /usr/local/neo4j/data

## 四、多选题

1、Neo4j支持的索引类型包括（）？
**A) 范围索引**
**B) 文本索引**
**C) 点索引**
**D) 令牌查找索引**

2、Cypher语言中用于路径查询的语法包括（）？
**A) shortestPath**
**B) allShortestPaths**
**C) MATCH...WHERE**
D) UNWIND

3、Neo4j支持的编程语言驱动包括（）？
**A) Java**
**B) Python**
**C) JavaScript**
**D) Go**

4、Cypher语言中用于聚合计算的函数包括（）？
**A) COUNT**
**B) SUM**
**C) AVG**
D) DISTINCT

## 五、填空题

1、Cypher语言中用于创建节点的关键词是**CREATE**。
2、Neo4j的图形化查询工具是**Neo4j Browser**。
3、Cypher语言中用于删除节点及其关系的关键词是**DETACH DELETE**。
4、Neo4j服务端的默认日志存储路径是**/var/lib/neo4j/logs**。

## 六、判断题（正确打√，错误打×）

1、Neo4j社区版支持集群部署。（**×**）
2、Cypher语言中变量名大小写不敏感。（**×**）
3、Neo4j的Bolt协议是基于HTTP的。（**×**）
4、Cypher语言中MATCH语句必须包含RETURN子句。（**√**）