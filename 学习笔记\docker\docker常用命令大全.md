# Docker常用命令大全

## 1. 基础信息命令

### 版本和系统信息

- `docker version` - 显示Docker客户端和服务器版本信息
- `docker info` - 显示Docker系统信息，包括容器数量、镜像数量、存储驱动等
- `docker --help` - 显示Docker命令帮助信息
- `docker system df` - 显示Docker磁盘使用情况
- `docker system prune` - 清理未使用的Docker对象（容器、网络、镜像、构建缓存）

## 2. 镜像管理命令

### 镜像查看

- `docker images` 或 `docker image ls` - 列出本地所有镜像
- `docker images -a` - 显示所有镜像（包括中间层镜像）
- `docker images -q` - 只显示镜像ID
- `docker history <镜像名>` - 显示镜像的历史层信息

### 镜像搜索和拉取

- `docker search <镜像名>` - 在Docker Hub中搜索镜像
- `docker pull <镜像名>:<标签>` - 从仓库拉取镜像到本地
- `docker pull <镜像名>` - 拉取最新版本镜像（默认latest标签）

### 镜像构建

- `docker build -t <镜像名>:<标签> <Dockerfile路径>` - 根据Dockerfile构建镜像
- `docker build -t <镜像名> .` - 在当前目录构建镜像
- `docker build --no-cache -t <镜像名> .` - 构建时不使用缓存

### 镜像标签和推送

- `docker tag <源镜像> <目标镜像>:<标签>` - 为镜像创建标签
- `docker push <镜像名>:<标签>` - 推送镜像到仓库
- `docker login` - 登录Docker仓库
- `docker logout` - 登出Docker仓库

### 镜像删除

- `docker rmi <镜像ID或名称>` - 删除指定镜像
- `docker rmi $(docker images -q)` - 删除所有镜像
- `docker image prune` - 删除未使用的镜像
- `docker image prune -a` - 删除所有未使用的镜像

### 镜像导入导出

- `docker save -o <文件名.tar> <镜像名>` - 将镜像保存为tar文件
- `docker load -i <文件名.tar>` - 从tar文件加载镜像
- `docker export <容器ID> > <文件名.tar>` - 导出容器为tar文件
- `docker import <文件名.tar> <镜像名>:<标签>` - 从tar文件导入为镜像

## 3. 容器生命周期管理

### 容器创建和运行

- `docker run <镜像名>` - 创建并运行容器
- `docker run -d <镜像名>` - 后台运行容器
- `docker run -it <镜像名>` - 交互式运行容器
- `docker run --name <容器名> <镜像名>` - 指定容器名称运行
- `docker run -p <主机端口>:<容器端口> <镜像名>` - 端口映射运行容器
- `docker run -v <主机路径>:<容器路径> <镜像名>` - 挂载数据卷运行容器
- `docker run --rm <镜像名>` - 容器停止后自动删除
- `docker create <镜像名>` - 创建容器但不启动

### 容器启动和停止

- `docker start <容器ID或名称>` - 启动已停止的容器
- `docker stop <容器ID或名称>` - 优雅停止运行中的容器
- `docker kill <容器ID或名称>` - 强制停止运行中的容器
- `docker restart <容器ID或名称>` - 重启容器
- `docker pause <容器ID或名称>` - 暂停容器
- `docker unpause <容器ID或名称>` - 恢复暂停的容器

### 容器删除

- `docker rm <容器ID或名称>` - 删除已停止的容器
- `docker rm -f <容器ID或名称>` - 强制删除运行中的容器
- `docker rm $(docker ps -aq)` - 删除所有容器
- `docker container prune` - 删除所有停止的容器

## 4. 容器查看和监控

### 容器列表

- `docker ps` - 显示运行中的容器
- `docker ps -a` - 显示所有容器（包括已停止的）
- `docker ps -q` - 只显示容器ID
- `docker ps -l` - 显示最新创建的容器
- `docker ps --format "table {{.ID}}\t{{.Image}}\t{{.Status}}"` - 自定义输出格式

### 容器详细信息

- `docker inspect <容器ID或名称>` - 显示容器详细信息
- `docker logs <容器ID或名称>` - 查看容器日志
- `docker logs -f <容器ID或名称>` - 实时跟踪容器日志
- `docker logs --tail 100 <容器ID或名称>` - 查看最后100行日志
- `docker top <容器ID或名称>` - 显示容器内运行的进程

### 容器资源使用

- `docker stats` - 显示所有容器的资源使用统计
- `docker stats <容器ID或名称>` - 显示指定容器的资源使用
- `docker stats --no-stream` - 显示一次性的资源使用快照

## 5. 容器交互命令

### 进入容器

- `docker exec -it <容器ID或名称> /bin/bash` - 在运行中的容器内启动bash
- `docker exec -it <容器ID或名称> sh` - 在运行中的容器内启动shell
- `docker attach <容器ID或名称>` - 连接到运行中容器的主进程

### 文件操作

- `docker cp <本地路径> <容器ID>:<容器路径>` - 从本地复制文件到容器
- `docker cp <容器ID>:<容器路径> <本地路径>` - 从容器复制文件到本地

### 容器更新

- `docker commit <容器ID> <新镜像名>:<标签>` - 将容器的更改提交为新镜像
- `docker update --memory="1g" <容器ID>` - 更新容器资源限制

## 6. 数据卷管理

### 数据卷操作

- `docker volume create <卷名>` - 创建数据卷
- `docker volume ls` - 列出所有数据卷
- `docker volume inspect <卷名>` - 查看数据卷详细信息
- `docker volume rm <卷名>` - 删除数据卷
- `docker volume prune` - 删除未使用的数据卷

### 数据卷使用

- `docker run -v <卷名>:<容器路径> <镜像名>` - 使用命名卷
- `docker run -v <主机路径>:<容器路径> <镜像名>` - 使用绑定挂载
- `docker run --mount source=<卷名>,target=<容器路径> <镜像名>` - 使用mount语法

## 7. 网络管理

### 网络操作

- `docker network create <网络名>` - 创建网络
- `docker network ls` - 列出所有网络
- `docker network inspect <网络名>` - 查看网络详细信息
- `docker network rm <网络名>` - 删除网络
- `docker network prune` - 删除未使用的网络

### 容器网络连接

- `docker network connect <网络名> <容器名>` - 将容器连接到网络
- `docker network disconnect <网络名> <容器名>` - 断开容器与网络的连接
- `docker run --network <网络名> <镜像名>` - 在指定网络中运行容器

## 8. Docker Compose命令

### 服务管理

- `docker-compose up` - 启动所有服务
- `docker-compose up -d` - 后台启动所有服务
- `docker-compose down` - 停止并删除所有服务
- `docker-compose start` - 启动已存在的服务
- `docker-compose stop` - 停止服务
- `docker-compose restart` - 重启服务

### 服务监控

- `docker-compose ps` - 显示服务状态
- `docker-compose logs` - 查看服务日志
- `docker-compose logs -f <服务名>` - 实时跟踪指定服务日志
- `docker-compose top` - 显示服务进程

### 服务操作

- `docker-compose build` - 构建服务镜像
- `docker-compose pull` - 拉取服务镜像
- `docker-compose exec <服务名> <命令>` - 在运行的服务中执行命令
- `docker-compose scale <服务名>=<数量>` - 扩缩服务实例数量

## 9. 系统清理命令

### 全面清理

- `docker system prune` - 删除未使用的镜像、容器、网络
- `docker system prune -a` - 删除所有未使用的镜像、容器、网络、构建缓存
- `docker system prune --volumes` - 同时删除未使用的数据卷

### 分类清理

- `docker container prune` - 删除所有停止的容器
- `docker image prune` - 删除未使用的镜像
- `docker image prune -a` - 删除所有未使用的镜像
- `docker volume prune` - 删除未使用的数据卷
- `docker network prune` - 删除未使用的网络

## 10. 高级和调试命令

### 镜像分析

- `docker diff <容器ID>` - 显示容器文件系统的变化
- `docker port <容器ID>` - 显示容器端口映射
- `docker wait <容器ID>` - 等待容器停止并返回退出码

### 事件监控

- `docker events` - 实时显示Docker事件
- `docker events --filter container=<容器名>` - 过滤特定容器的事件
- `docker events --since="2023-01-01"` - 显示指定时间后的事件

### 资源限制

- `docker run -m 512m <镜像名>` - 限制容器内存使用
- `docker run --cpus="1.5" <镜像名>` - 限制容器CPU使用
- `docker run --memory-swap="1g" <镜像名>` - 设置内存和swap总限制

## 常用组合示例

### 批量操作

```bash
# 停止所有容器
docker stop $(docker ps -q)

# 删除所有容器
docker rm $(docker ps -aq)

# 删除所有镜像
docker rmi $(docker images -q)

# 删除所有悬空镜像
docker rmi $(docker images -f "dangling=true" -q)
```

### 实用技巧

```bash
# 以只读方式挂载卷
docker run -v /host/path:/container/path:ro <镜像名>

# 设置环境变量
docker run -e ENV_VAR=value <镜像名>

# 限制日志大小
docker run --log-opt max-size=10m --log-opt max-file=3 <镜像名>

# 设置重启策略
docker run --restart=always <镜像名>
```

这些命令涵盖了Docker日常使用的大部分场景，熟练掌握这些命令可以大大提高Docker使用效率。