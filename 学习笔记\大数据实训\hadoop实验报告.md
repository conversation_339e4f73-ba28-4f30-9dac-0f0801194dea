# 大数据分析实训报告

**班级：** _____________  
**学号：** _____________  
**姓名：** _____________  
**日期：** _____________

---

## 实训一：使用HDFS命令导入数据集

**任务目标：**
- 掌握HDFS分布式文件系统的基本架构和原理
- 熟练使用HDFS Shell命令进行文件操作（创建目录、上传文件、查看文件等）
- 完成高速公路数据集（6个CSV文件）从本地文件系统到HDFS的导入
- 建立规范的HDFS目录结构，为后续数据处理奠定基础

**操作步骤：**

1. **准备本地数据目录**
   - 在master节点创建本地暂存目录
   - 将所有CSV数据文件上传至该目录
   - 验证文件完整性

2. **创建HDFS目录结构**
   - 设计合理的目录层级
   - 创建项目专用目录
   - 设置适当的权限

3. **批量上传数据文件**
   - 使用hdfs dfs -put命令上传各数据文件
   - 验证上传结果
   - 检查文件大小和完整性

**代码示例：**

```bash
# 1. 创建本地数据暂存目录
mkdir -p /root/highway_data
cd /root/highway_data

# 2. 查看本地文件列表
ls -lrt
# 输出应包含：vehicle_trails.csv, vehicle_type.csv, gantry.csv, 
# section.csv, toll.csv, service.csv

# 3. 创建HDFS项目目录
hdfs dfs -mkdir -p /user/hadoop/highway_data

# 4. 批量上传所有CSV文件到HDFS
hdfs dfs -put *.csv /user/hadoop/highway_data/

# 5. 验证上传结果
hdfs dfs -ls -h /user/hadoop/highway_data/
# 输出示例：
# -rw-r--r--   3 <USER> <GROUP>    125.3 M 2024-12-01 10:30 /user/hadoop/highway_data/vehicle_trails.csv
# -rw-r--r--   3 <USER> <GROUP>      2.1 K 2024-12-01 10:30 /user/hadoop/highway_data/vehicle_type.csv
# ...

# 6. 查看文件内容示例（检查数据格式）
hdfs dfs -head /user/hadoop/highway_data/gantry.csv
hdfs dfs -tail /user/hadoop/highway_data/vehicle_trails.csv
```

**截图说明：HDFS数据上传截图**
- 截图1：执行`hdfs dfs -ls -h /user/hadoop/highway_data/`命令后的输出结果
- 截图2：HDFS Web UI (http://master:9870) 中显示的文件列表
- 截图3：使用`hdfs dfs -tail`命令查看数据文件内容

---

## 实训二：使用MR程序统计无效车牌数量

**任务目标：**
- 理解MapReduce编程模型的核心思想（Map阶段和Reduce阶段）
- 使用Java编写MapReduce程序处理vehicle_trails.csv数据
- 通过正则表达式识别和统计无效车牌（非标准格式、空值、测试车牌等）
- 掌握在IDEA中开发和提交MapReduce作业的完整流程

**MR代码：**

```java
// InvalidPlateMapper.java
package com.highway.mapreduce;

import org.apache.hadoop.io.IntWritable;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;
import java.io.IOException;
import java.util.regex.Pattern;

public class InvalidPlateMapper extends Mapper<LongWritable, Text, Text, IntWritable> {
    
    private static final IntWritable one = new IntWritable(1);
    private Text invalidPlate = new Text();
    
    // 合法车牌正则表达式
    private static final Pattern VALID_PLATE_PATTERN = Pattern.compile(
        "^(WJ)?[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领]" +
        "[A-Z][A-Z0-9]{4}[A-Z0-9挂学警港澳]$"
    );
    
    @Override
    protected void map(LongWritable key, Text value, Context context) 
            throws IOException, InterruptedException {
        
        String line = value.toString();
        String[] fields = line.split(",");
        
        // 假设车牌号在第3个字段（索引2）
        if (fields.length > 2) {
            String vlp = fields[2].trim();
            
            // 检查是否为无效车牌
            if (vlp.isEmpty() || 
                vlp.equals("null") || 
                vlp.equals("无车牌") ||
                vlp.equals("未知") ||
                !VALID_PLATE_PATTERN.matcher(vlp).matches()) {
                
                // 对于空车牌，统一标记为"空车牌"
                if (vlp.isEmpty()) {
                    invalidPlate.set("空车牌");
                } else {
                    invalidPlate.set(vlp);
                }
                
                context.write(invalidPlate, one);
            }
        }
    }
}

// InvalidPlateReducer.java
package com.highway.mapreduce;

import org.apache.hadoop.io.IntWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;
import java.io.IOException;

public class InvalidPlateReducer extends Reducer<Text, IntWritable, Text, IntWritable> {
    
    private IntWritable result = new IntWritable();
    
    @Override
    protected void reduce(Text key, Iterable<IntWritable> values, Context context) 
            throws IOException, InterruptedException {
        
        int sum = 0;
        for (IntWritable val : values) {
            sum += val.get();
        }
        
        result.set(sum);
        context.write(key, result);
    }
}

// InvalidPlateDriver.java
package com.highway.mapreduce;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.conf.Configured;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.IntWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.FileInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.util.Tool;
import org.apache.hadoop.util.ToolRunner;

public class InvalidPlateDriver extends Configured implements Tool {
    
    @Override
    public int run(String[] args) throws Exception {
        Configuration conf = getConf();
        Job job = Job.getInstance(conf, "Invalid Plate Count");
        
        job.setJarByClass(InvalidPlateDriver.class);
        job.setMapperClass(InvalidPlateMapper.class);
        job.setCombinerClass(InvalidPlateReducer.class);
        job.setReducerClass(InvalidPlateReducer.class);
        
        job.setOutputKeyClass(Text.class);
        job.setOutputValueClass(IntWritable.class);
        
        FileInputFormat.addInputPath(job, 
            new Path("/user/hadoop/highway_data/vehicle_trails.csv"));
        FileOutputFormat.setOutputPath(job, 
            new Path("/user/hadoop/highway_output/invalid_plates"));
        
        return job.waitForCompletion(true) ? 0 : 1;
    }
    
    public static void main(String[] args) throws Exception {
        int res = ToolRunner.run(new Configuration(), new InvalidPlateDriver(), args);
        System.exit(res);
    }
}
```

**截图说明：MR运行结果截图**
- 截图1：IDEA中MapReduce程序的项目结构
- 截图2：程序运行时的控制台输出（显示Map和Reduce进度）
- 截图3：HDFS输出目录中的结果文件内容（hdfs dfs -cat输出）

---

## 实训三：使用Hive进行数据表创建数据初始化

**任务目标：**
- 在Hive中创建highway数据库，建立数据仓库基础
- 根据业务需求设计合理的表结构，包含字段类型、注释等元数据
- 区分内部表和外部表的使用场景（vehicle_trails使用外部表）
- 将HDFS中的CSV数据加载到对应的Hive表中，完成数据初始化

**DDL语句：**

```sql
-- 1. 创建数据库
CREATE DATABASE IF NOT EXISTS highway
COMMENT '高速公路数据分析数据仓库'
LOCATION '/user/hive/warehouse/highway.db';

USE highway;

-- 2. 创建车辆类型维度表（内部表）
CREATE TABLE IF NOT EXISTS ods_gsgl_vehicle_type (
    vehicle_type_code STRING COMMENT '车型编码',
    vehicle_type_name STRING COMMENT '车型名称',
    vehicle_class STRING COMMENT '车辆类别',
    axle_count INT COMMENT '轴数',
    weight_limit DOUBLE COMMENT '限重(吨)',
    fee_coefficient DOUBLE COMMENT '收费系数'
) COMMENT '车辆类型字典表'
ROW FORMAT DELIMITED 
FIELDS TERMINATED BY ','
STORED AS TEXTFILE;

-- 3. 创建门架信息表（内部表）
CREATE TABLE IF NOT EXISTS ods_gsgl_gantry (
    gantry_code STRING COMMENT '门架编码',
    gantry_name STRING COMMENT '门架名称',
    gantry_type STRING COMMENT '门架类型',
    road_code STRING COMMENT '路线编码',
    section_code STRING COMMENT '路段编码',
    pile_no STRING COMMENT '桩号',
    longitude DOUBLE COMMENT '经度',
    latitude DOUBLE COMMENT '纬度',
    direction STRING COMMENT '方向'
) COMMENT '门架基础信息表'
ROW FORMAT DELIMITED 
FIELDS TERMINATED BY ','
STORED AS TEXTFILE;

-- 4. 创建路段信息表（内部表）
CREATE TABLE IF NOT EXISTS ods_gsgl_section (
    section_code STRING COMMENT '路段编码',
    section_name STRING COMMENT '路段名称',
    road_code STRING COMMENT '路线编码',
    road_name STRING COMMENT '路线名称',
    start_pile_no STRING COMMENT '起始桩号',
    end_pile_no STRING COMMENT '结束桩号',
    section_length DOUBLE COMMENT '路段长度(公里)',
    lane_count INT COMMENT '车道数',
    design_speed INT COMMENT '设计时速'
) COMMENT '路段基础信息表'
ROW FORMAT DELIMITED 
FIELDS TERMINATED BY ','
STORED AS TEXTFILE;

-- 5. 创建收费站信息表（内部表）
CREATE TABLE IF NOT EXISTS ods_gsgl_toll (
    toll_code STRING COMMENT '收费站编码',
    toll_name STRING COMMENT '收费站名称',
    toll_type STRING COMMENT '收费站类型',
    road_code STRING COMMENT '路线编码',
    section_code STRING COMMENT '路段编码',
    lane_count INT COMMENT '车道数',
    etc_lane_count INT COMMENT 'ETC车道数'
) COMMENT '收费站基础信息表'
ROW FORMAT DELIMITED 
FIELDS TERMINATED BY ','
STORED AS TEXTFILE;

-- 6. 创建服务区信息表（内部表）
CREATE TABLE IF NOT EXISTS ods_gsgl_service (
    service_code STRING COMMENT '服务区编码',
    service_name STRING COMMENT '服务区名称',
    road_code STRING COMMENT '路线编码',
    section_code STRING COMMENT '路段编码',
    direction STRING COMMENT '方向',
    facilities STRING COMMENT '设施配置'
) COMMENT '服务区基础信息表'
ROW FORMAT DELIMITED 
FIELDS TERMINATED BY ','
STORED AS TEXTFILE;

-- 7. 创建车辆轨迹表（外部表）
CREATE EXTERNAL TABLE IF NOT EXISTS ods_gsgl_vehicle_trails (
    trail_id STRING COMMENT '轨迹ID',
    vehicle_id STRING COMMENT '车辆ID',
    vlp STRING COMMENT '车牌号',
    identify_vtype STRING COMMENT '识别车型',
    gantry_code STRING COMMENT '门架编码',
    capture_time STRING COMMENT '通过时间',
    speed DOUBLE COMMENT '瞬时速度',
    direction STRING COMMENT '行驶方向',
    lane_no INT COMMENT '车道号',
    crossing_type STRING COMMENT '通行类型'
) COMMENT '车辆通行轨迹表'
ROW FORMAT DELIMITED 
FIELDS TERMINATED BY ','
STORED AS TEXTFILE
LOCATION '/user/hadoop/highway_data/ods_gsgl_vehicle_trails';

-- 8. 数据加载（内部表）
LOAD DATA INPATH '/user/hadoop/highway_data/vehicle_type.csv' 
INTO TABLE ods_gsgl_vehicle_type;

LOAD DATA INPATH '/user/hadoop/highway_data/gantry.csv' 
INTO TABLE ods_gsgl_gantry;

LOAD DATA INPATH '/user/hadoop/highway_data/section.csv' 
INTO TABLE ods_gsgl_section;

LOAD DATA INPATH '/user/hadoop/highway_data/toll.csv' 
INTO TABLE ods_gsgl_toll;

LOAD DATA INPATH '/user/hadoop/highway_data/service.csv' 
INTO TABLE ods_gsgl_service;

-- 9. 验证数据加载
SELECT COUNT(*) as cnt, 'vehicle_type' as table_name FROM ods_gsgl_vehicle_type
UNION ALL
SELECT COUNT(*), 'gantry' FROM ods_gsgl_gantry
UNION ALL
SELECT COUNT(*), 'section' FROM ods_gsgl_section
UNION ALL
SELECT COUNT(*), 'toll' FROM ods_gsgl_toll
UNION ALL
SELECT COUNT(*), 'service' FROM ods_gsgl_service
UNION ALL
SELECT COUNT(*), 'vehicle_trails' FROM ods_gsgl_vehicle_trails;
```

**截图说明：Hive表结构截图**
- 截图1：DBeaver连接HiveServer2的界面
- 截图2：执行`SHOW TABLES`命令显示所有创建的表
- 截图3：使用`DESCRIBE FORMATTED ods_gsgl_vehicle_trails`查看表详细结构
- 截图4：数据验证查询结果，显示各表的记录数

---

## 实训四：使用HQL进行高速公路车流量统计

**任务目标：**
- 对原始数据进行清洗和标准化，创建高质量的标准层数据表
- 实现数据去重逻辑：1分钟内同车同方向同门架只保留最早记录
- 统计路线级和路段级的总车流量，支持管理决策
- 按小时维度分析车流量变化趋势，识别高峰时段

**HQL语句：**

```sql
-- 1. 数据预处理：创建标准化车辆轨迹表
CREATE TABLE std_gsgl_vehicle_trails AS
WITH cleaned_data AS (
    SELECT 
        trail_id,
        vehicle_id,
        UPPER(TRIM(REPLACE(vlp, '"', ''))) as vlp,  -- 车牌标准化
        identify_vtype,
        gantry_code,
        -- 时间格式标准化
        FROM_UNIXTIME(
            UNIX_TIMESTAMP(capture_time, 'yyyy/MM/dd HH:mm:ss'),
            'yyyy-MM-dd HH:mm:ss'
        ) as capture_time,
        speed,
        direction,
        lane_no,
        -- 通行类型代码转换
        CASE crossing_type
            WHEN '0' THEN '正常通行'
            WHEN '1' THEN 'ETC通行'
            WHEN '2' THEN '人工收费'
            ELSE '其他'
        END as crossing_type
    FROM ods_gsgl_vehicle_trails
    WHERE 
        -- 过滤无效车牌
        vlp IS NOT NULL 
        AND vlp != ''
        AND vlp NOT IN ('null', '无车牌', '未知', '测试')
        AND vlp REGEXP '^(WJ)?[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4}[A-Z0-9挂学警港澳]$'
),
-- 添加窗口函数用于去重
windowed_data AS (
    SELECT 
        *,
        LAG(capture_time, 1) OVER (
            PARTITION BY vlp, gantry_code, direction 
            ORDER BY capture_time
        ) as prev_capture_time,
        ROW_NUMBER() OVER (
            PARTITION BY vlp, gantry_code, direction 
            ORDER BY capture_time
        ) as rn
    FROM cleaned_data
)
-- 去重：保留时间差大于60秒的记录
SELECT 
    trail_id, vehicle_id, vlp, identify_vtype, gantry_code,
    capture_time, speed, direction, lane_no, crossing_type
FROM windowed_data
WHERE rn = 1  -- 第一条记录
   OR (UNIX_TIMESTAMP(capture_time) - UNIX_TIMESTAMP(prev_capture_time)) > 60;

-- 保存重复数据用于数据质量分析
CREATE TABLE std_gsgl_vehicle_trails_repeat AS
SELECT * FROM windowed_data
WHERE rn > 1 
  AND (UNIX_TIMESTAMP(capture_time) - UNIX_TIMESTAMP(prev_capture_time)) <= 60;

-- 2. 清洗门架和路段信息
CREATE TABLE std_gsgl_gantry AS
SELECT 
    gantry_code,
    TRIM(gantry_name) as gantry_name,
    gantry_type,
    road_code,
    section_code,
    pile_no,
    -- 桩号标准化为数值
    CASE 
        WHEN pile_no REGEXP '^K[0-9]+\\+[0-9]+$' THEN
            CAST(REGEXP_EXTRACT(pile_no, 'K([0-9]+)\\+', 1) AS DECIMAL(10,3)) +
            CAST(REGEXP_EXTRACT(pile_no, '\\+([0-9]+)$', 1) AS DECIMAL(10,3)) / 1000
        ELSE NULL
    END as pile_no_km,
    longitude,
    latitude,
    direction
FROM ods_gsgl_gantry
WHERE gantry_code IS NOT NULL;

CREATE TABLE std_gsgl_section AS
SELECT 
    section_code,
    TRIM(section_name) as section_name,
    road_code,
    TRIM(road_name) as road_name,
    start_pile_no,
    end_pile_no,
    section_length,
    lane_count,
    design_speed
FROM ods_gsgl_section
WHERE section_code IS NOT NULL;

-- 3. 路线级车流量统计
CREATE TABLE dm_gsgl_road_total_flow AS
SELECT 
    s.road_code,
    s.road_name,
    COUNT(DISTINCT t.vlp) as total_vehicles,
    COUNT(t.trail_id) as total_records,
    COUNT(DISTINCT DATE(t.capture_time)) as operating_days,
    MIN(t.capture_time) as first_record_time,
    MAX(t.capture_time) as last_record_time
FROM std_gsgl_vehicle_trails t
JOIN std_gsgl_gantry g ON t.gantry_code = g.gantry_code
JOIN std_gsgl_section s ON g.section_code = s.section_code
GROUP BY s.road_code, s.road_name
ORDER BY total_vehicles DESC;

-- 4. 路段级车流量统计
CREATE TABLE dm_gsgl_section_total_flow AS
SELECT 
    s.road_code,
    s.road_name,
    s.section_code,
    s.section_name,
    s.section_length,
    COUNT(DISTINCT t.vlp) as total_vehicles,
    COUNT(t.trail_id) as total_records,
    -- 计算日均流量
    COUNT(DISTINCT t.vlp) / COUNT(DISTINCT DATE(t.capture_time)) as avg_daily_flow,
    -- 计算利用率（假设设计通行能力为每车道每小时1500辆）
    COUNT(DISTINCT t.vlp) / (s.lane_count * 1500.0 * 24 * COUNT(DISTINCT DATE(t.capture_time))) as utilization_rate
FROM std_gsgl_vehicle_trails t
JOIN std_gsgl_gantry g ON t.gantry_code = g.gantry_code
JOIN std_gsgl_section s ON g.section_code = s.section_code
GROUP BY s.road_code, s.road_name, s.section_code, s.section_name, s.section_length, s.lane_count
ORDER BY total_vehicles DESC;

-- 5. 路线级小时流量统计
CREATE TABLE dm_gsgl_road_flow_hour AS
SELECT 
    s.road_code,
    s.road_name,
    DATE_FORMAT(t.capture_time, 'yyyy-MM-dd HH:00:00') as hour_window,
    HOUR(t.capture_time) as hour_of_day,
    COUNT(DISTINCT t.vlp) as vehicles_count,
    COUNT(t.trail_id) as records_count,
    AVG(t.speed) as avg_speed
FROM std_gsgl_vehicle_trails t
JOIN std_gsgl_gantry g ON t.gantry_code = g.gantry_code
JOIN std_gsgl_section s ON g.section_code = s.section_code
GROUP BY s.road_code, s.road_name, DATE_FORMAT(t.capture_time, 'yyyy-MM-dd HH:00:00'), HOUR(t.capture_time)
ORDER BY hour_window, road_code;

-- 6. 路段级小时流量统计
CREATE TABLE dm_gsgl_section_flow_hour AS
SELECT 
    s.road_code,
    s.road_name,
    s.section_code,
    s.section_name,
    DATE_FORMAT(t.capture_time, 'yyyy-MM-dd HH:00:00') as hour_window,
    HOUR(t.capture_time) as hour_of_day,
    t.direction,
    COUNT(DISTINCT t.vlp) as vehicles_count,
    COUNT(t.trail_id) as records_count,
    AVG(t.speed) as avg_speed,
    -- 计算该小时的利用率
    COUNT(DISTINCT t.vlp) / (s.lane_count * 1500.0) as hourly_utilization
FROM std_gsgl_vehicle_trails t
JOIN std_gsgl_gantry g ON t.gantry_code = g.gantry_code
JOIN std_gsgl_section s ON g.section_code = s.section_code
GROUP BY s.road_code, s.road_name, s.section_code, s.section_name, 
         DATE_FORMAT(t.capture_time, 'yyyy-MM-dd HH:00:00'), HOUR(t.capture_time), t.direction
ORDER BY hour_window, section_code;

-- 7. 查询验证结果
-- 查看路线总流量TOP10
SELECT * FROM dm_gsgl_road_total_flow LIMIT 10;

-- 查看路段流量TOP10
SELECT * FROM dm_gsgl_section_total_flow LIMIT 10;

-- 查看某天的小时流量变化
SELECT 
    hour_of_day,
    SUM(vehicles_count) as total_vehicles,
    AVG(avg_speed) as avg_speed
FROM dm_gsgl_road_flow_hour
WHERE DATE(hour_window) = '2024-12-01'
GROUP BY hour_of_day
ORDER BY hour_of_day;
```

**截图说明：车流量统计结果**
- 截图1：路线级总流量统计结果（TOP10路线）
- 截图2：路段级流量统计结果（包含利用率）
- 截图3：24小时流量分布图（可用Excel生成）
- 截图4：数据质量报告（去重前后数据量对比）

---

## 实训五：使用HQL进行高速公路拥堵分析

**任务目标：**
- 计算相邻门架间的车辆平均行驶速度，识别低速路段
- 设计并实现拥堵指数(TI)计算模型，综合考虑速度和流量因素
- 识别每日高峰时段，为交通管理提供决策依据
- 定位门架级拥堵热点，支持精准疏导

**HQL语句：**

```sql
-- 1. 创建车辆轨迹排序表（为计算相邻门架速度做准备）
CREATE TABLE dm_gsgl_ranked_trails AS
SELECT 
    t.vlp,
    t.vehicle_id,
    t.gantry_code,
    g.gantry_name,
    g.pile_no_km,
    t.capture_time,
    t.direction,
    t.speed as instant_speed,
    ROW_NUMBER() OVER (
        PARTITION BY t.vlp, t.direction 
        ORDER BY t.capture_time
    ) as rn
FROM std_gsgl_vehicle_trails t
JOIN std_gsgl_gantry g ON t.gantry_code = g.gantry_code
WHERE g.pile_no_km IS NOT NULL  -- 确保有有效的桩号
  AND g.gantry_type = '1';       -- 只考虑主线门架

-- 2. 计算相邻门架间平均速度
CREATE TABLE dm_gsgl_speed_trails AS
SELECT 
    t1.vlp,
    t1.direction,
    t1.gantry_code as from_gantry,
    t1.gantry_name as from_gantry_name,
    t2.gantry_code as to_gantry,
    t2.gantry_name as to_gantry_name,
    t1.capture_time as start_time,
    t2.capture_time as end_time,
    -- 计算距离（公里）
    ABS(t2.pile_no_km - t1.pile_no_km) as distance_km,
    -- 计算时间差（秒）
    UNIX_TIMESTAMP(t2.capture_time) - UNIX_TIMESTAMP(t1.capture_time) as time_seconds,
    -- 计算平均速度（km/h）
    CASE 
        WHEN (UNIX_TIMESTAMP(t2.capture_time) - UNIX_TIMESTAMP(t1.capture_time)) > 0 THEN
            (ABS(t2.pile_no_km - t1.pile_no_km) / 
             ((UNIX_TIMESTAMP(t2.capture_time) - UNIX_TIMESTAMP(t1.capture_time)) / 3600.0))
        ELSE NULL
    END as avg_speed_kmph
FROM dm_gsgl_ranked_trails t1
JOIN dm_gsgl_ranked_trails t2
    ON t1.vlp = t2.vlp 
    AND t1.direction = t2.direction
    AND t2.rn = t1.rn + 1
WHERE 
    -- 确保时间差合理（大于0，小于30分钟）
    (UNIX_TIMESTAMP(t2.capture_time) - UNIX_TIMESTAMP(t1.capture_time)) > 0
    AND (UNIX_TIMESTAMP(t2.capture_time) - UNIX_TIMESTAMP(t1.capture_time)) < 1800
    -- 确保距离合理（大于0.5公里）
    AND ABS(t2.pile_no_km - t1.pile_no_km) > 0.5;

-- 过滤异常速度数据
CREATE TABLE dm_gsgl_speed_trails_clean AS
SELECT * FROM dm_gsgl_speed_trails
WHERE avg_speed_kmph BETWEEN 10 AND 200;  -- 合理速度范围

-- 3. 计算拥堵指数
CREATE TABLE dm_gsgl_congestion_index AS
WITH hourly_stats AS (
    SELECT 
        g.road_code,
        s.road_name,
        g.section_code,
        s.section_name,
        g.gantry_code,
        g.gantry_name,
        t.direction,
        DATE_FORMAT(t.capture_time, 'yyyy-MM-dd HH:00:00') as hour_window,
        HOUR(t.capture_time) as hour_of_day,
        -- 统计车流量
        COUNT(DISTINCT t.vlp) as traffic_volume,
        -- 获取平均速度
        AVG(sp.avg_speed_kmph) as avg_speed
    FROM std_gsgl_vehicle_trails t
    JOIN std_gsgl_gantry g ON t.gantry_code = g.gantry_code
    JOIN std_gsgl_section s ON g.section_code = s.section_code
    LEFT JOIN dm_gsgl_speed_trails_clean sp 
        ON t.vlp = sp.vlp 
        AND t.gantry_code = sp.from_gantry
        AND ABS(UNIX_TIMESTAMP(t.capture_time) - UNIX_TIMESTAMP(sp.start_time)) < 300
    GROUP BY g.road_code, s.road_name, g.section_code, s.section_name,
             g.gantry_code, g.gantry_name, t.direction,
             DATE_FORMAT(t.capture_time, 'yyyy-MM-dd HH:00:00'), HOUR(t.capture_time)
)
SELECT 
    road_code,
    road_name,
    section_code,
    section_name,
    gantry_code,
    gantry_name,
    direction,
    hour_window,
    hour_of_day,
    traffic_volume,
    avg_speed,
    -- 计算速度因子（满分100）
    CASE 
        WHEN avg_speed IS NULL THEN 50  -- 无速度数据时给中等分
        WHEN avg_speed >= 100 THEN 0    -- 速度正常
        WHEN avg_speed <= 20 THEN 100   -- 严重拥堵
        ELSE (120 - avg_speed) / 120 * 100
    END as speed_factor,
    -- 计算流量因子（满分100）
    LEAST(traffic_volume / 1500.0 * 100, 100) as volume_factor,
    -- 综合拥堵指数（0-100，越高越拥堵）
    (CASE 
        WHEN avg_speed IS NULL THEN 50
        WHEN avg_speed >= 100 THEN 0
        WHEN avg_speed <= 20 THEN 100
        ELSE (120 - avg_speed) / 120 * 100
    END * 0.7 +  -- 速度权重70%
    LEAST(traffic_volume / 1500.0 * 100, 100) * 0.3) as congestion_index,
    -- 拥堵等级
    CASE 
        WHEN (CASE 
                WHEN avg_speed IS NULL THEN 50
                WHEN avg_speed >= 100 THEN 0
                WHEN avg_speed <= 20 THEN 100
                ELSE (120 - avg_speed) / 120 * 100
              END * 0.7 + 
              LEAST(traffic_volume / 1500.0 * 100, 100) * 0.3) >= 80 THEN '严重拥堵'
        WHEN (CASE 
                WHEN avg_speed IS NULL THEN 50
                WHEN avg_speed >= 100 THEN 0
                WHEN avg_speed <= 20 THEN 100
                ELSE (120 - avg_speed) / 120 * 100
              END * 0.7 + 
              LEAST(traffic_volume / 1500.0 * 100, 100) * 0.3) >= 60 THEN '中度拥堵'
        WHEN (CASE 
                WHEN avg_speed IS NULL THEN 50
                WHEN avg_speed >= 100 THEN 0
                WHEN avg_speed <= 20 THEN 100
                ELSE (120 - avg_speed) / 120 * 100
              END * 0.7 + 
              LEAST(traffic_volume / 1500.0 * 100, 100) * 0.3) >= 40 THEN '轻度拥堵'
        ELSE '畅通'
    END as congestion_level
FROM hourly_stats;

-- 4. 识别高峰时段（按小时统计）
CREATE TABLE dm_gsgl_peak_hours AS
SELECT 
    hour_of_day,
    AVG(traffic_volume) as avg_traffic_volume,
    AVG(avg_speed) as avg_speed,
    AVG(congestion_index) as avg_congestion_index,
    COUNT(DISTINCT DATE(hour_window)) as days_count,
    -- 标记高峰时段
    CASE 
        WHEN hour_of_day BETWEEN 7 AND 9 THEN '早高峰'
        WHEN hour_of_day BETWEEN 17 AND 19 THEN '晚高峰'
        WHEN hour_of_day BETWEEN 11 AND 13 THEN '午间'
        WHEN hour_of_day BETWEEN 22 AND 23 OR hour_of_day BETWEEN 0 AND 5 THEN '夜间'
        ELSE '平峰'
    END as period_type
FROM dm_gsgl_congestion_index
GROUP BY hour_of_day
ORDER BY avg_congestion_index DESC;

-- 5. 门架级拥堵热点TOP20
CREATE TABLE dm_gsgl_gantry_congestion_hotspot AS
SELECT 
    gantry_code,
    gantry_name,
    road_name,
    section_name,
    AVG(traffic_volume) as avg_traffic_volume,
    AVG(avg_speed) as avg_speed,
    AVG(congestion_index) as avg_congestion_index,
    STDDEV(congestion_index) as congestion_volatility,  -- 拥堵波动性
    COUNT(CASE WHEN congestion_index >= 60 THEN 1 END) as congestion_hours,
    COUNT(*) as total_hours,
    COUNT(CASE WHEN congestion_index >= 60 THEN 1 END) / COUNT(*) as congestion_frequency
FROM dm_gsgl_congestion_index
GROUP BY gantry_code, gantry_name, road_name, section_name
HAVING COUNT(*) >= 24  -- 至少有一天的数据
ORDER BY avg_congestion_index DESC
LIMIT 20;

-- 6. 低速路段识别（平均速度低于80km/h）
CREATE TABLE dm_gsgl_low_speed_sections AS
SELECT 
    section_code,
    section_name,
    road_name,
    direction,
    AVG(avg_speed) as avg_speed,
    MIN(avg_speed) as min_speed,
    PERCENTILE(avg_speed, 0.25) as speed_p25,
    PERCENTILE(avg_speed, 0.5) as speed_p50,
    PERCENTILE(avg_speed, 0.75) as speed_p75,
    COUNT(*) as sample_count,
    SUM(CASE WHEN avg_speed < 60 THEN 1 ELSE 0 END) as severe_slow_count,
    SUM(CASE WHEN avg_speed < 60 THEN 1 ELSE 0 END) / COUNT(*) as severe_slow_ratio
FROM dm_gsgl_congestion_index
WHERE avg_speed IS NOT NULL
GROUP BY section_code, section_name, road_name, direction
HAVING AVG(avg_speed) < 80
ORDER BY avg_speed ASC;

-- 7. 查询验证
-- 查看24小时拥堵指数变化
SELECT * FROM dm_gsgl_peak_hours ORDER BY hour_of_day;

-- 查看拥堵热点门架
SELECT 
    gantry_name,
    road_name,
    section_name,
    ROUND(avg_congestion_index, 2) as avg_ti,
    ROUND(congestion_frequency * 100, 2) as congestion_pct
FROM dm_gsgl_gantry_congestion_hotspot
LIMIT 10;

-- 查看某个时段的拥堵分布
SELECT 
    congestion_level,
    COUNT(*) as gantry_count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM dm_gsgl_congestion_index
WHERE hour_of_day = 8  -- 早高峰8点
GROUP BY congestion_level
ORDER BY 
    CASE congestion_level
        WHEN '严重拥堵' THEN 1
        WHEN '中度拥堵' THEN 2
        WHEN '轻度拥堵' THEN 3
        WHEN '畅通' THEN 4
    END;
```

**截图说明：拥堵分析统计结果**
- 截图1：24小时拥堵指数变化曲线（显示早晚高峰）
- 截图2：门架拥堵热点TOP10列表
- 截图3：低速路段分布（包含速度箱线图信息）
- 截图4：某时段拥堵等级分布饼图

---

## 实训六：使用Spark进行高速公路车型差异分析

**任务目标：**
- 使用Spark Core读取HDFS中的标准化车辆轨迹数据
- 正确处理Hive表数据的特殊分隔符（\001）
- 实现车型编码到中文名称的映射转换
- 统计不同车型的通行数量并输出分析结果

**Spark代码：**

```scala
// VehicleTypeCount.scala
package com.highway.scala

import org.apache.spark.{SparkConf, SparkContext}
import org.apache.spark.rdd.RDD

object VehicleTypeCount {
  
  def main(args: Array[String]): Unit = {
    // 1. 创建Spark配置和上下文
    val conf = new SparkConf()
      .setAppName("Highway Vehicle Type Analysis")
      .setMaster("yarn")  // 提交到YARN集群
      .set("spark.executor.memory", "2g")
      .set("spark.executor.cores", "2")
      .set("spark.default.parallelism", "8")
    
    val sc = new SparkContext(conf)
    sc.setLogLevel("WARN")  // 减少日志输出
    
    // 2. 定义车型编码映射关系
    val vehicleTypeMap = Map(
      "1" -> "一型客车(7座以下)",
      "2" -> "二型客车(8-19座)",
      "3" -> "三型客车(20-39座)",
      "4" -> "四型客车(40座以上)",
      "11" -> "一型货车(2轴)",
      "12" -> "二型货车(2轴)",
      "13" -> "三型货车(3轴)",
      "14" -> "四型货车(4轴)",
      "15" -> "五型货车(5轴)",
      "16" -> "六型货车(6轴及以上)",
      "21" -> "一型专项作业车",
      "22" -> "二型专项作业车",
      "23" -> "三型专项作业车",
      "24" -> "四型专项作业车",
      "25" -> "五型专项作业车",
      "26" -> "六型专项作业车"
    )
    
    // 广播变量优化
    val vehicleTypeMapBC = sc.broadcast(vehicleTypeMap)
    
    try {
      // 3. 读取HDFS数据（注意Hive表的分隔符是\001）
      val inputPath = "/user/hive/warehouse/highway.db/std_gsgl_vehicle_trails"
      val outputPath = "/user/hadoop/highway_output/vehicle_type_count"
      
      // 删除输出目录（如果存在）
      val fs = org.apache.hadoop.fs.FileSystem.get(sc.hadoopConfiguration)
      val outPath = new org.apache.hadoop.fs.Path(outputPath)
      if (fs.exists(outPath)) {
        fs.delete(outPath, true)
      }
      
      // 4. 读取和处理数据
      val vehicleData: RDD[String] = sc.textFile(inputPath)
      
      // 5. 数据转换和统计
      val vehicleTypeCounts = vehicleData
        .map(line => line.split("\001", -1))  // 使用\001分隔符
        .filter(fields => fields.length > 3)   // 确保有足够的字段
        .map(fields => {
          val vtype = fields(3).trim  // identify_vtype字段位置
          vtype
        })
        .filter(vtype => vtype.nonEmpty && vtype != "null")  // 过滤空值
        .map(vtype => {
          // 映射车型编码到中文名称
          val typeName = vehicleTypeMapBC.value.getOrElse(vtype, s"未知类型($vtype)")
          (typeName, 1)
        })
        .reduceByKey(_ + _)  // 按车型聚合计数
        .sortBy(_._2, ascending = false)  // 按数量降序排序
      
      // 6. 计算统计信息
      val totalVehicles = vehicleTypeCounts.map(_._2).sum()
      val vehicleTypeStats = vehicleTypeCounts.map { case (vtype, count) =>
        val percentage = count * 100.0 / totalVehicles
        (vtype, count, f"$percentage%.2f%%")
      }.collect()
      
      // 7. 输出到控制台
      println("\n" + "="*60)
      println("高速公路车型分布统计分析")
      println("="*60)
      println(f"${"车型名称"}%-25s ${"通行数量"}%10s ${"占比"}%8s")
      println("-"*60)
      
      vehicleTypeStats.foreach { case (vtype, count, pct) =>
        println(f"$vtype%-25s $count%10d $pct%8s")
      }
      
      println("-"*60)
      println(f"总计: $totalVehicles 辆次")
      println("="*60)
      
      // 8. 保存结果到HDFS
      val resultRDD = sc.parallelize(vehicleTypeStats.map { case (vtype, count, pct) =>
        s"$vtype\t$count\t$pct"
      })
      
      // 添加标题行
      val headerRDD = sc.parallelize(Seq("车型名称\t通行数量\t占比"))
      val finalRDD = headerRDD.union(resultRDD)
      
      // 合并为单个文件输出
      finalRDD.coalesce(1).saveAsTextFile(outputPath)
      
      println(s"\n结果已保存到: $outputPath")
      
      // 9. 额外分析：按车辆类别统计
      val vehicleCategoryStats = vehicleTypeCounts
        .map { case (vtype, count) =>
          val category = vtype match {
            case t if t.contains("客车") => "客车"
            case t if t.contains("货车") => "货车"
            case t if t.contains("专项作业车") => "专项作业车"
            case _ => "其他"
          }
          (category, count)
        }
        .reduceByKey(_ + _)
        .collect()
        .sortBy(_._2)(Ordering[Int].reverse)
      
      println("\n" + "="*60)
      println("车辆类别统计")
      println("="*60)
      vehicleCategoryStats.foreach { case (category, count) =>
        val pct = count * 100.0 / totalVehicles
        println(f"$category%-15s: $count%10d (${pct}%.2f%%)")
      }
      println("="*60)
      
    } catch {
      case e: Exception =>
        println(s"程序执行出错: ${e.getMessage}")
        e.printStackTrace()
    } finally {
      // 10. 关闭SparkContext
      sc.stop()
    }
  }
}
```

**Maven配置（pom.xml）：**
```xml
<project>
    <groupId>com.highway</groupId>
    <artifactId>spark-highway</artifactId>
    <version>1.0</version>
    
    <properties>
        <scala.version>2.12.10</scala.version>
        <spark.version>3.1.1</spark.version>
    </properties>
    
    <dependencies>
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-core_2.12</artifactId>
            <version>${spark.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
            <version>${scala.version}</version>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
                <version>4.4.0</version>
            </plugin>
        </plugins>
    </build>
</project>
```

**截图说明：车型分布输出结果截图**
- 截图1：IDEA项目结构，显示Scala代码文件
- 截图2：程序运行时的Spark作业进度
- 截图3：控制台输出的车型统计结果
- 截图4：HDFS输出文件内容（使用hdfs dfs -cat查看）

---

## 实训七：使用SparkSQL进行高速交通状况画像构建

**任务目标：**
- 通过Spark Thrift Server连接Hive Metastore，实现数据共享
- 从时间、空间、性能三个维度构建交通状况全景画像
- 使用PySpark进行数据查询和处理
- 利用PyEcharts生成交互式可视化大屏，直观展示分析结果

**SparkSQL与Python代码：**

```python
# highway_traffic_dashboard.py
from pyspark.sql import SparkSession
from pyspark.sql.functions import *
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Line, Geo, Bar, Page, Pie, Map, HeatMap
from pyecharts.globals import ChartType, ThemeType
from pyecharts.commons.utils import JsCode
import json

# 1. 创建SparkSession
spark = SparkSession.builder \
    .appName("HighwayTrafficDashboard") \
    .config("spark.sql.warehouse.dir", "/user/hive/warehouse") \
    .config("hive.metastore.uris", "thrift://slave2:9083") \
    .config("spark.sql.adaptive.enabled", "true") \
    .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
    .enableHiveSupport() \
    .getOrCreate()

spark.sparkContext.setLogLevel("WARN")

# 2. 数据准备SQL
# 时间维度分析
hourly_traffic_sql = """
SELECT 
    hour_of_day,
    AVG(vehicles_count) as avg_vehicles,
    AVG(avg_speed) as avg_speed,
    AVG(congestion_index) as avg_congestion_index,
    CASE 
        WHEN hour_of_day BETWEEN 7 AND 9 THEN '早高峰'
        WHEN hour_of_day BETWEEN 17 AND 19 THEN '晚高峰'
        WHEN hour_of_day BETWEEN 11 AND 13 THEN '午间'
        WHEN hour_of_day BETWEEN 22 AND 23 OR hour_of_day BETWEEN 0 AND 5 THEN '夜间'
        ELSE '平峰'
    END as period_type
FROM highway.dm_gsgl_road_flow_hour rh
JOIN (
    SELECT hour_of_day as hod, AVG(congestion_index) as congestion_index
    FROM highway.dm_gsgl_congestion_index
    GROUP BY hour_of_day
) ci ON rh.hour_of_day = ci.hod
GROUP BY hour_of_day
ORDER BY hour_of_day
"""

# 空间维度分析 - 路段热力数据
section_heatmap_sql = """
SELECT 
    s.section_name,
    s.section_code,
    AVG(sf.vehicles_count) as avg_traffic,
    AVG(ci.congestion_index) as avg_congestion,
    AVG(g.longitude) as longitude,
    AVG(g.latitude) as latitude,
    COUNT(DISTINCT DATE(sf.hour_window)) as days
FROM highway.dm_gsgl_section_flow_hour sf
JOIN highway.std_gsgl_section s ON sf.section_code = s.section_code
JOIN highway.std_gsgl_gantry g ON s.section_code = g.section_code
LEFT JOIN highway.dm_gsgl_congestion_index ci 
    ON sf.section_code = ci.section_code 
    AND sf.hour_window = ci.hour_window
GROUP BY s.section_name, s.section_code
HAVING days >= 1
"""

# 瓶颈识别 - 低速路段TOP10
bottleneck_sql = """
SELECT 
    section_name,
    road_name,
    ROUND(avg_speed, 2) as avg_speed,
    ROUND(severe_slow_ratio * 100, 2) as severe_slow_pct,
    sample_count
FROM highway.dm_gsgl_low_speed_sections
ORDER BY avg_speed ASC
LIMIT 10
"""

# 拥堵热点门架TOP10
congestion_hotspot_sql = """
SELECT 
    gantry_name,
    section_name,
    ROUND(avg_congestion_index, 2) as avg_ti,
    ROUND(congestion_frequency * 100, 2) as congestion_pct,
    congestion_hours
FROM highway.dm_gsgl_gantry_congestion_hotspot
LIMIT 10
"""

# 3. 执行查询并转换为Pandas DataFrame
print("正在查询数据...")
hourly_df = spark.sql(hourly_traffic_sql).toPandas()
section_df = spark.sql(section_heatmap_sql).toPandas()
bottleneck_df = spark.sql(bottleneck_sql).toPandas()
hotspot_df = spark.sql(congestion_hotspot_sql).toPandas()

# 4. 创建可视化图表
def create_hourly_traffic_chart():
    """24小时交通流量与拥堵指数变化图"""
    hours = [f"{h:02d}:00" for h in hourly_df['hour_of_day']]
    
    line = Line(init_opts=opts.InitOpts(theme=ThemeType.DARK, width="100%", height="400px"))
    line.add_xaxis(hours)
    
    # 添加双Y轴
    line.extend_axis(
        yaxis=opts.AxisOpts(
            name="拥堵指数",
            type_="value",
            min_=0,
            max_=100,
            position="right",
            axisline_opts=opts.AxisLineOpts(
                linestyle_opts=opts.LineStyleOpts(color="#FF6B6B")
            ),
            axislabel_opts=opts.LabelOpts(formatter="{value}")
        )
    )
    
    line.add_yaxis(
        "车流量",
        hourly_df['avg_vehicles'].round().tolist(),
        yaxis_index=0,
        symbol_size=8,
        is_smooth=True,
        label_opts=opts.LabelOpts(is_show=False),
        itemstyle_opts=opts.ItemStyleOpts(color="#4ECDC4"),
        areastyle_opts=opts.AreaStyleOpts(opacity=0.3)
    )
    
    line.add_yaxis(
        "拥堵指数",
        hourly_df['avg_congestion_index'].round(2).tolist(),
        yaxis_index=1,
        symbol_size=8,
        is_smooth=True,
        label_opts=opts.LabelOpts(is_show=False),
        itemstyle_opts=opts.ItemStyleOpts(color="#FF6B6B"),
        linestyle_opts=opts.LineStyleOpts(width=3)
    )
    
    line.add_yaxis(
        "平均速度",
        hourly_df['avg_speed'].round().tolist(),
        yaxis_index=0,
        symbol_size=6,
        is_smooth=True,
        label_opts=opts.LabelOpts(is_show=False),
        itemstyle_opts=opts.ItemStyleOpts(color="#95E1D3"),
        linestyle_opts=opts.LineStyleOpts(width=2, type_="dashed")
    )
    
    line.set_global_opts(
        title_opts=opts.TitleOpts(
            title="24小时交通流量与拥堵指数分析",
            subtitle="早高峰(7-9点)和晚高峰(17-19点)拥堵明显",
            pos_left="center"
        ),
        tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="cross"),
        legend_opts=opts.LegendOpts(pos_top="10%"),
        yaxis_opts=opts.AxisOpts(
            name="车流量/速度",
            type_="value",
            axislabel_opts=opts.LabelOpts(formatter="{value}")
        ),
        datazoom_opts=[
            opts.DataZoomOpts(type_="inside", range_start=0, range_end=100),
            opts.DataZoomOpts(type_="slider", range_start=0, range_end=100)
        ]
    )
    
    # 添加高峰时段标记
    line.set_series_opts(
        markarea_opts=opts.MarkAreaOpts(
            data=[
                [{"xAxis": "07:00", "itemStyle": {"color": "rgba(255, 173, 177, 0.4)"}}, 
                 {"xAxis": "09:00"}],
                [{"xAxis": "17:00", "itemStyle": {"color": "rgba(255, 173, 177, 0.4)"}}, 
                 {"xAxis": "19:00"}]
            ]
        )
    )
    
    return line

def create_congestion_heatmap():
    """路段拥堵热力图"""
    # 准备热力图数据
    heat_data = []
    for _, row in section_df.iterrows():
        if pd.notna(row['longitude']) and pd.notna(row['latitude']):
            heat_data.append([
                round(row['longitude'], 6),
                round(row['latitude'], 6),
                round(row['avg_congestion'], 2)
            ])
    
    geo = Geo(init_opts=opts.InitOpts(theme=ThemeType.DARK, width="100%", height="500px"))
    geo.add_schema(
        maptype="china",
        itemstyle_opts=opts.ItemStyleOpts(color="#323c48", border_color="#111"),
        label_opts=opts.LabelOpts(is_show=False)
    )
    
    # 添加热力图层
    geo.add(
        "拥堵指数",
        heat_data,
        type_=ChartType.HEATMAP,
        is_large=True,
        blur_size=15,
        point_size=10,
    )
    
    geo.set_series_opts(label_opts=opts.LabelOpts(is_show=False))
    geo.set_global_opts(
        title_opts=opts.TitleOpts(
            title="高速公路拥堵热力分布图",
            subtitle="颜色越深表示拥堵越严重",
            pos_left="center"
        ),
        visualmap_opts=opts.VisualMapOpts(
            min_=0,
            max_=100,
            is_calculable=True,
            pos_left="left",
            pos_bottom="bottom",
            textstyle_opts=opts.TextStyleOpts(color="#ddd"),
            range_color=["#50A3BA", "#EAC736", "#D94E5D"]
        )
    )
    
    return geo

def create_bottleneck_ranking():
    """低速路段排行榜"""
    bar = Bar(init_opts=opts.InitOpts(theme=ThemeType.DARK, width="100%", height="400px"))
    
    # 反转数据顺序，使速度最低的在最上面
    bottleneck_df_sorted = bottleneck_df.sort_values('avg_speed', ascending=True)
    
    bar.add_xaxis(bottleneck_df_sorted['section_name'].tolist())
    
    bar.add_yaxis(
        "平均速度(km/h)",
        bottleneck_df_sorted['avg_speed'].tolist(),
        category_gap="60%",
        itemstyle_opts=opts.ItemStyleOpts(
            color=JsCode("""
                function(params) {
                    var colorList = ['#c23531','#d48265','#e5a527','#f4b817',
                                   '#f9d71c','#ffe400','#c7e000','#8fd400'];
                    return colorList[params.dataIndex % colorList.length];
                }
            """)
        ),
        label_opts=opts.LabelOpts(
            is_show=True,
            position="right",
            formatter="{c} km/h"
        )
    )
    
    bar.reversal_axis()
    bar.set_global_opts(
        title_opts=opts.TitleOpts(
            title="低速路段TOP10",
            subtitle="平均速度低于80km/h的瓶颈路段",
            pos_left="center"
        ),
        xaxis_opts=opts.AxisOpts(
            name="平均速度(km/h)",
            axislabel_opts=opts.LabelOpts(formatter="{value}")
        ),
        yaxis_opts=opts.AxisOpts(
            name="路段名称",
            axislabel_opts=opts.LabelOpts(rotate=0)
        ),
        tooltip_opts=opts.TooltipOpts(
            trigger="axis",
            axis_pointer_type="shadow",
            formatter=JsCode("""
                function(params) {
                    return params[0].name + '<br/>' +
                           '平均速度: ' + params[0].value + ' km/h';
                }
            """)
        )
    )
    
    # 添加警戒线
    bar.set_series_opts(
        markline_opts=opts.MarkLineOpts(
            data=[
                opts.MarkLineItem(x=80, name="拥堵阈值")
            ],
            linestyle_opts=opts.LineStyleOpts(color="#FF0000", type_="dashed", width=2),
            label_opts=opts.LabelOpts(formatter="拥堵阈值: 80km/h")
        )
    )
    
    return bar

def create_congestion_pie():
    """拥堵等级分布饼图"""
    # 统计各拥堵等级的门架数量
    congestion_level_sql = """
    SELECT 
        CASE 
            WHEN congestion_index >= 80 THEN '严重拥堵'
            WHEN congestion_index >= 60 THEN '中度拥堵'
            WHEN congestion_index >= 40 THEN '轻度拥堵'
            ELSE '畅通'
        END as level,
        COUNT(*) as count
    FROM highway.dm_gsgl_congestion_index
    WHERE hour_of_day = 8  -- 早高峰时段
    GROUP BY 
        CASE 
            WHEN congestion_index >= 80 THEN '严重拥堵'
            WHEN congestion_index >= 60 THEN '中度拥堵'
            WHEN congestion_index >= 40 THEN '轻度拥堵'
            ELSE '畅通'
        END
    """
    
    level_df = spark.sql(congestion_level_sql).toPandas()
    
    pie = Pie(init_opts=opts.InitOpts(theme=ThemeType.DARK, width="100%", height="400px"))
    
    pie.add(
        "",
        [list(z) for z in zip(level_df['level'], level_df['count'])],
        radius=["40%", "75%"],
        label_opts=opts.LabelOpts(
            formatter="{b}: {c} ({d}%)",
            position="outside"
        ),
        itemstyle_opts=opts.ItemStyleOpts(
            border_radius=10,
            border_color="#fff",
            border_width=2
        )
    )
    
    pie.set_global_opts(
        title_opts=opts.TitleOpts(
            title="早高峰拥堵等级分布",
            subtitle="8:00时刻各门架拥堵状况",
            pos_left="center"
        ),
        legend_opts=opts.LegendOpts(
            orient="vertical",
            pos_left="left",
            pos_top="center"
        )
    )
    
    pie.set_colors(["#5CB85C", "#F0AD4E", "#D9534F", "#BB2124"])
    
    return pie

def create_hotspot_table():
    """拥堵热点门架表格"""
    from pyecharts.components import Table
    
    headers = ["排名", "门架名称", "所属路段", "平均拥堵指数", "拥堵频率(%)", "拥堵时长(h)"]
    rows = []
    
    for idx, row in hotspot_df.iterrows():
        rows.append([
            idx + 1,
            row['gantry_name'],
            row['section_name'],
            f"{row['avg_ti']:.1f}",
            f"{row['congestion_pct']:.1f}%",
            int(row['congestion_hours'])
        ])
    
    table = Table()
    table.add(headers, rows)
    table.set_global_opts(
        title_opts=opts.ComponentTitleOpts(
            title="拥堵热点门架TOP10",
            subtitle="基于历史数据统计"
        )
    )
    
    return table

# 5. 创建仪表板页面
def create_dashboard():
    page = Page(
        page_title="高速公路交通状况监控大屏",
        layout=Page.DraggablePageLayout
    )
    
    # 添加所有图表
    page.add(
        create_hourly_traffic_chart(),
        create_congestion_heatmap(),
        create_bottleneck_ranking(),
        create_congestion_pie(),
        # create_hotspot_table()  # 表格组件单独处理
    )
    
    # 生成HTML
    page.render("highway_traffic_dashboard.html")
    print("可视化大屏已生成: highway_traffic_dashboard.html")
    
    # 生成可拖拽配置文件
    # page.save_resize_html("highway_traffic_dashboard_config.html", cfg_file="chart_config.json")

# 6. 生成额外的数据分析报告
def generate_analysis_report():
    """生成文本分析报告"""
    report = f"""
    高速公路交通状况分析报告
    ========================
    
    1. 时间维度分析
    ----------------
    - 早高峰时段(7-9点): 平均拥堵指数 {hourly_df[hourly_df['hour_of_day'].between(7,9)]['avg_congestion_index'].mean():.2f}
    - 晚高峰时段(17-19点): 平均拥堵指数 {hourly_df[hourly_df['hour_of_day'].between(17,19)]['avg_congestion_index'].mean():.2f}
    - 最拥堵时刻: {hourly_df.loc[hourly_df['avg_congestion_index'].idxmax(), 'hour_of_day']}点
    - 最畅通时刻: {hourly_df.loc[hourly_df['avg_congestion_index'].idxmin(), 'hour_of_day']}点
    
    2. 空间维度分析
    ----------------
    - 监测路段总数: {len(section_df)}
    - 平均拥堵指数大于60的路段数: {len(section_df[section_df['avg_congestion'] > 60])}
    - 最拥堵路段: {section_df.loc[section_df['avg_congestion'].idxmax(), 'section_name']}
    
    3. 瓶颈路段分析
    ----------------
    - 低速路段数量: {len(bottleneck_df)}
    - 最低平均速度: {bottleneck_df['avg_speed'].min():.2f} km/h
    - 严重拥堵(速度<60km/h)频率最高路段: {bottleneck_df.loc[bottleneck_df['severe_slow_pct'].idxmax(), 'section_name']}
    
    4. 改善建议
    ------------
    - 重点关注早晚高峰时段的交通疏导
    - 对瓶颈路段进行扩容或优化
    - 在拥堵热点门架处增设可变信息板进行分流引导
    """
    
    with open("traffic_analysis_report.txt", "w", encoding="utf-8") as f:
        f.write(report)
    
    print("分析报告已生成: traffic_analysis_report.txt")

# 7. 执行主程序
if __name__ == "__main__":
    try:
        create_dashboard()
        generate_analysis_report()
        
        # 关闭Spark Session
        spark.stop()
        print("\n所有任务执行完成！")
        
    except Exception as e:
        print(f"执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        spark.stop()
```

**截图说明：可视化截图**
- 截图1：生成的HTML大屏整体效果（浏览器全屏截图）
- 截图2：24小时流量与拥堵指数变化图（带早晚高峰标记）
- 截图3：路段拥堵热力图
- 截图4：低速路段TOP10排行榜

---

## 实训八：使用SparkSQL进行高速交通流量关联分析

**任务目标：**
- 清洗和标准化车型、收费站、服务区等维度数据
- 分析不同车型的通行特征（通行量、速度差异、时间分布）
- 统计各收费站的小时流量变化，识别拥堵收费站
- 分析服务区进出流量，为服务区管理提供数据支持

**SparkSQL语句：**

```sql
-- 1. 数据清洗：创建标准化维度表
-- 车型标准表
CREATE OR REPLACE TABLE std_gsgl_vehicle_type AS
SELECT DISTINCT
    vehicle_type_code,
    TRIM(vehicle_type_name) as vehicle_type_name,
    CASE 
        WHEN vehicle_type_name LIKE '%客车%' THEN '客车'
        WHEN vehicle_type_name LIKE '%货车%' THEN '货车'
        WHEN vehicle_type_name LIKE '%专项作业车%' THEN '专项作业车'
        ELSE '其他'
    END as vehicle_category,
    vehicle_class,
    axle_count,
    weight_limit,
    fee_coefficient
FROM ods_gsgl_vehicle_type
WHERE vehicle_type_code IS NOT NULL
  AND vehicle_type_name IS NOT NULL;

-- 收费站标准表
CREATE OR REPLACE TABLE std_gsgl_toll AS
SELECT DISTINCT
    toll_code,
    TRIM(toll_name) as toll_name,
    toll_type,
    road_code,
    section_code,
    lane_count,
    etc_lane_count,
    ROUND(etc_lane_count * 100.0 / NULLIF(lane_count, 0), 2) as etc_coverage_rate
FROM ods_gsgl_toll
WHERE toll_code IS NOT NULL
  AND lane_count > 0;

-- 服务区标准表
CREATE OR REPLACE TABLE std_gsgl_service AS
SELECT DISTINCT
    service_code,
    TRIM(service_name) as service_name,
    road_code,
    section_code,
    direction,
    CASE 
        WHEN direction = '1' THEN '上行'
        WHEN direction = '2' THEN '下行'
        ELSE '双向'
    END as direction_name,
    facilities
FROM ods_gsgl_service
WHERE service_code IS NOT NULL;

-- 2. 车型通行特征分析
-- 2.1 不同车型的通行量统计
CREATE OR REPLACE TABLE dm_gsgl_vehicle_type_volume AS
SELECT 
    vt.vehicle_type_code,
    vt.vehicle_type_name,
    vt.vehicle_category,
    COUNT(DISTINCT t.vlp) as unique_vehicles,
    COUNT(t.trail_id) as total_records,
    ROUND(COUNT(t.trail_id) * 100.0 / SUM(COUNT(t.trail_id)) OVER(), 2) as volume_percentage
FROM std_gsgl_vehicle_trails t
JOIN std_gsgl_vehicle_type vt ON t.identify_vtype = vt.vehicle_type_code
GROUP BY vt.vehicle_type_code, vt.vehicle_type_name, vt.vehicle_category
ORDER BY total_records DESC;

-- 2.2 不同车型的速度特征分析
CREATE OR REPLACE TABLE dm_gsgl_vehicle_type_speed AS
WITH vehicle_speed AS (
    SELECT 
        vt.vehicle_type_name,
        vt.vehicle_category,
        st.avg_speed_kmph as speed
    FROM std_gsgl_vehicle_trails t
    JOIN std_gsgl_vehicle_type vt ON t.identify_vtype = vt.vehicle_type_code
    JOIN dm_gsgl_speed_trails_clean st ON t.vlp = st.vlp 
        AND t.gantry_code = st.from_gantry
        AND ABS(UNIX_TIMESTAMP(t.capture_time) - UNIX_TIMESTAMP(st.start_time)) < 300
)
SELECT 
    vehicle_type_name,
    vehicle_category,
    COUNT(*) as speed_samples,
    ROUND(AVG(speed), 2) as avg_speed,
    ROUND(MIN(speed), 2) as min_speed,
    ROUND(PERCENTILE(speed, 0.25), 2) as speed_p25,
    ROUND(PERCENTILE(speed, 0.5), 2) as speed_median,
    ROUND(PERCENTILE(speed, 0.75), 2) as speed_p75,
    ROUND(MAX(speed), 2) as max_speed,
    ROUND(STDDEV(speed), 2) as speed_stddev
FROM vehicle_speed
GROUP BY vehicle_type_name, vehicle_category
HAVING COUNT(*) >= 100  -- 确保样本量足够
ORDER BY avg_speed DESC;

-- 2.3 不同车型的时间分布特征
CREATE OR REPLACE TABLE dm_gsgl_vehicle_type_hourly AS
SELECT 
    vt.vehicle_category,
    HOUR(t.capture_time) as hour_of_day,
    COUNT(DISTINCT t.vlp) as vehicles_count,
    COUNT(t.trail_id) as records_count,
    -- 计算该类别在该时段的占比
    ROUND(COUNT(t.trail_id) * 100.0 / 
          SUM(COUNT(t.trail_id)) OVER(PARTITION BY vt.vehicle_category), 2) as hour_percentage
FROM std_gsgl_vehicle_trails t
JOIN std_gsgl_vehicle_type vt ON t.identify_vtype = vt.vehicle_type_code
GROUP BY vt.vehicle_category, HOUR(t.capture_time)
ORDER BY vehicle_category, hour_of_day;

-- 3. 收费站流量分析
-- 3.1 收费站总体流量统计
CREATE OR REPLACE TABLE dm_gsgl_toll_total_flow AS
SELECT 
    tl.toll_code,
    tl.toll_name,
    tl.toll_type,
    tl.lane_count,
    tl.etc_coverage_rate,
    COUNT(DISTINCT t.vlp) as total_vehicles,
    COUNT(t.trail_id) as total_records,
    -- 计算日均流量
    COUNT(DISTINCT t.vlp) / NULLIF(COUNT(DISTINCT DATE(t.capture_time)), 0) as avg_daily_flow,
    -- 计算每车道流量
    COUNT(DISTINCT t.vlp) / NULLIF(tl.lane_count, 0) as flow_per_lane
FROM std_gsgl_vehicle_trails t
JOIN std_gsgl_gantry g ON t.gantry_code = g.gantry_code
JOIN std_gsgl_toll tl ON g.section_code = tl.section_code
GROUP BY tl.toll_code, tl.toll_name, tl.toll_type, tl.lane_count, tl.etc_coverage_rate
ORDER BY total_vehicles DESC;

-- 3.2 收费站小时流量分析
CREATE OR REPLACE TABLE dm_gsgl_toll_hourly_flow AS
SELECT 
    tl.toll_code,
    tl.toll_name,
    DATE_FORMAT(t.capture_time, 'yyyy-MM-dd HH:00:00') as hour_window,
    HOUR(t.capture_time) as hour_of_day,
    t.direction,
    COUNT(DISTINCT t.vlp) as vehicles_count,
    -- 计算通行类型分布
    SUM(CASE WHEN t.crossing_type = 'ETC通行' THEN 1 ELSE 0 END) as etc_count,
    SUM(CASE WHEN t.crossing_type = '人工收费' THEN 1 ELSE 0 END) as manual_count,
    -- 计算拥堵程度（基于流量与车道数比例）
    COUNT(DISTINCT t.vlp) / NULLIF(tl.lane_count, 0) as congestion_level
FROM std_gsgl_vehicle_trails t
JOIN std_gsgl_gantry g ON t.gantry_code = g.gantry_code
JOIN std_gsgl_toll tl ON g.section_code = tl.section_code
GROUP BY tl.toll_code, tl.toll_name, DATE_FORMAT(t.capture_time, 'yyyy-MM-dd HH:00:00'), 
         HOUR(t.capture_time), t.direction, tl.lane_count;

-- 3.3 识别拥堵收费站
CREATE OR REPLACE TABLE dm_gsgl_toll_congestion AS
SELECT 
    toll_code,
    toll_name,
    lane_count,
    AVG(congestion_level) as avg_congestion_level,
    MAX(congestion_level) as max_congestion_level,
    COUNT(CASE WHEN congestion_level > 500 THEN 1 END) as congestion_hours,
    COUNT(CASE WHEN congestion_level > 500 THEN 1 END) * 100.0 / COUNT(*) as congestion_rate
FROM dm_gsgl_toll_hourly_flow
GROUP BY toll_code, toll_name, lane_count
HAVING COUNT(*) >= 24  -- 至少有一天的数据
ORDER BY avg_congestion_level DESC
LIMIT 20;

-- 4. 服务区流量分析
-- 4.1 服务区总体流量统计
CREATE OR REPLACE TABLE dm_gsgl_service_total_flow AS
SELECT 
    sv.service_code,
    sv.service_name,
    sv.direction_name,
    sv.road_code,
    COUNT(DISTINCT t.vlp) as total_vehicles,
    COUNT(t.trail_id) as total_records,
    -- 估算进入服务区的车辆（基于速度变化）
    SUM(CASE WHEN t.speed < 30 THEN 1 ELSE 0 END) as potential_entries
FROM std_gsgl_vehicle_trails t
JOIN std_gsgl_gantry g ON t.gantry_code = g.gantry_code
JOIN std_gsgl_service sv ON g.section_code = sv.section_code 
    AND (sv.direction = '双向' OR t.direction = sv.direction)
GROUP BY sv.service_code, sv.service_name, sv.direction_name, sv.road_code
ORDER BY total_vehicles DESC;

-- 4.2 服务区小时流量分析
CREATE OR REPLACE TABLE dm_gsgl_service_hourly_flow AS
SELECT 
    sv.service_code,
    sv.service_name,
    sv.direction_name,
    DATE_FORMAT(t.capture_time, 'yyyy-MM-dd HH:00:00') as hour_window,
    HOUR(t.capture_time) as hour_of_day,
    COUNT(DISTINCT t.vlp) as vehicles_count,
    -- 基于速度判断可能进入服务区的车辆
    SUM(CASE WHEN t.speed < 30 THEN 1 ELSE 0 END) as slow_vehicles,
    AVG(t.speed) as avg_speed
FROM std_gsgl_vehicle_trails t
JOIN std_gsgl_gantry g ON t.gantry_code = g.gantry_code
JOIN std_gsgl_service sv ON g.section_code = sv.section_code 
    AND (sv.direction = '双向' OR t.direction = sv.direction)
GROUP BY sv.service_code, sv.service_name, sv.direction_name,
         DATE_FORMAT(t.capture_time, 'yyyy-MM-dd HH:00:00'), HOUR(t.capture_time);

-- 4.3 服务区利用率分析
CREATE OR REPLACE TABLE dm_gsgl_service_utilization AS
SELECT 
    service_code,
    service_name,
    direction_name,
    hour_of_day,
    AVG(vehicles_count) as avg_vehicles,
    AVG(slow_vehicles) as avg_slow_vehicles,
    ROUND(AVG(slow_vehicles) * 100.0 / NULLIF(AVG(vehicles_count), 0), 2) as entry_rate,
    -- 识别高峰时段
    CASE 
        WHEN hour_of_day BETWEEN 11 AND 13 THEN '午餐时段'
        WHEN hour_of_day BETWEEN 17 AND 19 THEN '晚餐时段'
        WHEN hour_of_day BETWEEN 7 AND 9 THEN '早高峰'
        WHEN hour_of_day BETWEEN 0 AND 5 THEN '夜间'
        ELSE '其他时段'
    END as period_type
FROM dm_gsgl_service_hourly_flow
GROUP BY service_code, service_name, direction_name, hour_of_day
ORDER BY service_code, hour_of_day;

-- 5. 综合关联分析
-- 5.1 车型与收费站通行效率关联
CREATE OR REPLACE TABLE dm_gsgl_vehicle_toll_efficiency AS
SELECT 
    vt.vehicle_category,
    tl.toll_name,
    tl.etc_coverage_rate,
    COUNT(DISTINCT t.vlp) as vehicles_count,
    AVG(CASE WHEN t.crossing_type = 'ETC通行' THEN 1 ELSE 0 END) * 100 as etc_usage_rate,
    AVG(t.speed) as avg_speed_at_toll
FROM std_gsgl_vehicle_trails t
JOIN std_gsgl_vehicle_type vt ON t.identify_vtype = vt.vehicle_type_code
JOIN std_gsgl_gantry g ON t.gantry_code = g.gantry_code
JOIN std_gsgl_toll tl ON g.section_code = tl.section_code
GROUP BY vt.vehicle_category, tl.toll_name, tl.etc_coverage_rate
HAVING COUNT(DISTINCT t.vlp) >= 100;

-- 6. 生成分析报告视图
CREATE OR REPLACE VIEW v_traffic_analysis_summary AS
SELECT 
    '车型分布' as analysis_type,
    vehicle_category as category,
    CONCAT(ROUND(SUM(volume_percentage), 2), '%') as value,
    '占比' as metric
FROM dm_gsgl_vehicle_type_volume
GROUP BY vehicle_category

UNION ALL

SELECT 
    '收费站拥堵' as analysis_type,
    toll_name as category,
    CONCAT(ROUND(congestion_rate, 2), '%') as value,
    '拥堵率' as metric
FROM dm_gsgl_toll_congestion
WHERE ROWNUM <= 5

UNION ALL

SELECT 
    '服务区利用' as analysis_type,
    service_name as category,
    CONCAT(ROUND(AVG(entry_rate), 2), '%') as value,
    '平均进入率' as metric
FROM dm_gsgl_service_utilization
GROUP BY service_name
ORDER BY analysis_type, value DESC;

-- 查询验证
SELECT * FROM v_traffic_analysis_summary;
```

**Python可视化代码（关联分析）：**

```python
# traffic_correlation_analysis.py
from pyspark.sql import SparkSession
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
plt.rcParams['font.sans-serif'] = ['SimHei']  # 中文显示
plt.rcParams['axes.unicode_minus'] = False

# 创建SparkSession
spark = SparkSession.builder \
    .appName("TrafficCorrelationAnalysis") \
    .config("hive.metastore.uris", "thrift://slave2:9083") \
    .enableHiveSupport() \
    .getOrCreate()

# 1. 车型速度对比分析
vehicle_speed_df = spark.sql("SELECT * FROM highway.dm_gsgl_vehicle_type_speed").toPandas()

fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

# 箱线图
vehicle_speed_df.boxplot(column='avg_speed', by='vehicle_category', ax=ax1)
ax1.set_title('不同车型类别平均速度分布')
ax1.set_xlabel('车型类别')
ax1.set_ylabel('平均速度(km/h)')

# 条形图
vehicle_speed_df.plot(x='vehicle_type_name', y='avg_speed', kind='barh', ax=ax2)
ax2.set_title('各车型平均速度排名')
ax2.set_xlabel('平均速度(km/h)')

plt.tight_layout()
plt.savefig('vehicle_speed_analysis.png', dpi=300, bbox_inches='tight')

# 2. 收费站ETC使用率分析
toll_efficiency_df = spark.sql("""
    SELECT toll_name, etc_coverage_rate, etc_usage_rate
    FROM highway.dm_gsgl_vehicle_toll_efficiency
    WHERE etc_usage_rate IS NOT NULL
""").toPandas()

plt.figure(figsize=(10, 8))
plt.scatter(toll_efficiency_df['etc_coverage_rate'], 
           toll_efficiency_df['etc_usage_rate'],
           s=100, alpha=0.6)
plt.xlabel('ETC车道覆盖率(%)')
plt.ylabel('ETC实际使用率(%)')
plt.title('收费站ETC覆盖率与使用率关系')

# 添加趋势线
z = np.polyfit(toll_efficiency_df['etc_coverage_rate'], 
               toll_efficiency_df['etc_usage_rate'], 1)
p = np.poly1d(z)
plt.plot(toll_efficiency_df['etc_coverage_rate'], 
         p(toll_efficiency_df['etc_coverage_rate']), 
         "r--", alpha=0.8)

plt.grid(True, alpha=0.3)
plt.savefig('etc_usage_analysis.png', dpi=300, bbox_inches='tight')

print("关联分析图表已生成！")
spark.stop()
```

**截图说明：关联分析结果截图**
- 截图1：不同车型通行量占比（饼图或柱状图）
- 截图2：各收费站拥堵率排名（包含ETC覆盖率信息）
- 截图3：服务区24小时利用率变化曲线
- 截图4：车型速度分布箱线图

---

## 文档格式要求

- **代码格式**：所有代码使用 `Consolas` 字体，保持缩进规范
- **截图要求**：
  - 包含完整的命令行窗口或IDE界面
  - 清晰显示执行结果
  - 标注关键信息
- **文件命名**：`班级_学号_姓名_实训报告.docx`
- **提交内容**：
  - 实训报告文档
  - 源代码文件（打包为zip）
  - 可视化HTML文件

---

**实训总结**

通过本次大数据分析实训，我们完成了从数据导入、清洗、存储到分析、挖掘、可视化的完整流程。主要收获包括：

1. **技术能力提升**：掌握了Hadoop、Hive、Spark等大数据技术栈的实际应用
2. **业务理解深化**：通过实际数据分析，深入理解了高速公路交通管理的业务场景
3. **问题解决能力**：在实训过程中遇到并解决了数据质量、性能优化等实际问题
4. **团队协作经验**：通过小组合作完成项目，提升了沟通协调能力

本实训项目不仅是技术学习的过程，更是将理论知识转化为实践能力的重要环节，为今后从事大数据相关工作打下了坚实基础。