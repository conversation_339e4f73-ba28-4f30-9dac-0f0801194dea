# Linux 实际操作练习指南

通过以下步骤实际操作，可以加深对Linux命令的理解和记忆。

## 准备工作

1.  打开终端。
2.  创建一个用于本次练习的目录，并进入该目录。
    ```bash
    mkdir ~/linux_exam_practice
    cd ~/linux_exam_practice
    pwd # 确认你现在在 'linux_exam_practice' 目录下
    ```

## 第一部分：文件和目录操作

1.  **创建目录结构**：
    ```bash
    # 创建一个项目结构，-p参数可以一次性创建多层目录
    mkdir -p project/src project/docs project/bin
    ```

2.  **查看目录**：
    ```bash
    ls # 查看当前目录
    ls -l # 查看详细信息
    ls -a # 查看包括隐藏文件在内的所有文件
    ls -R # 递归查看所有子目录和文件
    ```

3.  **创建和复制文件**：
    ```bash
    # 在src目录中创建一个空文件
    touch project/src/main.c
    
    # 创建一个说明文件
    touch project/docs/readme.txt
    
    # 复制readme.txt到根目录并改名为 'info.txt'
    cp project/docs/readme.txt info.txt
    ```

4.  **移动和重命名文件**：
    ```bash
    # 将info.txt移动到docs目录
    mv info.txt project/docs/
    
    # 将main.c重命名为app.c
    mv project/src/main.c project/src/app.c
    ```

## 第二部分：文件内容与编辑

1.  **使用`vi`写入内容**：
    ```bash
    # 编辑readme.txt文件
    vi project/docs/readme.txt
    ```
    - 按 `i` 进入插入模式。
    - 输入 "This is a test file for linux practice."。
    - 按 `Esc` 键退回到命令模式。
    - 输入 `:wq` 然后按回车，保存并退出。

2.  **查看文件内容**：
    ```bash
    # 查看整个文件
    cat project/docs/readme.txt
    
    # 查看文件前两行
    head -n 2 project/docs/readme.txt
    
    # 实时查看日志（模拟）
    # 打开一个终端执行 tail -f project/docs/readme.txt
    # 在另一个终端执行 echo "new line" >> project/docs/readme.txt
    # 观察第一个终端的变化
    ```

## 第三部分：权限管理

1.  **创建并查看脚本权限**：
    ```bash
    # 创建一个脚本文件
    touch project/bin/hello.sh
    ls -l project/bin/hello.sh # 查看默认权限，通常是 644 (rw-r--r--)
    ```

2.  **修改权限使其可执行**：
    ```bash
    # 使用数字法给所有者加上执行权限
    chmod 744 project/bin/hello.sh
    ls -l project/bin/hello.sh # 权限变为 rwxr--r--
    
    # 使用符号法给用户组也加上执行权限
    chmod g+x project/bin/hello.sh
    ls -l project/bin/hello.sh # 权限变为 rwxr-xr--
    ```

## 第四部分：打包与压缩

1.  **打包整个项目**：
    ```bash
    # 将整个project目录打包成一个.tar文件
    tar -cvf project_backup.tar project
    ```

2.  **打包并用gzip压缩**：
    ```bash
    # 将project目录打包并压缩为 .tar.gz 文件
    tar -czvf project_archive.tar.gz project
    ```

3.  **解压和解包**：
    ```bash
    # 创建一个新目录用于测试解压
    mkdir unpack_test
    mv project_archive.tar.gz unpack_test/
    cd unpack_test
    
    # 解压文件
    tar -xzvf project_archive.tar.gz
    ls # 查看解压后的文件
    ```

## 清理工作

1.  完成练习后，可以删除练习目录。
    ```bash
    # 回到主目录
    cd ~
    # 强制递归删除练习目录，请确保路径正确！
    rm -rf linux_exam_practice
    ```
