# 数据库系统工程师考试详细知识点复习文档

## 考试科目1：信息系统知识

### 1. 计算机系统知识

#### 1.1 硬件知识

**计算机体系结构**

*冯·诺依曼体系结构*
- **五大组成部分**：运算器、控制器、存储器、输入设备、输出设备
- **存储程序概念**：程序和数据统一存储，按地址访问，顺序执行
- **数据通路**：CPU内部各部件间的数据传输路径

*CPU组成与工作原理*
- **运算器（ALU）**：执行算术运算和逻辑运算
- **控制器（CU）**：取指令、译码、执行、访存、写回
- **寄存器组**：
  - 程序计数器（PC）：存放下一条指令地址
  - 指令寄存器（IR）：存放当前执行指令
  - 累加器（ACC）：暂存运算结果
  - 状态寄存器（PSW）：存放程序状态字

*存储器层次结构*
- **寄存器**：CPU内部，速度最快，容量最小
- **Cache**：高速缓存，采用SRAM技术
- **主存储器**：内存，采用DRAM技术
- **辅助存储器**：硬盘、光盘等，容量大，速度慢

*Cache工作原理*
- **时间局部性**：最近访问的信息很可能再次被访问
- **空间局部性**：被访问信息附近的信息很可能被访问
- **Cache映射方式**：
  - 直接映射：每个主存块只能映射到Cache的固定位置
  - 全相联映射：主存块可映射到Cache任意位置
  - 组相联映射：直接映射和全相联映射的折中

*I/O系统*
- **I/O接口功能**：
  - 数据缓冲：解决CPU与外设速度差异
  - 信号转换：数字信号与模拟信号转换
  - 控制和状态信息传送：设备状态监控
- **I/O控制方式**：
  - 程序直接控制：CPU轮询检查设备状态
  - 中断控制：设备完成操作后向CPU发出中断
  - DMA控制：设备直接与内存交换数据
  - 通道控制：专用处理器控制I/O操作

*CISC vs RISC*
- **CISC（复杂指令集）**：
  - 指令数量多，功能强大
  - 指令长度可变，寻址方式多样
  - 代表：Intel x86系列
- **RISC（精简指令集）**：
  - 指令数量少，指令简单
  - 指令长度固定，寻址方式简单
  - 采用流水线技术，执行效率高
  - 代表：ARM、MIPS

*流水线技术*
- **基本概念**：将指令执行过程分解为若干阶段，各阶段并行工作
- **经典五级流水线**：
  1. IF（取指）：从内存取指令
  2. ID（译码）：指令译码，读寄存器
  3. EX（执行）：执行运算
  4. MEM（访存）：访问内存
  5. WB（写回）：结果写回寄存器
- **流水线冲突**：
  - 结构冲突：硬件资源冲突
  - 数据冲突：指令间数据相关
  - 控制冲突：分支指令改变执行流

*RAID技术*
- **RAID 0**：条带化，无冗余，提高性能
- **RAID 1**：镜像，100%冗余，提高可靠性
- **RAID 5**：分布式奇偶校验，需要至少3块盘
- **RAID 6**：双重奇偶校验，需要至少4块盘
- **RAID 10**：RAID 1+0，镜像+条带

**系统性能评测**

*性能指标*
- **响应时间**：系统完成任务所需时间
- **吞吐量**：单位时间内完成的任务数量
- **利用率**：资源被有效使用的时间比例
- **可用性**：系统正常工作时间占总时间比例

*性能计算公式*
```
CPU时间 = 指令数 × CPI × 时钟周期时间
MIPS = 指令数 / (执行时间 × 10^6)
加速比 = 优化前执行时间 / 优化后执行时间
效率 = 加速比 / 处理器数量
```

#### 1.2 数据结构与算法

**线性数据结构**

*数组*
- **静态数组**：编译时确定大小，存储在栈上
- **动态数组**：运行时确定大小，存储在堆上
- **时间复杂度**：
  - 访问：O(1)
  - 查找：O(n)
  - 插入/删除：O(n)

*链表*
- **单向链表**：每个节点包含数据和指向下一节点的指针
- **双向链表**：每个节点包含数据和指向前后节点的指针
- **循环链表**：尾节点指向头节点
- **时间复杂度**：
  - 访问：O(n)
  - 查找：O(n)
  - 插入/删除：O(1)（已知位置）

*栈和队列*
- **栈（Stack）**：后进先出（LIFO）
  - 基本操作：push（入栈）、pop（出栈）、top（栈顶元素）
  - 应用：函数调用、表达式求值、括号匹配
- **队列（Queue）**：先进先出（FIFO）
  - 基本操作：enqueue（入队）、dequeue（出队）、front（队首元素）
  - 应用：BFS遍历、缓冲区管理

**树形数据结构**

*二叉树*
- **性质**：
  - 节点数为n的二叉树，边数为n-1
  - 第i层最多有2^(i-1)个节点
  - 深度为k的二叉树最多有2^k-1个节点
- **遍历方式**：
  - 前序遍历：根→左→右
  - 中序遍历：左→根→右
  - 后序遍历：左→右→根
  - 层序遍历：按层从上到下，从左到右

*二叉搜索树（BST）*
- **性质**：左子树所有节点值小于根节点，右子树所有节点值大于根节点
- **时间复杂度**：
  - 平均情况：O(log n)
  - 最坏情况：O(n)（退化为链表）

*平衡二叉树（AVL树）*
- **平衡条件**：任意节点的左右子树高度差不超过1
- **旋转操作**：LL、RR、LR、RL四种情况
- **时间复杂度**：O(log n)

*堆*
- **大顶堆**：父节点值大于等于子节点值
- **小顶堆**：父节点值小于等于子节点值
- **应用**：优先队列、堆排序
- **时间复杂度**：
  - 插入：O(log n)
  - 删除最值：O(log n)
  - 建堆：O(n)

**图**

*图的表示*
- **邻接矩阵**：n×n矩阵，适合稠密图
- **邻接表**：每个顶点维护邻接顶点列表，适合稀疏图

*图的遍历*
- **深度优先搜索（DFS）**：
  - 使用栈（递归）
  - 时间复杂度：O(V+E)
- **广度优先搜索（BFS）**：
  - 使用队列
  - 时间复杂度：O(V+E)

*最短路径算法*
- **Dijkstra算法**：单源最短路径，不能处理负权边
- **Floyd算法**：所有顶点间最短路径
- **Bellman-Ford算法**：单源最短路径，可处理负权边

**Hash表**

*Hash函数*
- **除法散列**：h(k) = k mod m
- **乘法散列**：h(k) = floor(m * (k * A mod 1))
- **全域散列**：随机选择散列函数

*冲突处理*
- **链地址法**：将冲突元素存储在链表中
- **开放地址法**：
  - 线性探测：h(k,i) = (h'(k) + i) mod m
  - 二次探测：h(k,i) = (h'(k) + c1*i + c2*i^2) mod m
  - 双重散列：h(k,i) = (h1(k) + i*h2(k)) mod m

**算法复杂度**

*时间复杂度*
- **O(1)**：常数时间
- **O(log n)**：对数时间
- **O(n)**：线性时间
- **O(n log n)**：线性对数时间
- **O(n^2)**：平方时间
- **O(2^n)**：指数时间

*空间复杂度*
- 算法执行过程中所需的额外存储空间

**常用算法**

*排序算法*
- **冒泡排序**：O(n^2)，稳定
- **选择排序**：O(n^2)，不稳定
- **插入排序**：O(n^2)，稳定
- **希尔排序**：O(n^1.3)，不稳定
- **归并排序**：O(n log n)，稳定
- **快速排序**：平均O(n log n)，不稳定
- **堆排序**：O(n log n)，不稳定
- **计数排序**：O(n+k)，稳定
- **基数排序**：O(d(n+k))，稳定

*查找算法*
- **顺序查找**：O(n)
- **二分查找**：O(log n)，要求有序
- **Hash查找**：平均O(1)

#### 1.3 软件知识

**操作系统**

*进程管理*
- **进程状态**：
  - 就绪（Ready）：等待CPU调度
  - 运行（Running）：占用CPU执行
  - 阻塞（Blocked）：等待I/O或事件
- **进程调度算法**：
  - 先来先服务（FCFS）：按到达时间顺序
  - 短作业优先（SJF）：执行时间短的优先
  - 时间片轮转（RR）：每个进程分配时间片
  - 优先级调度：按优先级高低调度
  - 多级反馈队列：综合考虑多种因素

*线程*
- **用户级线程**：操作系统不感知，切换快
- **内核级线程**：操作系统管理，可利用多核
- **混合模型**：用户级线程映射到内核级线程

*同步与互斥*
- **临界区**：访问共享资源的代码段
- **信号量（Semaphore）**：
  - P操作：申请资源（等待）
  - V操作：释放资源（信号）
- **管程（Monitor）**：高级同步机制
- **死锁**：
  - 必要条件：互斥、占有并等待、非抢占、循环等待
  - 预防：破坏必要条件之一
  - 避免：银行家算法
  - 检测与恢复：定期检测，发现后恢复

*存储管理*
- **连续分配**：
  - 单一连续：整个内存分配给一个程序
  - 固定分区：内存分为固定大小分区
  - 动态分区：按需分配，大小可变
- **分页系统**：
  - 逻辑地址：页号+页内偏移
  - 物理地址：帧号+帧内偏移
  - 页表：页号到帧号的映射
- **分段系统**：
  - 按逻辑单位（函数、数组等）分段
  - 段表：段号到段基址和段长的映射
- **虚拟存储**：
  - 页面置换算法：
    - FIFO：先进先出
    - LRU：最近最少使用
    - OPT：最优置换（理论）
    - Clock：时钟算法

*文件系统*
- **文件分配方式**：
  - 连续分配：文件占用连续磁盘块
  - 链接分配：文件块通过指针链接
  - 索引分配：使用索引块记录文件块位置
- **目录结构**：
  - 单级目录：所有文件在一个目录
  - 两级目录：用户目录+文件目录
  - 树形目录：层次结构
  - 有向图目录：允许共享

**程序设计语言**

*编译系统*
- **词法分析**：将源程序分解为单词符号
- **语法分析**：根据语法规则生成语法树
- **语义分析**：类型检查、作用域分析
- **代码生成**：生成目标代码
- **代码优化**：提高代码效率

*解释系统*
- **直接解释**：逐条解释执行源程序
- **预编译解释**：先编译成中间代码再解释

*汇编系统*
- **一遍汇编**：一次扫描完成汇编
- **两遍汇编**：第一遍建立符号表，第二遍生成目标代码

#### 1.4 计算机网络知识

**网络体系结构**

*OSI七层模型*
1. **物理层**：传输比特流，定义电气特性
2. **数据链路层**：帧传输，错误检测和纠正
3. **网络层**：路由选择，IP协议
4. **传输层**：端到端通信，TCP/UDP协议
5. **会话层**：建立、管理和终止会话
6. **表示层**：数据格式转换、加密解密
7. **应用层**：网络服务，HTTP、FTP等

*TCP/IP四层模型*
1. **网络接口层**：对应OSI物理层+数据链路层
2. **网际层**：对应OSI网络层，IP协议
3. **传输层**：对应OSI传输层，TCP/UDP
4. **应用层**：对应OSI会话层+表示层+应用层

**网络协议**

*IP协议*
- **IPv4地址**：32位，点分十进制表示
- **子网掩码**：区分网络部分和主机部分
- **私有地址**：
  - A类：10.0.0.0/8
  - B类：**********/12
  - C类：***********/16

*TCP协议*
- **可靠传输**：确认应答、超时重传
- **流量控制**：滑动窗口机制
- **拥塞控制**：慢开始、拥塞避免、快重传、快恢复
- **三次握手**：建立连接
- **四次挥手**：断开连接

*UDP协议*
- **无连接**：发送数据前无需建立连接
- **不可靠**：不保证数据传输可靠性
- **高效**：头部开销小，传输效率高

**网络设备**

*集线器（Hub）*
- 物理层设备，共享带宽
- 半双工通信，存在冲突域

*交换机（Switch）*
- 数据链路层设备，独享带宽
- 全双工通信，每个端口独立冲突域
- MAC地址学习和转发

*路由器（Router）*
- 网络层设备，连接不同网络
- 路由选择，IP地址转发
- 分割广播域

**局域网技术**

*以太网*
- **CSMA/CD**：载波监听多路访问/冲突检测
- **最小帧长**：64字节
- **最大帧长**：1518字节

*无线局域网（WLAN）*
- **IEEE 802.11标准族**
- **CSMA/CA**：载波监听多路访问/冲突避免
- **安全协议**：WEP、WPA、WPA2

**网络安全**

*防火墙*
- **包过滤防火墙**：基于IP地址和端口过滤
- **状态检测防火墙**：跟踪连接状态
- **应用层防火墙**：深度包检测

*VPN*
- **点对点隧道协议（PPTP）**
- **第二层隧道协议（L2TP）**
- **Internet协议安全（IPSec）**

### 2. 数据库技术

#### 2.1 数据库基本概念

**数据库系统三级模式结构**

*外模式（用户模式）*
- 用户看到的数据视图
- 一个数据库可以有多个外模式
- 提供数据安全性和逻辑独立性

*概念模式（逻辑模式）*
- 数据库中全体数据的逻辑结构
- 一个数据库只有一个概念模式
- 数据库设计的核心

*内模式（物理模式）*
- 数据的物理存储结构
- 存储方式、索引方式、数据分布

*两级映像*
- **外模式/概念模式映像**：保证逻辑独立性
- **概念模式/内模式映像**：保证物理独立性

**数据模型**

*实体-联系模型（E-R模型）*
- **实体（Entity）**：客观存在的事物
- **属性（Attribute）**：实体的特征
- **联系（Relationship）**：实体间的关联
- **联系类型**：
  - 一对一（1:1）
  - 一对多（1:n）
  - 多对多（m:n）

*关系模型*
- **关系（Relation）**：二维表
- **元组（Tuple）**：表中的行
- **属性（Attribute）**：表中的列
- **主键（Primary Key）**：唯一标识元组的属性集
- **外键（Foreign Key）**：引用其他关系主键的属性

*层次模型*
- 树形结构，每个节点最多有一个父节点
- 适合表示一对多关系
- 缺点：不能直接表示多对多关系

*网状模型*
- 图形结构，节点可以有多个父节点
- 可以表示多对多关系
- 结构复杂，操作困难

#### 2.2 关系代数

**基本运算**

*选择（σ）*
- σ条件(R)：从关系R中选取满足条件的元组
- 例：σ年龄>20(学生)

*投影（π）*
- π属性列表(R)：从关系R中选取指定属性列
- 例：π姓名,年龄(学生)

*并（∪）*
- R∪S：属于R或属于S的元组集合
- 要求R和S模式相同

*差（-）*
- R-S：属于R但不属于S的元组集合

*笛卡尔积（×）*
- R×S：R和S所有元组的组合

**扩展运算**

*交（∩）*
- R∩S：既属于R又属于S的元组集合
- R∩S = R-(R-S)

*连接（⋈）*
- **θ连接**：R⋈A θ B S，满足连接条件A θ B
- **等值连接**：θ为等号的连接
- **自然连接**：去除重复属性的等值连接

*除（÷）*
- R÷S：R中包含S所有属性值的元组在其余属性上的投影

#### 2.3 SQL语言

**数据定义语言（DDL）**

*创建表*
```sql
CREATE TABLE 表名 (
    列名1 数据类型 [约束],
    列名2 数据类型 [约束],
    ...
    [表级约束]
);
```

*修改表*
```sql
ALTER TABLE 表名 ADD 列名 数据类型;
ALTER TABLE 表名 DROP COLUMN 列名;
ALTER TABLE 表名 ALTER COLUMN 列名 数据类型;
```

*删除表*
```sql
DROP TABLE 表名;
```

**数据操作语言（DML）**

*查询语句*
```sql
SELECT [DISTINCT] 列名列表
FROM 表名列表
[WHERE 条件]
[GROUP BY 列名 [HAVING 条件]]
[ORDER BY 列名 [ASC|DESC]];
```

*插入语句*
```sql
INSERT INTO 表名 (列名列表) VALUES (值列表);
INSERT INTO 表名 SELECT语句;
```

*更新语句*
```sql
UPDATE 表名 SET 列名=值 [WHERE 条件];
```

*删除语句*
```sql
DELETE FROM 表名 [WHERE 条件];
```

**数据控制语言（DCL）**

*授权*
```sql
GRANT 权限 ON 对象 TO 用户 [WITH GRANT OPTION];
```

*撤销权限*
```sql
REVOKE 权限 ON 对象 FROM 用户;
```

**完整性约束**

*实体完整性*
- 主键约束：PRIMARY KEY
- 唯一约束：UNIQUE

*参照完整性*
- 外键约束：FOREIGN KEY REFERENCES

*用户定义完整性*
- 检查约束：CHECK
- 非空约束：NOT NULL
- 默认值：DEFAULT

#### 2.4 数据库设计

**需求分析**
- 功能需求：系统要实现的功能
- 数据需求：需要存储和处理的数据
- 性能需求：响应时间、吞吐量等
- 安全需求：访问控制、数据保护等

**概念设计**
- 设计E-R图
- 确定实体、属性和联系
- 处理多值属性、复合属性
- 解决联系的多义性

**逻辑设计**
- E-R图转换为关系模式
- 转换规则：
  - 实体→关系
  - 1:1联系→合并关系或单独关系
  - 1:n联系→在n端加外键
  - m:n联系→单独关系

**物理设计**
- 确定存储结构
- 设计索引
- 选择存储设备
- 优化存储空间

#### 2.5 数据库规范化

**函数依赖**
- **完全函数依赖**：Y完全依赖于X，X的任何真子集都不能决定Y
- **部分函数依赖**：Y部分依赖于X，X的某个真子集可以决定Y
- **传递函数依赖**：X→Y，Y→Z，且Y不依赖于Z，则Z传递依赖于X

**范式**

*第一范式（1NF）*
- 关系中每个属性都是不可分的原子值
- 消除非原子属性

*第二范式（2NF）*
- 满足1NF，且非主属性完全依赖于主键
- 消除部分函数依赖

*第三范式（3NF）*
- 满足2NF，且非主属性不传递依赖于主键
- 消除传递函数依赖

*BC范式（BCNF）*
- 满足3NF，且每个决定因素都是候选键
- 消除主属性对候选键的部分和传递依赖

**模式分解**
- **无损连接分解**：分解后通过自然连接能恢复原关系
- **保持函数依赖分解**：分解后函数依赖集的并等于原函数依赖集

#### 2.6 事务管理

**ACID特性**
- **原子性（Atomicity）**：事务是不可分割的工作单位
- **一致性（Consistency）**：事务前后数据库状态一致
- **隔离性（Isolation）**：事务间相互独立
- **持久性（Durability）**：事务提交后永久保存

**并发控制**

*封锁协议*
- **一级封锁协议**：事务结束前对修改数据加X锁
- **二级封锁协议**：一级基础上，读数据前加S锁，读完释放
- **三级封锁协议**：一级基础上，读数据前加S锁，事务结束释放

*两段锁协议（2PL）*
- **扩展阶段**：只能申请锁，不能释放锁
- **收缩阶段**：只能释放锁，不能申请锁

*死锁处理*
- **预防**：一次性申请所有锁
- **检测**：等待图法检测环路
- **解除**：撤销代价最小的事务

**恢复技术**

*故障类型*
- **事务故障**：程序错误、用户中断
- **系统故障**：停电、操作系统崩溃
- **介质故障**：磁盘损坏、磁头碰撞

*恢复策略*
- **数据转储**：定期备份数据库
- **日志文件**：记录事务操作
- **检查点**：强制写回缓冲区数据

*恢复算法*
- **UNDO**：撤销未完成事务的操作
- **REDO**：重做已提交事务的操作

### 3. 系统开发和运行维护

#### 3.1 软件工程

**软件生命周期**
1. **计划阶段**：问题定义、可行性研究
2. **分析阶段**：需求分析
3. **设计阶段**：概要设计、详细设计
4. **实现阶段**：编码、单元测试
5. **测试阶段**：集成测试、系统测试
6. **运行维护阶段**：系统维护

**开发方法**

*瀑布模型*
- 线性顺序，阶段性明确
- 适合需求明确、变化少的项目
- 缺点：缺乏灵活性

*原型模型*
- 快速构建原型，用户评估
- 适合需求不明确的项目
- 类型：抛弃型、演化型

*螺旋模型*
- 结合瀑布模型和原型模型
- 强调风险分析
- 适合大型复杂项目

*敏捷方法*
- 迭代开发，快速响应变化
- 重视个体、交互、软件、客户合作
- 代表方法：Scrum、XP

**项目管理**

*时间管理*
- **关键路径法（CPM）**：确定项目最短完成时间
- **PERT网络**：考虑不确定性的网络计划技术
- **甘特图**：直观显示任务时间安排

*成本管理*
- **工作量估算**：功能点法、代码行数法
- **成本估算模型**：COCOMO模型
- **成本控制**：挣得值分析

*质量管理*
- **质量保证（QA）**：过程改进，预防缺陷
- **质量控制（QC）**：检查产品，发现缺陷
- **软件度量**：复杂度、可靠性、可维护性

*风险管理*
- **风险识别**：技术风险、管理风险、外部风险
- **风险评估**：概率×影响度
- **风险控制**：风险规避、转移、减轻、接受

#### 3.2 系统分析与设计

**结构化分析方法**

*数据流图（DFD）*
- **外部实体**：系统外部的人或组织
- **处理**：数据变换过程
- **数据存储**：数据保存位置
- **数据流**：数据传递路径
- **层次化**：顶层→0层→1层→...

*数据字典（DD）*
- **数据流条目**：数据流名 = 数据元素
- **数据存储条目**：数据存储名 = {数据结构}
- **处理条目**：处理名 = 输入数据流+输出数据流
- **符号规约**：
  - = ：定义
  - + ：连接
  - [] ：选择
  - {} ：重复
  - () ：可选

*实体关系图（ERD）*
- **实体**：矩形框表示
- **关系**：菱形框表示
- **属性**：椭圆形表示
- **基数**：1:1、1:n、m:n

**面向对象分析方法**

*统一建模语言（UML）*
- **用例图**：系统功能视图
- **类图**：系统静态结构
- **序列图**：对象间消息传递
- **活动图**：处理流程
- **状态图**：对象状态变化

*面向对象基本概念*
- **封装**：数据和方法结合，隐藏内部实现
- **继承**：子类继承父类属性和方法
- **多态**：同一接口不同实现

**系统设计**

*概要设计*
- **系统架构设计**：确定系统总体结构
- **数据库设计**：概念设计、逻辑设计
- **接口设计**：用户接口、外部接口

*详细设计*
- **模块设计**：算法设计、数据结构设计
- **代码设计**：编码标准、命名规范
- **数据库物理设计**：存储结构、索引设计

#### 3.3 软件测试

**测试方法**

*黑盒测试*
- **等价类划分**：有效等价类、无效等价类
- **边界值分析**：测试边界值和临界值
- **判定表测试**：复杂业务逻辑测试
- **因果图测试**：输入条件组合测试

*白盒测试*
- **语句覆盖**：每条语句至少执行一次
- **判定覆盖**：每个判定的真假值至少执行一次
- **条件覆盖**：每个条件的真假值至少执行一次
- **路径覆盖**：每条执行路径至少执行一次

*灰盒测试*
- 结合黑盒和白盒测试
- 基于接口设计的功能测试

**测试层次**

*单元测试*
- 测试最小可测试单元
- 使用测试桩和测试驱动

*集成测试*
- **大爆炸集成**：所有模块一次性集成
- **自顶向下集成**：使用测试桩
- **自底向上集成**：使用测试驱动
- **三明治集成**：结合自顶向下和自底向上

*系统测试*
- **功能测试**：验证系统功能
- **性能测试**：负载测试、压力测试
- **安全测试**：权限、加密、漏洞
- **兼容性测试**：平台、浏览器兼容

*验收测试*
- **α测试**：内部用户测试
- **β测试**：外部用户测试

### 4. 安全性知识

#### 4.1 网络安全

**威胁类型**
- **被动攻击**：窃听、流量分析
- **主动攻击**：伪装、重放、修改、拒绝服务

**安全服务**
- **机密性**：防止信息泄露
- **完整性**：防止信息被篡改
- **可用性**：确保系统正常运行
- **认证性**：验证用户身份
- **不可否认性**：防止否认已发生的行为

**加密技术**

*对称加密*
- **特点**：加密和解密使用相同密钥
- **算法**：DES、3DES、AES
- **优点**：速度快，效率高
- **缺点**：密钥分发困难

*非对称加密*
- **特点**：使用公钥和私钥对
- **算法**：RSA、DSA、ECC
- **优点**：密钥分发容易
- **缺点**：速度慢

*数字签名*
- **原理**：私钥签名，公钥验证
- **功能**：认证、完整性、不可否认
- **算法**：RSA签名、DSA、ECDSA

*数字证书*
- **组成**：用户信息、公钥、CA签名
- **作用**：绑定公钥和身份
- **层次**：根CA→中间CA→用户证书

#### 4.2 访问控制

**访问控制模型**

*自主访问控制（DAC）*
- 资源拥有者决定访问权限
- 使用访问控制表（ACL）
- 灵活但安全性相对较低

*强制访问控制（MAC）*
- 系统根据安全标签强制控制
- 用户和资源都有安全级别
- 安全性高但灵活性差

*基于角色的访问控制（RBAC）*
- 用户→角色→权限
- 最小特权原则
- 职责分离原则

**身份认证**

*认证因子*
- **知识因子**：密码、PIN码
- **所有因子**：智能卡、USB密钥
- **生物因子**：指纹、虹膜、声纹

*多因子认证*
- 结合两种或以上认证因子
- 提高安全性

#### 4.3 计算机病毒

**病毒特征**
- **破坏性**：破坏数据、系统
- **传染性**：自我复制和传播
- **潜伏性**：隐藏在正常程序中
- **触发性**：特定条件下激活

**病毒分类**
- **按感染方式**：文件型、引导型、宏病毒
- **按表现形式**：良性病毒、恶性病毒
- **按传播媒介**：网络病毒、邮件病毒

**防护措施**
- **预防**：安装防病毒软件、定期更新
- **检测**：特征码检测、行为检测
- **清除**：隔离、删除、修复

### 5. 标准化知识

#### 5.1 标准化体系

**标准层次**
- **国际标准**：ISO、IEC、ITU
- **国家标准（GB）**：强制性标准、推荐性标准
- **行业标准**：部门或行业制定
- **企业标准**：企业内部标准

**软件工程标准**
- **ISO/IEC 12207**：软件生命周期过程
- **ISO 9000族**：质量管理体系
- **CMM/CMMI**：软件能力成熟度模型

#### 5.2 文档标准

**国家标准GB 8567**
- **可行性研究报告**：GB/T 8567.1
- **项目开发计划**：GB/T 8567.2
- **软件需求说明书**：GB/T 8567.3
- **概要设计说明书**：GB/T 8567.4
- **详细设计说明书**：GB/T 8567.5
- **用户手册**：GB/T 8567.6
- **测试计划**：GB/T 8567.7
- **测试报告**：GB/T 8567.8

### 6. 信息化基础知识

#### 6.1 信息化战略

**国家信息化战略**
- **信息基础设施**：网络基础设施建设
- **信息资源开发利用**：政务信息化、企业信息化
- **信息技术应用**：电子政务、电子商务
- **信息产业发展**：软件产业、集成电路产业
- **信息化人才培养**：教育培训、人才引进
- **信息安全保障**：网络安全、数据保护

**企业信息化**
- **ERP**：企业资源计划
- **CRM**：客户关系管理
- **SCM**：供应链管理
- **BI**：商业智能
- **KM**：知识管理

#### 6.2 电子商务

**电子商务模式**
- **B2B**：企业对企业
- **B2C**：企业对消费者
- **C2C**：消费者对消费者
- **O2O**：线上到线下

**电子支付**
- **第三方支付**：支付宝、微信支付
- **网上银行**：银行网络支付
- **数字货币**：央行数字货币

#### 6.3 法律法规

**信息化相关法律**
- **网络安全法**：网络安全保护义务
- **数据安全法**：数据安全保护制度
- **个人信息保护法**：个人信息处理规则
- **电子签名法**：电子签名法律效力

---

## 考试科目2：数据库系统设计与管理

### 1. 数据库设计详解

#### 1.1 需求分析阶段

**用户需求收集**
- **业务调研**：了解企业业务流程
- **数据调研**：分析现有数据资源
- **功能需求**：确定系统功能范围
- **性能需求**：响应时间、并发用户数、数据量

**数据流分析**
- **输入数据流**：数据来源、格式、频率
- **输出数据流**：报表、查询结果
- **存储需求**：数据保存期限、增长速度

**约束条件分析**
- **硬件环境**：服务器配置、存储容量
- **软件环境**：操作系统、数据库管理系统
- **安全要求**：访问控制、数据加密
- **法规遵循**：行业标准、法律要求

#### 1.2 概念设计详解

**实体识别**
- **实体命名规范**：使用业务术语，避免技术术语
- **实体分类**：
  - 基本实体：独立存在的事物
  - 依赖实体：依赖其他实体存在
  - 关联实体：多对多关系产生的实体

**属性设计**
- **属性分类**：
  - 简单属性：不可再分
  - 复合属性：可分解为更小部分
  - 单值属性：只有一个值
  - 多值属性：可有多个值
  - 存储属性：直接存储
  - 派生属性：由其他属性计算得出

**关系设计**
- **关系类型确定**：
  - 1:1关系：身份证和人员
  - 1:n关系：部门和员工
  - m:n关系：学生和课程

**E-R图绘制规范**
- **实体**：矩形框，实体名位于框内
- **属性**：椭圆形，属性名位于椭圆内
- **关系**：菱形框，关系名位于框内
- **主键属性**：下划线标识
- **多值属性**：双椭圆形
- **派生属性**：虚椭圆形
- **弱实体**：双矩形框

#### 1.3 逻辑设计详解

**E-R图转关系模式规则**

*实体转换规则*
```
实体E → 关系R
属性 → 关系的属性
主键 → 关系的主键
```

*1:1联系转换*
- **方法1**：任选一个关系，加入另一个关系的主键作为外键
- **方法2**：两个关系合并为一个
- **方法3**：联系单独成为一个关系

*1:n联系转换*
- 在n端关系中加入1端关系的主键作为外键

*m:n联系转换*
- 联系单独转换为关系
- 关系的主键为两端实体主键的组合

**关系模式优化**
- **合并关系**：减少连接操作
- **分解关系**：提高查询效率
- **增加冗余**：空间换时间
- **消除冗余**：避免数据不一致

#### 1.4 物理设计详解

**存储结构设计**

*表空间设计*
- **系统表空间**：存储数据字典
- **用户表空间**：存储用户数据
- **临时表空间**：排序、哈希操作
- **撤销表空间**：事务回滚信息

*分区设计*
- **范围分区**：按数值范围分区
- **哈希分区**：按哈希值分区
- **列表分区**：按具体值分区
- **复合分区**：多级分区

**索引设计**

*索引类型选择*
- **B+树索引**：
  - 适用：等值查询、范围查询
  - 结构：叶子节点存储数据，非叶子节点存储索引
- **哈希索引**：
  - 适用：等值查询
  - 特点：查询速度快，不支持范围查询
- **位图索引**：
  - 适用：低基数列
  - 特点：空间效率高，适合数据仓库

*索引策略*
- **主键索引**：自动创建，唯一性保证
- **外键索引**：提高连接查询效率
- **复合索引**：多列组合索引，注意列顺序
- **部分索引**：只对满足条件的行建索引
- **函数索引**：对函数表达式建索引

**性能优化**

*查询优化*
- **统计信息**：表大小、索引选择性、数据分布
- **执行计划**：查询优化器选择的执行路径
- **提示（Hint）**：强制使用特定索引或连接方式

*存储优化*
- **数据压缩**：减少存储空间和I/O
- **分区裁剪**：只扫描相关分区
- **并行处理**：多CPU并行执行

### 2. 数据库应用系统设计详解

#### 2.1 系统架构设计

**三层架构**
- **表示层（Presentation Layer）**：
  - 用户界面
  - 输入验证
  - 格式化输出
- **业务逻辑层（Business Logic Layer）**：
  - 业务规则处理
  - 事务控制
  - 数据验证
- **数据访问层（Data Access Layer）**：
  - 数据库连接
  - SQL执行
  - 结果集处理

**微服务架构**
- **服务分解**：按业务功能分解
- **服务通信**：REST API、消息队列
- **数据一致性**：分布式事务、最终一致性
- **服务治理**：服务注册、发现、监控

#### 2.2 数据库连接技术

**JDBC（Java Database Connectivity）**
```java
// 加载驱动
Class.forName("com.mysql.cj.jdbc.Driver");

// 建立连接
Connection conn = DriverManager.getConnection(
    "**********************************", 
    "username", "password");

// 执行SQL
PreparedStatement pstmt = conn.prepareStatement(
    "SELECT * FROM users WHERE id = ?");
pstmt.setInt(1, userId);
ResultSet rs = pstmt.executeQuery();
```

**ODBC（Open Database Connectivity）**
- **连接字符串**：指定数据源、用户名、密码
- **API函数**：SQLConnect、SQLExecute、SQLFetch
- **数据类型映射**：C数据类型与SQL数据类型对应

**连接池技术**
- **连接复用**：避免频繁创建和销毁连接
- **参数配置**：
  - 初始连接数：系统启动时创建的连接数
  - 最大连接数：允许的最大连接数
  - 连接超时：连接空闲时间上限
- **实现框架**：HikariCP、Druid、C3P0

#### 2.3 事务处理设计

**事务边界设计**
- **细粒度事务**：单个操作为一个事务
- **粗粒度事务**：多个相关操作为一个事务
- **业务事务**：完整业务流程为一个事务

**分布式事务**
- **两阶段提交（2PC）**：
  - 准备阶段：所有参与者准备提交
  - 提交阶段：协调者决定提交或回滚
- **三阶段提交（3PC）**：
  - CanCommit阶段：询问是否可以提交
  - PreCommit阶段：发送预提交请求
  - DoCommit阶段：正式提交或回滚

**事务隔离级别**
- **READ UNCOMMITTED**：
  - 问题：脏读、不可重复读、幻读
  - 性能：最高
- **READ COMMITTED**：
  - 问题：不可重复读、幻读
  - 应用：Oracle默认级别
- **REPEATABLE READ**：
  - 问题：幻读
  - 应用：MySQL默认级别
- **SERIALIZABLE**：
  - 问题：无
  - 性能：最低

### 3. 数据库实施详解

#### 3.1 数据库管理系统配置

**MySQL配置**
```ini
[mysqld]
# 基本设置
port = 3306
datadir = /var/lib/mysql
socket = /var/lib/mysql/mysql.sock

# 性能设置
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
max_connections = 200
query_cache_size = 64M

# 安全设置
bind-address = 127.0.0.1
sql_mode = STRICT_TRANS_TABLES
```

**Oracle配置**
```sql
-- 内存设置
ALTER SYSTEM SET sga_target = 1G SCOPE=SPFILE;
ALTER SYSTEM SET pga_aggregate_target = 512M SCOPE=SPFILE;

-- 日志设置
ALTER SYSTEM SET log_archive_dest_1 = 'LOCATION=/archive' SCOPE=SPFILE;
ALTER SYSTEM SET log_archive_format = 'arch_%t_%s_%r.arc' SCOPE=SPFILE;
```

**SQL Server配置**
- **内存配置**：最大服务器内存、最小服务器内存
- **并行度配置**：最大并行度、并行度成本阈值
- **备份配置**：备份压缩、备份校验

#### 3.2 数据导入导出

**批量数据导入**
```sql
-- MySQL LOAD DATA
LOAD DATA INFILE 'data.csv'
INTO TABLE students
FIELDS TERMINATED BY ','
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

-- SQL Server BULK INSERT
BULK INSERT students
FROM 'C:\data.csv'
WITH (
    FIELDTERMINATOR = ',',
    ROWTERMINATOR = '\n',
    FIRSTROW = 2
);
```

**数据同步**
- **增量同步**：只同步变化的数据
- **全量同步**：同步所有数据
- **实时同步**：基于日志的实时复制
- **定时同步**：按计划执行同步任务

#### 3.3 性能监控与调优

**性能指标监控**
- **响应时间**：SQL执行时间
- **吞吐量**：每秒处理的事务数
- **资源利用率**：CPU、内存、磁盘I/O
- **锁等待**：锁冲突次数和等待时间

**SQL调优**
```sql
-- 查看执行计划
EXPLAIN SELECT * FROM orders o
JOIN customers c ON o.customer_id = c.id
WHERE o.order_date > '2023-01-01';

-- 添加索引
CREATE INDEX idx_order_date ON orders(order_date);
CREATE INDEX idx_customer_id ON orders(customer_id);

-- 重写查询
SELECT o.*, c.name
FROM orders o, customers c
WHERE o.customer_id = c.id
  AND o.order_date > '2023-01-01';
```

### 4. 数据库运行管理详解

#### 4.1 备份与恢复

**备份策略**

*完全备份*
```sql
-- MySQL
mysqldump -u root -p --all-databases > full_backup.sql

-- SQL Server
BACKUP DATABASE MyDB TO DISK = 'C:\Backup\MyDB_Full.bak'
WITH FORMAT, COMPRESSION;

-- Oracle
RMAN> BACKUP DATABASE;
```

*增量备份*
```sql
-- MySQL二进制日志
mysqlbinlog --start-datetime='2023-01-01 00:00:00' \
           --stop-datetime='2023-01-02 00:00:00' \
           mysql-bin.000001 > incremental.sql

-- SQL Server差异备份
BACKUP DATABASE MyDB TO DISK = 'C:\Backup\MyDB_Diff.bak'
WITH DIFFERENTIAL, COMPRESSION;
```

**恢复策略**

*完全恢复*
```sql
-- MySQL
mysql -u root -p < full_backup.sql

-- SQL Server
RESTORE DATABASE MyDB FROM DISK = 'C:\Backup\MyDB_Full.bak'
WITH REPLACE;
```

*点时间恢复*
```sql
-- SQL Server
RESTORE DATABASE MyDB FROM DISK = 'C:\Backup\MyDB_Full.bak'
WITH NORECOVERY;
RESTORE LOG MyDB FROM DISK = 'C:\Backup\MyDB_Log.trn'
WITH STOPAT = '2023-01-01 12:00:00';
```

#### 4.2 安全管理

**用户管理**
```sql
-- 创建用户
CREATE USER 'app_user'@'localhost' IDENTIFIED BY 'password';

-- 授权
GRANT SELECT, INSERT, UPDATE ON mydb.* TO 'app_user'@'localhost';

-- 撤销权限
REVOKE INSERT ON mydb.orders FROM 'app_user'@'localhost';

-- 删除用户
DROP USER 'app_user'@'localhost';
```

**角色管理**
```sql
-- 创建角色
CREATE ROLE db_reader;

-- 给角色授权
GRANT SELECT ON mydb.* TO db_reader;

-- 将角色分配给用户
GRANT db_reader TO 'user1'@'localhost';
```

**审计配置**
```sql
-- MySQL开启审计日志
SET GLOBAL general_log = 'ON';
SET GLOBAL general_log_file = '/var/log/mysql/general.log';

-- SQL Server审计
CREATE SERVER AUDIT MyAudit
TO FILE (FILEPATH = 'C:\Audit\');

ALTER SERVER AUDIT MyAudit WITH (STATE = ON);
```

#### 4.3 性能调优

**索引优化**
```sql
-- 分析索引使用情况
SELECT 
    schema_name,
    table_name,
    index_name,
    non_unique,
    cardinality
FROM information_schema.statistics
WHERE table_schema = 'mydb'
ORDER BY cardinality DESC;

-- 删除未使用的索引
DROP INDEX idx_unused ON my_table;

-- 创建复合索引
CREATE INDEX idx_composite ON orders(customer_id, order_date, status);
```

**查询优化**
```sql
-- 避免SELECT *
SELECT order_id, customer_id, order_date 
FROM orders;

-- 使用LIMIT
SELECT * FROM products ORDER BY price DESC LIMIT 10;

-- 使用EXISTS代替IN
SELECT * FROM customers c
WHERE EXISTS (
    SELECT 1 FROM orders o 
    WHERE o.customer_id = c.id
);
```

### 5. SQL进阶知识

#### 5.1 复杂查询

**窗口函数**
```sql
-- 排名函数
SELECT 
    employee_id,
    salary,
    ROW_NUMBER() OVER (ORDER BY salary DESC) as row_num,
    RANK() OVER (ORDER BY salary DESC) as rank_num,
    DENSE_RANK() OVER (ORDER BY salary DESC) as dense_rank_num
FROM employees;

-- 分组排名
SELECT 
    department_id,
    employee_id,
    salary,
    ROW_NUMBER() OVER (PARTITION BY department_id ORDER BY salary DESC) as dept_rank
FROM employees;

-- 累计统计
SELECT 
    order_date,
    amount,
    SUM(amount) OVER (ORDER BY order_date) as running_total,
    AVG(amount) OVER (ORDER BY order_date ROWS BETWEEN 2 PRECEDING AND CURRENT ROW) as moving_avg
FROM orders;
```

**公用表表达式（CTE）**
```sql
-- 递归CTE - 组织架构
WITH RECURSIVE employee_hierarchy AS (
    -- 锚点：顶级经理
    SELECT employee_id, name, manager_id, 1 as level
    FROM employees
    WHERE manager_id IS NULL
    
    UNION ALL
    
    -- 递归：下级员工
    SELECT e.employee_id, e.name, e.manager_id, eh.level + 1
    FROM employees e
    JOIN employee_hierarchy eh ON e.manager_id = eh.employee_id
)
SELECT * FROM employee_hierarchy
ORDER BY level, employee_id;
```

**分析函数**
```sql
-- 同比环比分析
SELECT 
    year,
    month,
    sales,
    LAG(sales, 1) OVER (ORDER BY year, month) as prev_month_sales,
    LAG(sales, 12) OVER (ORDER BY year, month) as prev_year_sales,
    sales - LAG(sales, 1) OVER (ORDER BY year, month) as mom_change,
    sales - LAG(sales, 12) OVER (ORDER BY year, month) as yoy_change
FROM monthly_sales;
```

#### 5.2 存储过程和函数

**存储过程**
```sql
-- MySQL存储过程
DELIMITER //
CREATE PROCEDURE GetEmployeesByDept(IN dept_id INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE emp_name VARCHAR(100);
    DECLARE emp_cursor CURSOR FOR 
        SELECT name FROM employees WHERE department_id = dept_id;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN emp_cursor;
    read_loop: LOOP
        FETCH emp_cursor INTO emp_name;
        IF done THEN
            LEAVE read_loop;
        END IF;
        SELECT emp_name;
    END LOOP;
    CLOSE emp_cursor;
END //
DELIMITER ;

-- SQL Server存储过程
CREATE PROCEDURE sp_GetSalesByPeriod
    @StartDate DATE,
    @EndDate DATE,
    @TotalSales MONEY OUTPUT
AS
BEGIN
    SELECT @TotalSales = SUM(amount)
    FROM orders
    WHERE order_date BETWEEN @StartDate AND @EndDate;
    
    IF @TotalSales IS NULL
        SET @TotalSales = 0;
END;
```

**函数**
```sql
-- MySQL函数
DELIMITER //
CREATE FUNCTION CalculateAge(birth_date DATE)
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE age INT;
    SET age = TIMESTAMPDIFF(YEAR, birth_date, CURDATE());
    RETURN age;
END //
DELIMITER ;

-- 使用函数
SELECT name, birth_date, CalculateAge(birth_date) as age
FROM employees;
```

**触发器**
```sql
-- INSERT触发器
CREATE TRIGGER tr_audit_insert
AFTER INSERT ON employees
FOR EACH ROW
BEGIN
    INSERT INTO audit_log (
        table_name, 
        operation, 
        record_id, 
        old_values, 
        new_values, 
        user_name, 
        timestamp
    ) VALUES (
        'employees',
        'INSERT',
        NEW.employee_id,
        NULL,
        CONCAT('name:', NEW.name, ',salary:', NEW.salary),
        USER(),
        NOW()
    );
END;

-- UPDATE触发器
CREATE TRIGGER tr_salary_history
AFTER UPDATE ON employees
FOR EACH ROW
BEGIN
    IF OLD.salary != NEW.salary THEN
        INSERT INTO salary_history (
            employee_id,
            old_salary,
            new_salary,
            change_date,
            changed_by
        ) VALUES (
            NEW.employee_id,
            OLD.salary,
            NEW.salary,
            NOW(),
            USER()
        );
    END IF;
END;
```

#### 5.3 事务处理高级特性

**事务隔离级别实例**
```sql
-- 设置事务隔离级别
SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

-- 开始事务
BEGIN TRANSACTION;

-- 设置保存点
SAVEPOINT sp1;

UPDATE accounts SET balance = balance - 1000 WHERE account_id = 1;

SAVEPOINT sp2;

UPDATE accounts SET balance = balance + 1000 WHERE account_id = 2;

-- 回滚到保存点
ROLLBACK TO sp1;

-- 提交事务
COMMIT;
```

**死锁处理**
```sql
-- 检测死锁
SELECT 
    r.session_id,
    r.blocking_session_id,
    t.text as sql_text,
    r.wait_type,
    r.wait_time
FROM sys.dm_exec_requests r
CROSS APPLY sys.dm_exec_sql_text(r.sql_handle) t
WHERE r.blocking_session_id > 0;

-- 杀死阻塞进程
KILL 52; -- session_id
```

### 6. 网络环境下的数据库详解

#### 6.1 分布式数据库系统

**数据分布策略**

*水平分片*
```sql
-- 按范围分片
CREATE TABLE orders_2023 (
    order_id INT PRIMARY KEY,
    order_date DATE,
    customer_id INT,
    amount DECIMAL(10,2),
    CONSTRAINT chk_date CHECK (order_date >= '2023-01-01' AND order_date < '2024-01-01')
);

-- 按哈希分片
CREATE TABLE customers_shard1 (
    customer_id INT PRIMARY KEY,
    name VARCHAR(100),
    email VARCHAR(100),
    CONSTRAINT chk_shard CHECK (customer_id % 3 = 1)
);
```

*垂直分片*
```sql
-- 基本信息表
CREATE TABLE customer_basic (
    customer_id INT PRIMARY KEY,
    name VARCHAR(100),
    email VARCHAR(100)
);

-- 详细信息表
CREATE TABLE customer_detail (
    customer_id INT PRIMARY KEY,
    address TEXT,
    phone VARCHAR(20),
    FOREIGN KEY (customer_id) REFERENCES customer_basic(customer_id)
);
```

**分布式查询优化**
- **查询分解**：将复杂查询分解为子查询
- **数据传输最小化**：减少网络传输量
- **并行执行**：多节点并行处理
- **结果合并**：合并各节点查询结果

#### 6.2 数据复制

**主从复制**
```sql
-- 主库配置 (MySQL)
[mysqld]
server-id = 1
log-bin = mysql-bin
binlog-format = ROW

-- 从库配置
[mysqld]
server-id = 2
relay-log = relay-bin
read-only = 1

-- 设置复制
CHANGE MASTER TO
    MASTER_HOST = '*************',
    MASTER_USER = 'repl_user',
    MASTER_PASSWORD = 'password',
    MASTER_LOG_FILE = 'mysql-bin.000001',
    MASTER_LOG_POS = 154;

START SLAVE;
```

**主主复制**
```sql
-- 服务器1配置
[mysqld]
server-id = 1
log-bin = mysql-bin
auto-increment-increment = 2
auto-increment-offset = 1

-- 服务器2配置
[mysqld]
server-id = 2
log-bin = mysql-bin
auto-increment-increment = 2
auto-increment-offset = 2
```

#### 6.3 集群技术

**MySQL集群（NDB Cluster）**
- **管理节点**：配置和监控集群
- **数据节点**：存储数据
- **SQL节点**：提供MySQL API接口

**Oracle RAC**
- **共享存储**：所有节点共享同一存储
- **Cache Fusion**：节点间缓存同步
- **负载均衡**：连接自动分发到不同节点

### 7. 数据库安全详解

#### 7.1 访问控制机制

**基于角色的访问控制（RBAC）**
```sql
-- 创建角色层次
CREATE ROLE hr_manager;
CREATE ROLE hr_employee;

-- 设置角色继承
GRANT hr_employee TO hr_manager;

-- 角色权限分配
GRANT SELECT, INSERT, UPDATE ON employees TO hr_employee;
GRANT DELETE ON employees TO hr_manager;
GRANT ALL ON salary_history TO hr_manager;

-- 用户角色分配
CREATE USER 'john'@'%' IDENTIFIED BY 'password';
GRANT hr_manager TO 'john'@'%';

CREATE USER 'mary'@'%' IDENTIFIED BY 'password';
GRANT hr_employee TO 'mary'@'%';
```

**行级安全策略**
```sql
-- SQL Server行级安全
CREATE FUNCTION fn_security_predicate(@user_department INT)
RETURNS TABLE
WITH SCHEMABINDING
AS
RETURN SELECT 1 AS result
WHERE @user_department = USER_NAME() OR USER_NAME() = 'manager';

CREATE SECURITY POLICY employee_security
ADD FILTER PREDICATE fn_security_predicate(department_id) ON employees
WITH (STATE = ON);
```

#### 7.2 数据加密

**透明数据加密（TDE）**
```sql
-- SQL Server TDE
-- 创建主密钥
CREATE MASTER KEY ENCRYPTION BY PASSWORD = 'MyStrongPassword';

-- 创建证书
CREATE CERTIFICATE MyServerCert WITH SUBJECT = 'My DEK Certificate';

-- 创建数据库加密密钥
USE MyDatabase;
CREATE DATABASE ENCRYPTION KEY
WITH ALGORITHM = AES_256
ENCRYPTION BY SERVER CERTIFICATE MyServerCert;

-- 启用TDE
ALTER DATABASE MyDatabase SET ENCRYPTION ON;
```

**列级加密**
```sql
-- 加密敏感列
CREATE TABLE customers (
    customer_id INT PRIMARY KEY,
    name VARCHAR(100),
    ssn VARBINARY(256), -- 加密存储
    credit_card VARBINARY(256) -- 加密存储
);

-- 插入加密数据
INSERT INTO customers (customer_id, name, ssn, credit_card)
VALUES (1, 'John Doe', 
        EncryptByKey(Key_GUID('SSN_Key'), '***********'),
        EncryptByKey(Key_GUID('CC_Key'), '1234-**************'));

-- 查询解密数据
SELECT 
    customer_id,
    name,
    CONVERT(VARCHAR, DecryptByKey(ssn)) as ssn,
    CONVERT(VARCHAR, DecryptByKey(credit_card)) as credit_card
FROM customers;
```

#### 7.3 审计日志

**数据库审计配置**
```sql
-- Oracle审计
AUDIT SELECT, INSERT, UPDATE, DELETE ON sensitive_table BY ACCESS;
AUDIT CREATE TABLE BY hr;

-- 查看审计记录
SELECT username, action_name, object_name, timestamp
FROM dba_audit_trail
WHERE object_name = 'SENSITIVE_TABLE'
ORDER BY timestamp DESC;
```

**自定义审计表**
```sql
CREATE TABLE audit_log (
    audit_id INT AUTO_INCREMENT PRIMARY KEY,
    table_name VARCHAR(100),
    operation VARCHAR(10),
    user_name VARCHAR(100),
    timestamp DATETIME,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45)
);

-- 审计触发器
CREATE TRIGGER tr_audit_employees
AFTER UPDATE ON employees
FOR EACH ROW
BEGIN
    INSERT INTO audit_log (
        table_name, operation, user_name, timestamp,
        old_values, new_values, ip_address
    ) VALUES (
        'employees', 'UPDATE', USER(), NOW(),
        JSON_OBJECT('name', OLD.name, 'salary', OLD.salary),
        JSON_OBJECT('name', NEW.name, 'salary', NEW.salary),
        CONNECTION_ID()
    );
END;
```

### 8. 数据库发展趋势与新技术

#### 8.1 NoSQL数据库

**文档数据库（MongoDB）**
```javascript
// 插入文档
db.users.insertOne({
    name: "John Doe",
    email: "<EMAIL>",
    address: {
        street: "123 Main St",
        city: "New York",
        zipcode: "10001"
    },
    hobbies: ["reading", "swimming"]
});

// 查询文档
db.users.find({
    "address.city": "New York",
    hobbies: { $in: ["reading"] }
});

// 聚合查询
db.orders.aggregate([
    { $match: { status: "completed" } },
    { $group: { 
        _id: "$customer_id", 
        total: { $sum: "$amount" } 
    }},
    { $sort: { total: -1 } }
]);
```

**键值数据库（Redis）**
```redis
# 基本操作
SET user:1001 "John Doe"
GET user:1001
EXPIRE user:1001 3600

# 哈希操作
HSET user:1001 name "John Doe" email "<EMAIL>"
HGET user:1001 name
HGETALL user:1001

# 列表操作
LPUSH queue:tasks "task1" "task2"
RPOP queue:tasks

# 集合操作
SADD tags:user1001 "developer" "python" "mysql"
SINTER tags:user1001 tags:user1002
```

**图数据库（Neo4j）**
```cypher
// 创建节点和关系
CREATE (john:Person {name: 'John', age: 30})
CREATE (mary:Person {name: 'Mary', age: 25})
CREATE (company:Company {name: 'TechCorp'})
CREATE (john)-[:WORKS_FOR]->(company)
CREATE (mary)-[:WORKS_FOR]->(company)
CREATE (john)-[:FRIENDS_WITH]->(mary)

// 查询关系
MATCH (p:Person)-[:WORKS_FOR]->(c:Company)
WHERE c.name = 'TechCorp'
RETURN p.name, p.age

// 路径查询
MATCH path = (a:Person {name: 'John'})-[*1..3]-(b:Person {name: 'Alice'})
RETURN path
```

#### 8.2 数据仓库与大数据

**维度建模**
```sql
-- 事实表
CREATE TABLE fact_sales (
    sale_id BIGINT PRIMARY KEY,
    date_key INT,
    product_key INT,
    customer_key INT,
    store_key INT,
    quantity INT,
    unit_price DECIMAL(10,2),
    total_amount DECIMAL(12,2),
    FOREIGN KEY (date_key) REFERENCES dim_date(date_key),
    FOREIGN KEY (product_key) REFERENCES dim_product(product_key),
    FOREIGN KEY (customer_key) REFERENCES dim_customer(customer_key),
    FOREIGN KEY (store_key) REFERENCES dim_store(store_key)
);

-- 维度表
CREATE TABLE dim_date (
    date_key INT PRIMARY KEY,
    full_date DATE,
    year INT,
    quarter INT,
    month INT,
    week INT,
    day_of_week INT,
    is_weekend BOOLEAN
);

CREATE TABLE dim_product (
    product_key INT PRIMARY KEY,
    product_id VARCHAR(50),
    product_name VARCHAR(200),
    category VARCHAR(100),
    subcategory VARCHAR(100),
    brand VARCHAR(100)
);
```

**ETL处理**
```sql
-- 数据抽取 (Extract)
CREATE VIEW source_sales AS
SELECT 
    order_id,
    order_date,
    product_id,
    customer_id,
    store_id,
    quantity,
    unit_price,
    quantity * unit_price as total_amount
FROM operational_orders
WHERE order_date >= CURRENT_DATE - INTERVAL 1 DAY;

-- 数据转换 (Transform)
INSERT INTO fact_sales (
    sale_id,
    date_key,
    product_key,
    customer_key,
    store_key,
    quantity,
    unit_price,
    total_amount
)
SELECT 
    s.order_id,
    d.date_key,
    p.product_key,
    c.customer_key,
    st.store_key,
    s.quantity,
    s.unit_price,
    s.total_amount
FROM source_sales s
JOIN dim_date d ON s.order_date = d.full_date
JOIN dim_product p ON s.product_id = p.product_id
JOIN dim_customer c ON s.customer_id = c.customer_id
JOIN dim_store st ON s.store_id = st.store_id;
```

**OLAP查询**
```sql
-- 销售分析
SELECT 
    d.year,
    d.quarter,
    p.category,
    SUM(f.total_amount) as total_sales,
    COUNT(*) as transaction_count,
    AVG(f.total_amount) as avg_transaction
FROM fact_sales f
JOIN dim_date d ON f.date_key = d.date_key
JOIN dim_product p ON f.product_key = p.product_key
WHERE d.year = 2023
GROUP BY ROLLUP(d.year, d.quarter, p.category)
ORDER BY d.year, d.quarter, p.category;

-- 同比分析
WITH yearly_sales AS (
    SELECT 
        d.year,
        p.category,
        SUM(f.total_amount) as annual_sales
    FROM fact_sales f
    JOIN dim_date d ON f.date_key = d.date_key
    JOIN dim_product p ON f.product_key = p.product_key
    GROUP BY d.year, p.category
)
SELECT 
    current_year.year,
    current_year.category,
    current_year.annual_sales,
    previous_year.annual_sales as previous_year_sales,
    (current_year.annual_sales - previous_year.annual_sales) / previous_year.annual_sales * 100 as growth_rate
FROM yearly_sales current_year
LEFT JOIN yearly_sales previous_year 
    ON current_year.category = previous_year.category 
    AND current_year.year = previous_year.year + 1
WHERE current_year.year = 2023;
```

#### 8.3 内存数据库

**SAP HANA特性**
- **列式存储**：压缩效率高，查询性能好
- **内存计算**：数据全部加载到内存
- **并行处理**：多核CPU并行计算
- **实时分析**：事务和分析统一平台

**Redis作为数据库**
```python
import redis
import json

# 连接Redis
r = redis.Redis(host='localhost', port=6379, db=0)

# 用户会话管理
def create_session(user_id, session_data):
    session_id = f"session:{user_id}:{int(time.time())}"
    r.setex(session_id, 3600, json.dumps(session_data))
    return session_id

# 排行榜系统
def update_score(user_id, score):
    r.zadd("leaderboard", {user_id: score})

def get_top_users(limit=10):
    return r.zrevrange("leaderboard", 0, limit-1, withscores=True)

# 实时计数
def increment_counter(key):
    return r.incr(f"counter:{key}")
```

### 9. 重点考试技巧和复习要点

#### 9.1 高频考点总结

**数据库设计**
1. **E-R图绘制**：实体、属性、关系的正确表示
2. **规范化理论**：第一范式到BC范式的定义和应用
3. **函数依赖**：完全、部分、传递依赖的识别

**SQL语言**
1. **基本查询**：SELECT、WHERE、ORDER BY的使用
2. **连接查询**：内连接、外连接、自连接
3. **聚合函数**：GROUP BY、HAVING的使用
4. **子查询**：相关子查询、EXISTS的使用

**事务管理**
1. **ACID特性**：原子性、一致性、隔离性、持久性
2. **并发控制**：封锁协议、死锁处理
3. **恢复技术**：日志、检查点、备份恢复

**系统设计**
1. **三层架构**：表示层、业务层、数据层
2. **性能优化**：索引设计、查询优化
3. **安全设计**：访问控制、数据加密

#### 9.2 答题策略

**选择题技巧**
- 仔细审题，注意关键词
- 排除明显错误选项
- 利用数据库理论知识判断
- 注意SQL语法的细节

**案例分析题**
- 理解业务需求
- 画出完整的E-R图
- 写出规范的关系模式
- 考虑完整性约束

**编程题**
- 注意SQL语法规范
- 考虑查询效率
- 处理特殊情况（空值、重复等）
- 验证结果的正确性

#### 9.3 最后冲刺要点

**必背概念**
- 三级模式、两级映像
- ACID特性
- 范式定义（1NF、2NF、3NF、BCNF）
- 关系代数运算
- 索引类型和特点
- 事务隔离级别

**必会SQL**
```sql
-- 基本查询模板
SELECT [DISTINCT] column_list
FROM table_list
[WHERE condition]
[GROUP BY group_list [HAVING group_condition]]
[ORDER BY order_list]
[LIMIT count];

-- 连接查询模板
SELECT t1.col1, t2.col2
FROM table1 t1
[INNER|LEFT|RIGHT|FULL] JOIN table2 t2 ON t1.id = t2.id
WHERE condition;

-- 子查询模板
SELECT * FROM table1 
WHERE column IN (SELECT column FROM table2 WHERE condition);

-- 窗口函数模板
SELECT column, 
       function() OVER (PARTITION BY column ORDER BY column) 
FROM table;
```

**常见错误避免**
- SQL关键字大小写混用
- 忘记在GROUP BY中包含非聚合列
- WHERE和HAVING的使用混淆
- 外键约束定义错误
- 事务隔离级别理解错误

---

## 总结

这份详细的复习文档涵盖了数据库系统工程师考试的所有重要知识点。建议您：

1. **重点掌握**：数据库设计、SQL语言、事务管理
2. **理论结合实践**：多做SQL编写练习
3. **系统复习**：按章节顺序复习，建立知识体系
4. **查漏补缺**：针对薄弱环节重点突破
5. **模拟练习**：做历年真题，熟悉考试形式

祝您考试成功！