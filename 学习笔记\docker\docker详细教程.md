好的，没问题！这是一份非常详细的、从零开始的 Docker 入门教程，旨在帮助你理解 Docker 的核心概念，并掌握其基本和常用操作。

本教程将分为以下几个部分：

1.  **Docker 是什么？为什么要用它？** (核心概念)
2.  **第一步：安装 Docker** (环境准备)
3.  **核心操作：镜像与容器** (动手实践)
4.  **构建自己的镜像：Dockerfile** (定制化)
5.  **数据持久化：数据卷 (Volume)** (保存数据)
6.  **多容器编排：Docker Compose** (实战应用)
7.  **常用命令速查表** (备忘录)

---

### 1. Docker 是什么？为什么要用它？

想象一下，你在你的电脑上（比如 Windows）开发了一个网站，用到了 Python 3.8、Redis 数据库和 Nginx。现在你想把它部署到一台 Linux 服务器上。你可能会遇到以下问题：

*   服务器上的 Python 版本是 3.6，不兼容。
*   安装 Redis 和 Nginx 的过程繁琐，配置复杂。
*   你同事的电脑是 macOS，他想帮你调试，又要重新配一套环境。

**“在我的电脑上明明是好的啊！”** 这句话是不是很熟悉？

**Docker 就是为了解决这个问题而生的。**

它就像一个**标准化的集装箱**。你把你的应用程序（代码、依赖库、配置文件、运行环境）全部打包到一个“集装箱”里，这个集装箱就叫做 **Docker 镜像 (Image)**。

然后，你可以在任何安装了 Docker 的机器上（Windows, macOS, Linux）运行这个“集装箱”，运行起来的实例就叫做 **Docker 容器 (Container)**。



**使用 Docker 的好处：**

*   **环境隔离**：每个容器都有自己独立的环境，互不干扰。
*   **一次构建，处处运行**：解决了“在我电脑上是好的”这个终极难题。
*   **快速部署**：启动容器是秒级的，比虚拟机快得多。
*   **轻量高效**：Docker 直接利用宿主机的内核，资源开销小。
*   **易于扩展**：可以轻松地启动多个相同的容器来分摊流量。

**核心概念：**

*   **镜像 (Image)**：一个只读的模板，包含了运行应用程序所需的一切。可以把它看作是软件的“安装包”。
*   **容器 (Container)**：镜像的运行实例。你可以启动、停止、删除它。容器是独立的、可运行的。
*   **仓库 (Registry)**：存放镜像的地方。最著名的就是官方的 **Docker Hub**，你可以把它想象成存放代码的 GitHub。
*   **Dockerfile**：一个文本文件，用来定义如何构建一个镜像。里面包含了一系列的指令和配置。

---

### 2. 第一步：安装 Docker

根据你的操作系统进行安装。Docker Desktop 是官方推荐的在 Windows 和 macOS 上的安装方式，它包含了 Docker Engine、Docker CLI 客户端和 Docker Compose。

*   **Windows**: [下载 Docker Desktop for Windows](https://docs.docker.com/desktop/install/windows-install/)
    *   *要求：需要 Windows 10/11 64位专业版、企业版或教育版，并开启 WSL 2 功能。*
*   **macOS**: [下载 Docker Desktop for Mac](https://docs.docker.com/desktop/install/mac-install/)
    *   *要求：根据你的芯片选择 Intel 版或 Apple Silicon 版。*
*   **Linux (以 Ubuntu 为例)**: [查看官方安装指南](https://docs.docker.com/engine/install/ubuntu/)
    *   Linux 用户通常直接安装 Docker Engine，而不是 Docker Desktop。

安装完成后，打开你的终端（或 Windows 的 PowerShell/CMD），输入以下命令来验证是否安装成功：

```bash
docker --version
# 如果成功，会显示 Docker 的版本号，如：Docker version 20.10.17, build 100c701

docker info
# 显示更详细的 Docker 系统信息
```

---

### 3. 核心操作：镜像与容器 (动手实践)

#### 3.1 运行你的第一个容器

让我们从 Docker Hub 拉取一个官方的 `hello-world` 镜像，并运行它。

```bash
docker run hello-world
```

这条命令做了什么？
1.  Docker 在本地查找 `hello-world` 镜像。
2.  本地没有找到，于是去 Docker Hub 官方仓库下载 (pull)。
3.  下载完成后，使用这个镜像创建一个新的容器并启动它。
4.  容器运行，打印出 "Hello from Docker!" 等信息，然后退出。

#### 3.2 运行一个有交互的 Nginx 容器

`hello-world` 太简单了，我们来运行一个更有用的东西：一个 Nginx Web 服务器。

```bash
docker run --name my-nginx -d -p 8080:80 nginx
```

让我们分解一下这个命令：
*   `docker run`: 运行容器的命令。
*   `--name my-nginx`: 给这个容器取一个名字叫 `my-nginx`，方便以后操作。如果不指定，Docker 会随机分配一个名字。
*   `-d`: **Detached** 模式，表示在后台运行容器，并返回容器 ID。没有这个参数，你的终端就会被容器的日志占据。
*   `-p 8080:80`: **Port** 端口映射。把**宿主机（你的电脑）的 8080 端口**映射到**容器的 80 端口**。Nginx 默认在容器内监听 80 端口，这样我们就可以通过访问电脑的 8080 端口来访问 Nginx 服务了。
*   `nginx`: 要使用的镜像名称。Docker 会像之前一样，如果本地没有就去 Docker Hub 下载。

现在，打开你的浏览器，访问 `http://localhost:8080`。你会看到 Nginx 的欢迎页面！

#### 3.3 管理容器

你已经有了一个正在后台运行的容器，怎么管理它呢？

*   **查看正在运行的容器**
    ```bash
    docker ps
    ```
    你会看到 `my-nginx` 的信息，包括容器ID、使用的镜像、状态、端口映射等。

*   **查看所有容器（包括已停止的）**
    ```bash
    docker ps -a
    ```

*   **停止容器**
    ```bash
    docker stop my-nginx  # 或者使用容器ID的前几位，如 docker stop a1b2c3d4
    ```
    现在再访问 `http://localhost:8080` 就访问不到了。

*   **启动已停止的容器**
    ```bash
    docker start my-nginx
    ```

*   **重启容器**
    ```bash
    docker restart my-nginx
    ```

*   **查看容器日志**
    ```bash
    docker logs my-nginx
    ```
    这对于调试非常有用。

*   **进入容器内部执行命令 (非常重要！)**
    如果你想看看容器里面是什么样的，或者修改一些配置，可以用 `exec` 命令。
    ```bash
    docker exec -it my-nginx /bin/bash
    ```
    *   `-it`: `-i` (interactive) 和 `-t` (tty) 的组合，表示我们要进行交互式操作。
    *   `my-nginx`: 容器名。
    *   `/bin/bash`: 要在容器内执行的命令，这里是启动一个 bash shell。
    
    执行后，你的终端提示符会变，表示你已经**进入了容器的内部**。你可以像在 Linux 系统里一样使用 `ls`, `cd`, `cat` 等命令。输入 `exit` 可以退回到宿主机。

*   **删除容器**
    **注意：删除前必须先停止容器！**
    ```bash
    docker rm my-nginx
    ```
    如果要强制删除一个正在运行的容器，可以加 `-f` 参数：`docker rm -f my-nginx`。

#### 3.4 管理镜像

*   **查看本地所有镜像**
    ```bash
    docker images
    ```

*   **从 Docker Hub 拉取镜像**
    `docker run` 会在本地没有时自动拉取，但你也可以手动拉取。
    ```bash
h
    docker pull redis
    ```

*   **删除本地镜像**
    **注意：删除前必须先删除所有基于该镜像创建的容器！**
    ```bash
    docker rmi nginx
    ```
    如果一个镜像有多个标签 (tag)，删除时需要指定 `IMAGE ID`。

---

### 4. 构建自己的镜像：Dockerfile

现在，我们要打包自己的应用程序了。假设我们有一个简单的 Python Web 应用。

**项目结构:**

```
my-python-app/
├── app.py
└── Dockerfile
```

**`app.py` 文件内容 (一个简单的 Flask 应用):**

```python
from flask import Flask
import os

app = Flask(__name__)

@app.route('/')
def hello():
    # 从环境变量获取一个名字，如果没有就用 'World'
    name = os.environ.get('NAME', 'World')
    return f"<h1>Hello, {name}!</h1>"

if __name__ == "__main__":
    app.run(host='0.0.0.0', port=5000)
```
*(注意: `host='0.0.0.0'` 很重要，它让容器内的服务可以被外部访问，而不仅仅是 `localhost`)*

**`Dockerfile` 文件内容:**

```dockerfile
# 1. 选择一个基础镜像
# 我们选择一个官方的、包含 Python 3.9 的轻量级镜像
FROM python:3.9-slim

# 2. 设置工作目录
# 在容器内创建一个 /app 目录，并作为后续命令的执行目录
WORKDIR /app

# 3. 复制文件
# 将当前目录下的所有文件复制到容器的 /app 目录中
COPY . .

# 4. 安装依赖
# 在容器内执行命令，安装 Flask
# 在实际项目中，通常是 COPY requirements.txt . 和 RUN pip install -r requirements.txt
RUN pip install Flask

# 5. 暴露端口
# 声明容器在运行时会监听 5000 端口（这只是一个元数据声明，不起实际作用）
EXPOSE 5000

# 6. 设置环境变量
ENV NAME="Docker"

# 7. 定义容器启动时执行的命令
# 当容器启动时，执行 python app.py
CMD ["python", "app.py"]
```

**构建镜像**

在 `my-python-app` 目录下，打开终端，执行以下命令：

```bash
docker build -t my-python-app:1.0 .
```

*   `docker build`: 构建镜像的命令。
*   `-t my-python-app:1.0`: `-t` 表示 **Tag**，给镜像取个名字和标签。格式是 `[镜像名]:[标签]`。
*   `.`: 表示 Dockerfile 所在的上下文路径（Context Path），这里是当前目录。

构建成功后，用 `docker images` 就可以看到你自己的镜像了！

**运行你自己的镜像**

```bash
docker run --name my-app -d -p 5001:5000 my-python-app:1.0
```

*   我们将宿主机的 `5001` 端口映射到容器的 `5000` 端口。
*   现在访问 `http://localhost:5001`，你会看到 **"Hello, Docker!"**。

恭喜你！你已经成功地将自己的应用 Docker化 了！

---

### 5. 数据持久化：数据卷 (Volume)

容器默认是无状态的，一旦容器被删除，容器内产生的所有数据都会丢失。如果我们的应用需要保存数据（比如数据库、用户上传的文件），就需要用到**数据卷 (Volume)**。

数据卷是一个特殊的目录，它可以绕过容器的文件系统，直接将数据存在宿主机上或者由 Docker 管理。

**两种主要方式：**

1.  **绑定挂载 (Bind Mount)**: 将宿主机上的一个**已知路径**的目录挂载到容器内。
    *   **优点**：方便在宿主机上直接查看和修改文件，适合开发时挂载代码目录。
    *   **示例**：启动一个 Nginx 容器，并将宿主机的 `./my-html` 目录挂载到 Nginx 的网站根目录。

    ```bash
    # 在当前目录下创建一个 my-html 文件夹，并在里面放一个 index.html 文件
    mkdir my-html
    echo "<h1>Hello from my host machine!</h1>" > ./my-html/index.html

    # 运行容器，使用 -v 或 --volume 进行挂载
    # 格式：-v /path/on/host:/path/in/container
    docker run --name nginx-with-bind-mount -d -p 8081:80 -v "$(pwd)/my-html":/usr/share/nginx/html nginx
    ```
    现在访问 `http://localhost:8081`，你会看到你自己的 `index.html` 内容。你在宿主机上修改这个文件，刷新浏览器，内容会立即改变！

2.  **命名卷 (Named Volume)**: 由 Docker 自己管理数据卷，我们只需要给它起个名字。
    *   **优点**：不需要关心数据在宿主机的具体位置，由 Docker 统一管理，跨平台性更好，是官方推荐的生产环境用法。
    *   **示例**：创建一个 MySQL 数据库，并将数据存储在命名卷中。

    ```bash
    # 运行 MySQL 容器，创建一个名为 mysql-data 的数据卷，并挂载到 MySQL 的数据目录
    docker run --name my-mysql -d -p 3306:3306 \
      -v mysql-data:/var/lib/mysql \
      -e MYSQL_ROOT_PASSWORD=my-secret-pw \
      mysql:8.0
    ```
    *   `-v mysql-data:/var/lib/mysql`: `mysql-data` 是卷名，Docker 会自动创建并管理它。`/var/lib/mysql` 是 MySQL 在容器内存储数据的路径。
    *   `-e`: 设置环境变量。
    
    现在，即使你 `docker stop my-mysql` 然后 `docker rm my-mysql`，这个名为 `mysql-data` 的数据卷依然存在。下次你再用这个卷启动一个新的 MySQL 容器时，数据还在。
    *   查看所有数据卷：`docker volume ls`
    *   删除数据卷：`docker volume rm mysql-data`

---

### 6. 多容器编排：Docker Compose

当你的应用由多个服务组成时（如 Web 应用 + 数据库 + 缓存），使用 `docker run` 一个个启动和管理会非常麻烦。**Docker Compose** 就是用来解决这个问题的。

它允许你使用一个 `docker-compose.yml` 的 YAML 文件来定义和运行一个多容器应用。

**示例：**
我们来编排前面创建的 Python 应用和一个 Redis 缓存服务。

**项目结构:**

```
my-compose-app/
├── app/
│   ├── app.py
│   └── Dockerfile
└── docker-compose.yml
```

*   把之前 `my-python-app` 文件夹重命名为 `app`，并放入 `my-compose-app` 中。
*   修改 `app/app.py`，让它连接 Redis：

```python
# app/app.py
from flask import Flask
from redis import Redis
import os

app = Flask(__name__)
# 'redis' 是我们在 docker-compose.yml 中定义的服务名
# Docker Compose 会自动处理网络，让 web 服务能通过服务名找到 redis 服务
redis = Redis(host='redis', port=6379)

@app.route('/')
def hello():
    count = redis.incr('hits')
    return f"<h1>Hello Docker!</h1><p>This page has been viewed {count} times.</p>"

if __name__ == "__main__":
    app.run(host='0.0.0.0', port=5000)

```
*   修改 `app/Dockerfile`，增加 `redis` 依赖：
    ```dockerfile
    # ... 其他内容不变 ...
    RUN pip install Flask redis
    # ... 其他内容不变 ...
    ```

*   **`docker-compose.yml` 文件内容：**

```yaml
version: '3.8' # 定义文件版本

services: # 定义一系列服务
  web: # 服务名，可以自定义
    build: ./app # 指定 Dockerfile 的位置，Compose 会自动构建它
    ports:
      - "8000:5000" # 映射端口：宿主机:容器
    volumes:
      - ./app:/app # 挂载代码目录，方便开发时热更新
    
  redis: # 另一个服务，名为 redis
    image: "redis:alpine" # 直接使用 Docker Hub 上的官方镜像
```

**使用 Docker Compose**

在 `my-compose-app` 目录下，打开终端：

*   **启动所有服务**
    ```bash
    docker-compose up
    ```
    或者在后台启动：
    ```bash
    docker-compose up -d
    ```

*   **停止并删除所有服务、网络和卷**
    ```bash
    docker-compose down
    ```

*   **查看服务状态**
    ```bash
    docker-compose ps
    ```

*   **查看日志**
    ```bash
    docker-compose logs web # 查看特定服务的日志
    docker-compose logs -f  # 实时查看所有日志
    ```

现在，访问 `http://localhost:8000`，每次刷新页面，计数器都会增加。这证明你的 Python 应用成功连接到了 Redis 容器！

---

### 7. 常用命令速查表

| 分类       | 命令                                | 描述                                       |
| :--------- | :---------------------------------- | :----------------------------------------- |
| **镜像管理** | `docker images`                     | 列出本地所有镜像                           |
|            | `docker pull [image]`               | 从仓库拉取镜像                             |
|            | `docker build -t [name:tag] .`      | 使用当前目录的 Dockerfile 构建镜像         |
|            | `docker rmi [image]`                | 删除一个或多个镜像                         |
|            | `docker image prune`                | 删除所有悬空镜像 (dangling images)         |
| **容器管理** | `docker run [options] [image]`      | 创建并启动一个容器                         |
|            | `docker ps`                         | 列出正在运行的容器                         |
|            | `docker ps -a`                      | 列出所有容器（包括已停止的）               |
|            | `docker start [container]`          | 启动一个已停止的容器                       |
|            | `docker stop [container]`           | 停止一个正在运行的容器                     |
|            | `docker restart [container]`        | 重启容器                                   |
|            | `docker rm [container]`             | 删除一个或多个容器                         |
|            | `docker logs [container]`           | 查看容器的日志                             |
|            | `docker exec -it [container] cmd`   | 在运行的容器内执行命令                     |
|            | `docker container prune`            | 删除所有已停止的容器                       |
| **数据卷**   | `docker volume ls`                  | 列出所有数据卷                             |
|            | `docker volume create [name]`       | 创建一个数据卷                             |
|            | `docker volume inspect [name]`      | 查看数据卷的详细信息                       |
|            | `docker volume rm [name]`           | 删除一个或多个数据卷                       |
|            | `docker volume prune`               | 删除所有未使用的数据卷                     |
| **Compose**  | `docker-compose up -d`              | 在后台创建并启动所有服务                   |
|            | `docker-compose down`               | 停止并删除容器、网络、卷和镜像           |
|            | `docker-compose ps`                 | 列出 Compose 项目中的容器                  |
|            | `docker-compose logs -f [service]`  | 实时查看服务日志                           |
|            | `docker-compose build`              | 构建或重新构建服务                         |
|            | `docker-compose exec [service] cmd` | 在指定服务容器内执行命令                   |
| **系统清理** | `docker system prune -a --volumes`  | **(危险)** 删除所有未使用的镜像、容器、网络和卷 |

希望这份详细的教程能帮助你顺利踏上 Docker 的学习之旅！从这里开始，你可以继续探索更高级的主题，如 Docker 网络、私有仓库、Docker Swarm 或 Kubernetes。祝你玩得开心！