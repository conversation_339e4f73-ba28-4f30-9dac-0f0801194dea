# 数据库系统工程师考试复习指南

## 第一部分：信息系统知识

### 1. 计算机系统知识

#### 1.1 硬件知识

##### 1.1.1 计算机体系结构和主要部件的基本工作原理

**CPU和存储器的组成、性能、基本工作原理**

- **CPU（中央处理器）**
  - 组成：控制单元（CU）、算术逻辑单元（ALU）、寄存器组
  - 性能指标：主频、核心数、缓存大小、指令集
  - 工作原理：取指令→译码→执行→访存→写回（五级流水线）
  - 生活案例：CPU就像餐厅的主厨，控制单元是主厨的大脑（决定做什么菜），ALU是主厨的双手（实际烹饪），寄存器是主厨面前的调料架（快速存取）

- **存储器**
  - 组成：主存储器（RAM、ROM）、辅助存储器（硬盘、SSD）
  - 性能指标：容量、存取速度、带宽
  - 存储层次：寄存器→缓存→主存→辅存
  - 生活案例：就像图书馆系统，常用书放在手边（寄存器），参考书放在书桌（缓存），其他书在书架（主存），档案在地下室（辅存）

**I/O接口的功能、类型和特点**

- 功能：数据缓冲、格式转换、时序控制、错误检测
- 类型：
  - 并行接口：多位同时传输（如打印机接口）
  - 串行接口：逐位传输（如USB、串口）
  - 通用接口：USB、Thunderbolt
- 生活案例：I/O接口就像机场的登机口，负责乘客（数据）有序上下飞机（进出系统）

**CISC/RISC，流水线操作，多处理机，并行处理**

- **CISC（复杂指令集）vs RISC（精简指令集）**
  - CISC：指令复杂但数量少，像瑞士军刀（功能多但复杂）
  - RISC：指令简单但执行快，像专用工具（功能单一但高效）
  
- **流水线操作**
  - 将指令执行分为多个阶段，同时处理多条指令
  - 生活案例：洗车流水线，一辆车在冲水时，另一辆在打泡沫，第三辆在擦干

- **多处理机与并行处理**
  - SMP（对称多处理）：多个CPU共享内存
  - MPP（大规模并行处理）：每个CPU有独立内存
  - 生活案例：SMP像多个厨师共用一个厨房，MPP像每个厨师有独立厨房

##### 1.1.2 存储系统

**虚拟存储器基本工作原理**

- 原理：将程序分页/分段，按需调入主存
- 地址转换：虚拟地址→物理地址（通过页表）
- 页面置换算法：FIFO、LRU、OPT
- 生活案例：像看电子书，不需要把整本书都下载，只需要加载当前章节

**RAID类型和特性**

- RAID 0：条带化，提高性能但无冗余
- RAID 1：镜像，100%冗余但成本高
- RAID 5：分布式奇偶校验，平衡性能和冗余
- RAID 6：双重奇偶校验，更高容错
- RAID 10：RAID 1+0的组合
- 生活案例：RAID就像备份重要文件，RAID 1像复印一份，RAID 5像把文件分成几部分并加密保存

##### 1.1.3 安全性、可靠性与系统性能评测

- **诊断与容错**
  - 错误检测：奇偶校验、CRC校验
  - 容错技术：冗余设计、故障切换
  - 生活案例：像飞机的双引擎设计，一个故障另一个仍可工作

- **系统可靠性分析评价**
  - MTBF（平均故障间隔时间）
  - MTTR（平均修复时间）
  - 可用性 = MTBF / (MTBF + MTTR)

#### 1.2 数据结构与算法

##### 1.2.1 常用数据结构

**数组**
- 静态数组：大小固定，像教室的座位表
- 动态数组：可伸缩，像可加座的餐桌

**线性表、链表**
- 单向链表：像火车车厢，只能向前
- 双向链表：像地铁，可前可后
- 循环链表：像旋转木马，首尾相连

**栈和队列**
- 栈（LIFO）：像叠盘子，后进先出
- 队列（FIFO）：像排队买票，先进先出

**树结构**
- 二叉树：每个节点最多两个子节点
- 二叉查找树：左小右大，便于查找
- 平衡树（AVL、红黑树）：自动调整保持平衡
- 堆：完全二叉树，父节点大于/小于子节点
- 生活案例：公司组织架构就是树形结构

**图结构**
- 有向图：单行道路网
- 无向图：双向道路网
- 应用：最短路径、网络流

**Hash表**
- 原理：通过散列函数计算存储位置
- 碰撞处理：开放地址法、链地址法
- 生活案例：像图书馆按书名首字母分类存放

##### 1.2.2 常用算法

**排序算法**
- 冒泡排序：相邻比较交换，像水中气泡上浮
- 快速排序：分治法，选基准分区
- 归并排序：分割合并，像扑克牌整理
- 堆排序：利用堆结构特性

**查找算法**
- 顺序查找：逐个比较
- 二分查找：有序数组折半查找
- 哈希查找：直接定位

**算法复杂度**
- 时间复杂度：O(1) < O(log n) < O(n) < O(n log n) < O(n²)
- 空间复杂度：算法执行所需额外空间

#### 1.3 软件知识

##### 1.3.1 操作系统知识

**进程与线程**
- 进程：独立的执行单元，有独立内存空间
- 线程：进程内的执行单元，共享进程资源
- 生活案例：进程像独立的餐厅，线程像餐厅里的服务员

**进程管理**
- 进程状态：新建→就绪→运行→阻塞→终止
- 进程同步：信号量、互斥锁、条件变量
- 死锁条件：互斥、占有等待、不可抢占、循环等待
- 死锁处理：预防、避免、检测、恢复

**存储管理**
- 分页：固定大小块，内部碎片
- 分段：逻辑单元，外部碎片  
- 段页式：结合两者优点
- 虚拟内存：请求分页/分段

**设备管理**
- I/O控制方式：程序查询、中断、DMA
- 假脱机技术：提高设备利用率
- 磁盘调度：FCFS、SSTF、SCAN、C-SCAN

**文件管理**
- 文件组织：顺序、索引、散列
- 目录结构：单级、两级、树形、图形
- 存取控制：读、写、执行权限

##### 1.3.2 程序设计语言

- 编译过程：词法分析→语法分析→语义分析→中间代码→目标代码
- 解释执行：逐行翻译执行
- 程序基本成分：数据、运算、控制、传输

#### 1.4 计算机网络知识

**网络体系结构**
- OSI七层模型：物理层→数据链路层→网络层→传输层→会话层→表示层→应用层
- TCP/IP四层：网络接口层→网络层→传输层→应用层
- 生活案例：像寄快递，每层负责不同工作（打包、贴标签、运输、派送）

**Client/Server与Browser/Server结构**
- C/S：客户端功能强，像专用APP
- B/S：浏览器即可，像网页应用
- B/W/D：浏览器/Web服务器/数据库三层架构

### 2. 数据库技术

#### 2.1 数据库技术基础

##### 2.1.1 数据库模型

**三级模式两级映像**
- 外模式（用户视图）：用户看到的数据
- 概念模式（逻辑视图）：数据的逻辑结构
- 内模式（物理视图）：数据的物理存储
- 外模式/概念模式映像：逻辑独立性
- 概念模式/内模式映像：物理独立性
- 生活案例：像看电影，观众看到画面（外模式），导演设计剧情（概念模式），胶片存储（内模式）

**数据模型**
- 概念模型：E-R图（实体-关系图）
  - 实体：矩形表示
  - 属性：椭圆表示
  - 关系：菱形表示
- 逻辑模型：
  - 关系模型：二维表结构
  - 层次模型：树形结构
  - 网络模型：图形结构

##### 2.1.2 数据库管理系统

**常见DBMS类型**
- RDB（关系数据库）：Oracle、MySQL、SQL Server
- OODB（面向对象数据库）：处理复杂对象
- ORDB（对象关系数据库）：关系+对象特性
- NDB（网状数据库）：复杂关系

##### 2.1.3 数据库系统体系结构

- 集中式：所有处理在一台主机
- Client/Server：客户端与服务器分离
- 分布式：数据分布在多个节点

#### 2.2 数据操作

##### 2.2.1 关系运算

**基本运算**
- 选择（σ）：选取满足条件的行，像筛选考试及格的学生
- 投影（π）：选取特定列，像只看学生的姓名和成绩
- 并（∪）：两个关系的所有元组
- 差（−）：在R中但不在S中的元组
- 笛卡尔积（×）：所有可能的元组组合

**扩展运算**
- 连接（⋈）：
  - 自然连接：按公共属性连接
  - 等值连接：按指定属性相等连接
  - 外连接：保留未匹配元组
- 除（÷）：找出与除数所有元组都有关联的被除数元组

##### 2.2.2 SQL语言

**数据定义（DDL）**
```sql
-- 创建表
CREATE TABLE Students (
    StudentID INT PRIMARY KEY,
    Name VARCHAR(50) NOT NULL,
    Age INT CHECK (Age >= 18),
    DeptID INT,
    FOREIGN KEY (DeptID) REFERENCES Department(DeptID)
);

-- 创建索引
CREATE INDEX idx_name ON Students(Name);

-- 创建视图
CREATE VIEW AdultStudents AS
SELECT * FROM Students WHERE Age >= 18;
```

**数据操作（DML）**
```sql
-- 插入数据
INSERT INTO Students VALUES (1, '张三', 20, 101);

-- 更新数据
UPDATE Students SET Age = 21 WHERE StudentID = 1;

-- 删除数据
DELETE FROM Students WHERE Age < 18;

-- 查询数据
SELECT Name, Age 
FROM Students 
WHERE DeptID = 101
ORDER BY Age DESC;
```

**数据控制（DCL）**
```sql
-- 授权
GRANT SELECT, INSERT ON Students TO user1;

-- 撤销权限
REVOKE INSERT ON Students FROM user1;

-- 创建角色
CREATE ROLE student_admin;
```

#### 2.3 数据库的控制功能

**事务管理（ACID）**
- Atomicity（原子性）：事务要么全做要么全不做
- Consistency（一致性）：事务前后数据保持一致
- Isolation（隔离性）：事务间互不干扰
- Durability（持久性）：提交后永久保存
- 生活案例：ATM转账，要么成功（扣款+到账），要么失败（都不变）

**并发控制**
- 并发问题：
  - 脏读：读到未提交数据
  - 不可重复读：两次读取结果不同
  - 幻读：查询结果集变化
- 解决方法：
  - 封锁协议：共享锁（S锁）、排他锁（X锁）
  - 两段锁协议：增长阶段和缩减阶段
  - 时间戳：按时间顺序处理

**备份与恢复**
- 备份类型：
  - 完全备份：备份所有数据
  - 增量备份：只备份变化部分
  - 差异备份：备份与上次完全备份的差异
- 恢复技术：
  - UNDO：撤销未提交事务
  - REDO：重做已提交事务
  - 检查点：减少恢复工作量

#### 2.4 数据库设计基础理论

##### 2.4.1 关系数据库设计

**函数依赖**
- 完全函数依赖：X→Y，且Y不依赖于X的真子集
- 部分函数依赖：Y依赖于X的真子集
- 传递函数依赖：X→Y→Z

**范式理论**
- 第一范式（1NF）：属性不可分
  - 错误示例：地址="北京市海淀区中关村"
  - 正确示例：省="北京"，市="北京"，区="海淀"，街道="中关村"

- 第二范式（2NF）：消除部分依赖
  - 问题：(学号,课程号)→成绩，但学号→姓名（部分依赖）
  - 解决：分解为学生表和成绩表

- 第三范式（3NF）：消除传递依赖
  - 问题：学号→系号→系主任（传递依赖）
  - 解决：分解为学生表和系表

- BC范式（BCNF）：每个决定因素都是候选键
- 第四范式（4NF）：消除多值依赖
- 第五范式（5NF）：消除连接依赖

##### 2.4.2 对象关系数据库设计

- 嵌套关系：属性可以是关系
- 复杂类型：数组、集合、对象类型
- 继承：子类继承父类属性
- 引用类型：对象间的引用关系

#### 2.5 数据挖掘和数据仓库

**数据仓库**
- 特点：面向主题、集成、稳定、时变
- 组成：数据源、ETL、数据仓库、数据集市
- 模式：星型模式、雪花模式、事实星座模式
- 生活案例：像公司的决策支持系统，汇总各部门数据供分析

**数据挖掘**
- 分类：预测类别（如预测客户流失）
- 聚类：发现相似群体（如客户细分）
- 关联规则：发现项目间关系（如购物篮分析）
- 预测：预测数值（如销售预测）

#### 2.6 多媒体基础知识

**多媒体文件格式**
- 图像：JPEG（有损）、PNG（无损）、GIF（动画）
- 音频：MP3（有损）、WAV（无损）、AAC
- 视频：MP4、AVI、MOV、MKV

**压缩编码技术**
- 无损压缩：ZIP、RAR（可完全还原）
- 有损压缩：JPEG、MP3（牺牲质量换取压缩率）
- 编码标准：H.264、H.265、MPEG-4

#### 2.7 系统性能知识

**性能指标**
- 响应时间：从请求到响应的时间
- 吞吐量：单位时间处理的事务数
- 并发用户数：同时在线用户数
- 资源利用率：CPU、内存、I/O使用率

### 3. 系统开发和运行维护知识

#### 3.1 软件工程基础

**软件开发生命周期**
1. 需求分析：确定做什么
2. 系统设计：确定怎么做
3. 编码实现：实际开发
4. 测试验证：确保质量
5. 部署上线：交付使用
6. 运维支持：持续维护

**软件开发方法**
- 瀑布模型：线性顺序，适合需求明确项目
- 原型法：快速原型，逐步完善
- 螺旋模型：风险驱动，适合大型项目
- 敏捷开发：快速迭代，适应变化

**项目管理**
- 时间管理：甘特图、关键路径法
- 成本管理：预算控制、成本效益分析
- 质量管理：质量保证、质量控制
- 风险管理：识别、评估、应对、监控

#### 3.2 系统分析基础

**结构化分析方法**
- 数据流图（DFD）：
  - 外部实体：方框
  - 处理过程：圆圈
  - 数据存储：开口矩形
  - 数据流：箭头
- 数据字典：定义所有数据元素
- 实体关系图（ERD）：描述数据关系

**统一建模语言（UML）**
- 用例图：描述系统功能
- 类图：描述静态结构
- 序列图：描述动态交互
- 状态图：描述状态转换
- 活动图：描述业务流程

#### 3.3 系统设计知识

**结构化设计**
- 模块化设计：高内聚低耦合
- 自顶向下：逐层细化
- 系统流程图：描述系统处理流程
- HIPO图：层次化输入处理输出

**详细设计**
- 代码设计：命名规范、编码标准
- 界面设计：用户友好、操作便捷
- 数据库设计：表结构、索引、约束
- 算法设计：效率优化、可读性

#### 3.4 系统实施知识

**程序设计**
- 结构化程序设计：顺序、选择、循环
- 面向对象程序设计：封装、继承、多态
- 可视化程序设计：拖拽式开发

**系统测试**
- 单元测试：测试单个模块
- 集成测试：测试模块间接口
- 系统测试：测试整体功能
- 验收测试：用户最终确认

**测试方法**
- 黑盒测试：不关心内部结构，只看输入输出
  - 等价类划分
  - 边界值分析
  - 决策表测试
- 白盒测试：基于代码结构
  - 语句覆盖
  - 分支覆盖
  - 路径覆盖
- 灰盒测试：结合两者特点

#### 3.5 系统运行和维护

**系统运行管理**
- 日常监控：性能、错误、安全
- 故障处理：快速定位、及时恢复
- 变更管理：评估影响、控制风险

**系统维护类型**
- 纠错性维护：修复缺陷
- 适应性维护：适应环境变化
- 完善性维护：增强功能
- 预防性维护：提前预防问题

### 4. 安全性知识

**安全威胁**
- 网络安全：黑客攻击、病毒、木马
- 系统安全：漏洞利用、权限提升
- 数据安全：泄露、篡改、丢失

**安全措施**
- 访问控制：身份认证、权限管理
- 加密技术：
  - 对称加密：DES、AES（加解密用同一密钥）
  - 非对称加密：RSA（公钥加密、私钥解密）
  - 哈希算法：MD5、SHA（单向加密）
- 防火墙：包过滤、应用代理
- 入侵检测：异常检测、特征匹配

**风险管理**
- 风险识别：威胁建模、漏洞扫描
- 风险评估：可能性×影响程度
- 风险应对：规避、转移、减轻、接受
- 内部控制：职责分离、审计跟踪

### 5. 标准化知识

**标准层次**
- 国际标准：ISO、IEC
- 国家标准：GB（国标）
- 行业标准：各行业制定
- 企业标准：企业内部标准

**常见标准**
- ISO 9001：质量管理体系
- ISO 27001：信息安全管理
- CMMI：软件能力成熟度模型
- ITIL：IT服务管理

### 6. 信息化基础知识

**信息化内涵**
- 信息技术的广泛应用
- 信息资源的开发利用
- 信息产业的发展
- 信息化人才培养

**相关法律法规**
- 《网络安全法》
- 《数据安全法》
- 《个人信息保护法》
- 《电子签名法》
- 《著作权法》

### 7. 计算机专业英语

**基本词汇**
- Hardware/Software：硬件/软件
- Database：数据库
- Network：网络
- Security：安全
- Performance：性能
- Backup/Recovery：备份/恢复
- Transaction：事务
- Query：查询
- Index：索引
- Constraint：约束

## 第二部分：数据库系统设计与管理

### 1. 数据库设计

#### 1.1 需求分析

**需求收集方法**
- 用户访谈：直接了解需求
- 问卷调查：大范围收集
- 现场观察：了解实际流程
- 文档分析：研究现有资料

**需求分析内容**
- 功能需求：系统要做什么
- 性能需求：响应时间、并发量
- 安全需求：权限控制、数据保护
- 约束条件：技术限制、成本限制

#### 1.2 概念结构设计

**E-R图设计**
- 实体识别：找出关键业务对象
- 属性确定：实体的特征描述
- 关系定义：实体间的联系
  - 1:1关系：如人与身份证
  - 1:N关系：如部门与员工
  - M:N关系：如学生与课程

**设计原则**
- 完整性：覆盖所有需求
- 一致性：命名规范统一
- 最小冗余：避免重复存储

#### 1.3 逻辑结构设计

**E-R图转换为关系模式**
- 实体转换：每个实体对应一个表
- 1:1关系：可合并或增加外键
- 1:N关系：在N端增加外键
- M:N关系：创建关联表

**关系模式优化**
- 规范化：应用范式理论
- 反规范化：适度冗余提高性能
- 视图设计：简化复杂查询

#### 1.4 物理结构设计

**存储结构设计**
- 表空间规划：数据文件分配
- 分区设计：大表分区存储
- 聚簇设计：相关数据物理相邻

**索引设计**
- 主键索引：自动创建
- 唯一索引：保证唯一性
- 普通索引：提高查询效率
- 复合索引：多列组合
- 全文索引：文本搜索

**性能优化**
- 查询优化：使用执行计划分析
- 索引优化：创建合适索引
- 存储优化：合理分配空间
- 参数调优：调整数据库参数

### 2. 数据库应用系统设计

#### 2.1 系统架构设计

**两层架构（C/S）**
- 优点：响应快、功能强
- 缺点：维护困难、升级麻烦
- 适用：局域网应用

**三层架构（B/S）**
- 表示层：浏览器界面
- 业务逻辑层：应用服务器
- 数据层：数据库服务器
- 优点：维护方便、跨平台
- 适用：互联网应用

**中间件技术**
- 应用服务器：Tomcat、WebLogic
- 消息中间件：RabbitMQ、Kafka
- 数据库中间件：MyCAT、ShardingSphere

#### 2.2 应用程序开发

**数据库连接技术**
- JDBC：Java数据库连接
- ODBC：开放数据库连接
- ADO.NET：.NET数据访问
- 连接池：复用连接，提高性能

**开发模式**
- DAO模式：数据访问对象
- ORM框架：对象关系映射
  - Hibernate
  - MyBatis
  - Entity Framework

**事务处理**
- 编程式事务：手动控制
- 声明式事务：配置管理
- 分布式事务：两阶段提交

### 3. 数据库系统实施

#### 3.1 安装配置

**环境准备**
- 硬件要求：CPU、内存、存储
- 操作系统：Windows、Linux
- 网络配置：IP、端口、防火墙

**数据库安装**
- 安装前检查：依赖、权限
- 安装过程：选择组件、配置参数
- 安装后配置：初始化、安全设置

#### 3.2 数据迁移

**迁移方案**
- 直接迁移：停机迁移
- 并行迁移：新旧系统并行
- 分阶段迁移：逐步迁移

**迁移工具**
- 数据导出/导入：exp/imp、expdp/impdp
- ETL工具：Kettle、DataStage
- 复制工具：GoldenGate、DataGuard

**数据验证**
- 数据完整性：记录数对比
- 数据一致性：关键数据抽检
- 性能验证：响应时间测试

#### 3.3 系统测试

**测试环境**
- 独立测试环境
- 测试数据准备
- 测试工具配置

**测试内容**
- 功能测试：业务功能验证
- 性能测试：
  - 负载测试：正常负载
  - 压力测试：极限负载
  - 并发测试：多用户同时访问
- 安全测试：SQL注入、权限测试

#### 3.4 系统部署

**部署方式**
- 单机部署：适合小型应用
- 集群部署：负载均衡、高可用
- 分布式部署：跨地域部署

**部署步骤**
1. 环境检查
2. 应用部署
3. 配置调整
4. 功能验证
5. 性能调优

### 4. 数据库系统运行和管理

#### 4.1 日常运维

**监控管理**
- 性能监控：CPU、内存、I/O、网络
- 空间监控：表空间、日志空间
- 会话监控：活动会话、锁等待
- 告警设置：阈值告警、异常告警

**日常维护**
- 数据备份：定期备份
- 日志管理：归档清理
- 统计信息：定期更新
- 索引维护：重建优化

#### 4.2 性能优化

**SQL优化**
- 执行计划分析
- 索引使用优化
- 查询重写
- 避免全表扫描

**数据库优化**
- 参数调优：内存、连接数
- 存储优化：数据文件布局
- 分区表：大表分区
- 物化视图：预计算结果

**应用优化**
- 连接池优化
- 批量操作
- 缓存使用
- 异步处理

#### 4.3 故障处理

**常见故障**
- 数据库无法启动
- 连接数超限
- 表空间满
- 死锁问题
- 数据损坏

**故障处理流程**
1. 故障发现：监控告警
2. 故障定位：日志分析
3. 影响评估：业务影响
4. 故障修复：应急处理
5. 根因分析：避免再发

#### 4.4 安全管理

**用户权限管理**
- 最小权限原则
- 角色管理
- 权限审计
- 定期复查

**数据安全**
- 数据加密：传输加密、存储加密
- 访问控制：IP白名单、防火墙
- 审计日志：操作记录
- 数据脱敏：敏感数据处理

**备份恢复**
- 备份策略：
  - 全备份：每周一次
  - 增量备份：每天一次
  - 归档日志：实时备份
- 恢复演练：定期演练
- 异地备份：灾备考虑

### 5. SQL语言深入

#### 5.1 高级查询

**子查询**
```sql
-- 标量子查询
SELECT name, salary 
FROM employees 
WHERE salary > (SELECT AVG(salary) FROM employees);

-- 列子查询
SELECT name 
FROM employees 
WHERE dept_id IN (SELECT id FROM departments WHERE location='Beijing');

-- 行子查询
SELECT * 
FROM employees 
WHERE (dept_id, salary) = (SELECT dept_id, MAX(salary) FROM employees GROUP BY dept_id);
```

**连接查询**
```sql
-- 内连接
SELECT e.name, d.dept_name 
FROM employees e 
INNER JOIN departments d ON e.dept_id = d.id;

-- 左外连接
SELECT e.name, d.dept_name 
FROM employees e 
LEFT JOIN departments d ON e.dept_id = d.id;

-- 自连接
SELECT e1.name AS employee, e2.name AS manager 
FROM employees e1 
LEFT JOIN employees e2 ON e1.manager_id = e2.id;
```

**窗口函数**
```sql
-- 排名函数
SELECT name, salary,
       ROW_NUMBER() OVER (ORDER BY salary DESC) AS row_num,
       RANK() OVER (ORDER BY salary DESC) AS rank,
       DENSE_RANK() OVER (ORDER BY salary DESC) AS dense_rank
FROM employees;

-- 分析函数
SELECT name, salary, dept_id,
       AVG(salary) OVER (PARTITION BY dept_id) AS dept_avg
FROM employees;
```

#### 5.2 存储过程和函数

**存储过程**
```sql
CREATE PROCEDURE update_salary(
    IN emp_id INT,
    IN increase_rate DECIMAL(5,2)
)
BEGIN
    DECLARE current_salary DECIMAL(10,2);
    
    SELECT salary INTO current_salary 
    FROM employees 
    WHERE id = emp_id;
    
    UPDATE employees 
    SET salary = salary * (1 + increase_rate/100)
    WHERE id = emp_id;
    
    SELECT CONCAT('Salary updated from ', current_salary, 
                  ' to ', salary) AS result
    FROM employees 
    WHERE id = emp_id;
END;
```

**函数**
```sql
CREATE FUNCTION calculate_bonus(salary DECIMAL(10,2), performance INT)
RETURNS DECIMAL(10,2)
DETERMINISTIC
BEGIN
    DECLARE bonus DECIMAL(10,2);
    
    CASE performance
        WHEN 5 THEN SET bonus = salary * 0.2;
        WHEN 4 THEN SET bonus = salary * 0.15;
        WHEN 3 THEN SET bonus = salary * 0.1;
        ELSE SET bonus = 0;
    END CASE;
    
    RETURN bonus;
END;
```

#### 5.3 触发器

```sql
CREATE TRIGGER audit_salary_change
AFTER UPDATE ON employees
FOR EACH ROW
BEGIN
    IF OLD.salary != NEW.salary THEN
        INSERT INTO salary_audit_log(
            emp_id, 
            old_salary, 
            new_salary, 
            changed_by, 
            changed_at
        ) VALUES (
            NEW.id,
            OLD.salary,
            NEW.salary,
            USER(),
            NOW()
        );
    END IF;
END;
```

#### 5.4 事务控制

```sql
START TRANSACTION;

-- 转账示例
UPDATE accounts SET balance = balance - 1000 WHERE account_id = 'A001';
UPDATE accounts SET balance = balance + 1000 WHERE account_id = 'B001';

-- 检查余额
SELECT @balance := balance FROM accounts WHERE account_id = 'A001';

IF @balance >= 0 THEN
    COMMIT;
    SELECT 'Transaction completed successfully';
ELSE
    ROLLBACK;
    SELECT 'Transaction rolled back due to insufficient balance';
END IF;
```

### 6. 网络环境下的数据库

#### 6.1 分布式数据库

**基本概念**
- 数据分布在多个物理位置
- 逻辑上是一个整体
- 位置透明性：用户不需要知道数据位置

**数据分布策略**
- 水平分片：按行分割（如按地区）
- 垂直分片：按列分割（如敏感数据分离）
- 混合分片：结合两种方式

**分布式事务**
- 两阶段提交（2PC）：
  1. 准备阶段：协调者询问各节点
  2. 提交阶段：全部同意则提交，否则回滚
- 三阶段提交（3PC）：增加预提交阶段，解决阻塞问题

**CAP理论**
- Consistency（一致性）
- Availability（可用性）
- Partition tolerance（分区容错性）
- 只能同时满足两个

#### 6.2 数据库集群

**主从复制**
- 主库：处理写操作
- 从库：处理读操作
- 异步复制：可能有延迟
- 半同步复制：至少一个从库确认

**读写分离**
- 写操作路由到主库
- 读操作路由到从库
- 负载均衡：分散读压力
- 延迟处理：关键读操作走主库

**高可用方案**
- 主备切换：故障时自动切换
- 双主模式：两个主库互为备份
- 集群模式：多节点协同工作

#### 6.3 Web数据库技术

**连接池技术**
- 预创建连接
- 连接复用
- 连接管理：最大/最小连接数
- 连接验证：定期检查有效性

**缓存技术**
- 查询缓存：缓存查询结果
- 对象缓存：缓存业务对象
- 页面缓存：缓存渲染结果
- 分布式缓存：Redis、Memcached

**Web开发技术**
- JSP/Servlet：Java Web开发
- ASP.NET：.NET Web开发  
- PHP：轻量级Web开发
- Node.js：JavaScript后端开发

### 7. 数据库安全性

#### 7.1 安全威胁

**常见威胁**
- SQL注入：恶意SQL代码
- 权限滥用：越权访问
- 数据泄露：敏感信息暴露
- 拒绝服务：资源耗尽攻击

**SQL注入防护**
```sql
-- 错误方式（易受注入）
String sql = "SELECT * FROM users WHERE name = '" + userName + "'";

-- 正确方式（参数化查询）
String sql = "SELECT * FROM users WHERE name = ?";
PreparedStatement pstmt = conn.prepareStatement(sql);
pstmt.setString(1, userName);
```

#### 7.2 安全措施

**访问控制**
- 身份认证：用户名密码、证书认证
- 授权管理：基于角色的访问控制
- 最小权限：只授予必要权限
- 权限审计：定期审查权限

**数据加密**
- 传输加密：SSL/TLS
- 存储加密：透明数据加密（TDE）
- 字段加密：敏感字段单独加密
- 密钥管理：安全存储密钥

**审计跟踪**
- 登录审计：记录登录尝试
- 操作审计：记录数据变更
- 查询审计：记录敏感查询
- 审计分析：异常行为检测

### 8. 数据库发展趋势与新技术

#### 8.1 NoSQL数据库

**类型和特点**
- 键值存储：Redis、Memcached
  - 简单高效
  - 适合缓存
- 文档数据库：MongoDB、CouchDB
  - 灵活模式
  - 适合JSON数据
- 列族数据库：HBase、Cassandra
  - 高扩展性
  - 适合大数据
- 图数据库：Neo4j、OrientDB
  - 关系复杂
  - 适合社交网络

**应用场景**
- 高并发读写
- 海量数据存储
- 灵活数据模型
- 地理分布式

#### 8.2 大数据技术

**Hadoop生态**
- HDFS：分布式文件系统
- MapReduce：分布式计算
- Hive：数据仓库
- HBase：列族数据库
- Spark：内存计算

**数据处理**
- 批处理：大批量数据处理
- 流处理：实时数据处理
- Lambda架构：批处理+流处理
- 数据湖：原始数据存储

#### 8.3 云数据库

**服务模式**
- IaaS：基础设施即服务
- PaaS：平台即服务
- DBaaS：数据库即服务

**优势**
- 弹性扩展
- 按需付费
- 自动备份
- 高可用保障

**主流产品**
- AWS RDS
- 阿里云RDS
- Azure SQL Database
- Google Cloud SQL

#### 8.4 新兴技术

**内存数据库**
- 全内存存储
- 极高性能
- 持久化策略
- 应用：SAP HANA、Redis

**时序数据库**
- IoT数据存储
- 时间序列优化
- 数据压缩
- 应用：InfluxDB、OpenTSDB

**区块链数据库**
- 去中心化
- 不可篡改
- 共识机制
- 应用：供应链、金融

**AI与数据库**
- 自动调优
- 智能索引
- 异常检测
- 查询优化

## 考试备考建议

### 1. 学习方法

**理论结合实践**
- 概念理解：先理解原理
- 动手实践：实际操作验证
- 案例分析：解决实际问题
- 总结归纳：形成知识体系

**重点难点突破**
- SQL语言：多写多练
- 范式理论：理解并应用
- 事务处理：掌握ACID
- 性能优化：积累经验

### 2. 复习策略

**知识梳理**
- 制作思维导图
- 整理重点笔记
- 归纳易错点
- 对比相似概念

**真题练习**
- 历年真题：熟悉题型
- 模拟考试：控制时间
- 错题分析：查漏补缺
- 重复练习：巩固提高

### 3. 考试技巧

**时间分配**
- 通览全卷：了解难度
- 先易后难：确保得分
- 留出检查时间
- 不留空白

**答题技巧**
- 审题仔细：理解题意
- 答案完整：步骤清晰
- 规范作答：术语准确
- 字迹工整：便于阅卷

### 结语

数据库系统工程师考试涵盖面广，需要扎实的理论基础和丰富的实践经验。通过系统学习本复习指南，结合实际操作练习，定能顺利通过考试。记住：理解原理、掌握方法、勤于实践、善于总结，是学好数据库技术的关键。

祝考试顺利！