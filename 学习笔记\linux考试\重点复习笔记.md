# Linux 考试核心重点复习

## 一、Linux 基础与哲学
- **四大基本思想**：
  1.  一切皆文件（包括硬件）。
  2.  由众多功能单一的小程序组成。
  3.  尽量避免与用户交互（便于自动化）。
  4.  使用纯文本文件进行配置。
- **Linux来源**：源于 Minix，由 Linus Torvalds 开发内核，结合 GNU 项目的软件构成了一个完整的操作系统。
- **特点**：开源、免费、多用户、多任务、良好的稳定性和安全性。

## 二、文件系统与目录结构
- **FHS (文件系统层次化标准)**：
  - `/bin`: 存放所有用户皆可用的基本命令。
  - `/sbin`: 存放只有 root 用户才能使用的管理命令。
  - `/boot`: 系统启动核心文件，如内核 `vmlinuz`。
  - `/dev`: 设备文件存放处，如硬盘 `sda`。
  - `/etc`: 系统主要配置文件存放处。
  - `/home`: 普通用户的家目录。
  - `/root`: 超级用户 root 的家目录。
  - `/lib`, `/lib64`: 系统和程序所需的共享库。
  - `/usr`: 用户安装的应用程序和文件，类似 `C:\Program Files`。
  - `/var`: 存放经常变化的文件，如日志 `/var/log`。
- **文件类型**：
  - `-`: 普通文件
  - `d`: 目录
  - `l`: 符号链接（软链接）

## 三、核心命令

### 1. 目录与文件管理
- `ls`: 列出文件和目录。常用参数 `-l` (详细列表), `-a` (显示所有), `-h` (人性化显示大小)。
- `cd`: 切换目录。`cd ~` (回家目录), `cd ..` (上一级), `cd -` (上次目录)。
- `pwd`: 显示当前工作目录。
- `mkdir`: 创建目录。`-p` (递归创建)。
- `rmdir`: 删除空目录。
- `touch`: 创建空文件或更新文件时间戳。
- `rm`: 删除文件或目录。`-r` (递归删除), `-f` (强制)。 **`rm -rf` 是最危险的命令之一，慎用！**
- `cp`: 复制文件或目录。`-r` (复制目录)。
- `mv`: 移动或重命名文件/目录。

### 2. 文件内容查看与编辑
- `cat`: 查看文件全部内容。
- `more`: 分页查看文件（只能下翻）。
- `less`: 分页查看文件（可上下翻页）。
- `head`: 查看文件开头部分。`-n` (指定行数)。
- `tail`: 查看文件结尾部分。`-n` (指定行数), `-f` (实时追踪文件更新)。
- **`vi/vim` 编辑器**：
  - **三种模式**：命令模式、插入模式、末行模式。
  - **模式切换**：`i/a/o` 进入插入模式，`Esc` 返回命令模式，`:` 进入末行模式。
  - **末行命令**：`:w` (保存), `:q` (退出), `:q!` (强制退出不保存), `:wq` (保存并退出)。

### 3. 权限管理
- **权限位**：`r` (读-4), `w` (写-2), `x` (执行-1)。
- **三组对象**：`u` (用户), `g` (用户组), `o` (其他)。
- `chmod`: 修改文件权限。
  - **数字法**：`chmod 755 file.sh` (rwxr-xr-x)。
  - **符号法**：`chmod u+x,g-w file.sh`。

### 4. 用户与用户组
- `useradd`: 创建用户。
- `passwd`: 修改用户密码。
- `su`: 切换用户。`su - username` (切换并加载环境变量)。

## 四、进程管理
- `ps`: 查看当前进程快照。常用 `ps aux` 或 `ps -ef`。
- `top`: 实时动态查看系统进程。
- `kill`: 终止进程。`kill PID` (温和), `kill -9 PID` (强制)。

## 五、打包与压缩
- `tar`: 打包和解包工具。
  - `-c`: 创建归档。
  - `-x`: 提取归档。
  - `-v`: 显示过程。
  - `-f`: 指定文件名 (**必须放在最后**)。
  - `-z`: 使用 gzip 压缩/解压 (文件为 `.tar.gz`)。
  - **打包压缩**：`tar -czvf archive.tar.gz /path/to/dir`
  - **解压**：`tar -xzvf archive.tar.gz`

## 六、网络管理
- `ifconfig` / `ip addr`: 查看和配置网络接口。
- `ping`: 测试网络连通性。
- `netstat`: 查看网络状态，如端口监听。常用 `-antup`。

## 七、软件安装
- **RPM (RedHat Package Manager)**：
  - `rpm -ivh package.rpm`: 安装软件。
  - `rpm -e package_name`: 卸载软件。
  - `rpm -qa`: 查询所有已安装的包。
