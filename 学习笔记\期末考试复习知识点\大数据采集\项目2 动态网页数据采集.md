# 项目2 动态网页数据采集

## 1.单选题

(1) Urllib库中用于打开网络连接的是（ ）。
A. headers
**B. urlopen**
C. params
D. url

(2) 下列方法中，不属于Urllib库parse模块的是（ ）。
A. urlparse()
B. urljoin()
C. quote()
**D. urlretrieve()**

(3) Requests库是一个使用（ ）编写的HTTP库。
A. C
**B. Python**
C. Java
D. Scala

(4) 下列HTTP请求中，用于向服务器发送获取信息请求的是（ ）。
**A. GET**
B. POST
C. HEAD
D. PATCH

(5) 下列用于设置超时时间的参数是（ ）。
A. auth
B. files
**C. timeout**
D. proxies

（6）关于urlparse()方法的描述，哪一项是不正确的？
A. urlparse()方法用于将接收的URL路径解析成协议、域名、路径、参数、查询条件以及锚点等六个部分。
B. urlparse()方法返回结果包含字段有scheme, netloc, path, params, query, fragment。
**C. urlparse()方法只能处理HTTP协议的URL。**
D. urlparse()方法可以接收三个参数：urlstring, scheme, allow_fragments。

（7）在使用Requests库时，哪个选项正确展示了如何设置会话级别的用户代理（User-Agent）？
A. session.headers['User-Agent'] = 'my-app/0.0.1'
**B. session.headers.update({'User-Agent': 'my-app/0.0.1'})**
C. session.headers.add('User-Agent', 'my-app/0.0.1')
D. session.headers.set('User-Agent', 'my-app/0.0.1')

（8）以下哪个异常类型是继承自URLError且专门用于处理HTTP请求返回的错误状态码？
A. RequestException
B. ConnectionError
**C. HTTPError**
D. Timeout

（9）在Requests库中，如果要发送一个带有JSON格式数据的POST请求，应该使用哪个参数？
A. data
**B. json**
C. params
D. headers

（10）下列关于urlencode()方法的描述，哪一项是正确的？
**A. urlencode()方法用于将字典或由两元素的元组序列转换为“application/x-www-form-urlencoded”编码的字符串。**
B. urlencode()方法只能处理单个键值对。
C. urlencode()方法不会对特殊字符进行编码。
D. urlencode()方法需要手动导入urllib.parse模块才能使用。

（11）在Requests库中，如何设置一个请求的超时时间为5秒？
**A. requests.get(url, timeout=5)**
B. requests.get(url, time_limit=5)
C. requests.get(url).timeout(5)
D. requests.get(url, delay=5)

（12）在使用urllib库时，若要通过POST请求上传文件，以下哪个选项是正确的做法？
A. 使用urllib.request.urlopen()方法，并将文件内容作为data参数传递。
B. 使用urllib.request.Request()对象，并设置files参数为文件路径。
**C. 将文件内容编码为字节序列，并将其作为data参数传递给urllib.request.urlopen()方法。**
D. 使用urllib.parse.urlencode()方法对文件路径进行编码，并将其作为查询参数传递。

（13）在Requests库中，如何通过基本认证方式发送请求？
A. response = requests.get(url, auth=('user', 'password'))
B. response = requests.get(url, basic_auth=('user', 'password'))
**C. from requests.auth import HTTPBasicAuth; session = requests.Session(); session.auth = HTTPBasicAuth('user', 'password')**
D. response = requests.get(url, headers={'Authorization': 'Basic user:password'})

（14）关于Session对象的描述，哪一项是不正确的？
A. 使用Session对象可以在多个请求之间复用TCP连接，提高效率。
B. Session对象可以自动管理Cookie，保持会话状态。
**C. Session对象不能设置代理服务器。**
D. Session对象可以通过session.close()来关闭会话。

（15）下列关于HTTPError异常类型的描述，哪一项是正确的？
A. HTTPError仅适用于HTTP请求，无法处理HTTPS请求。
**B. HTTPError继承自URLError，并且专门处理HTTP响应中的错误状态码。**
C. HTTPError仅在响应状态码为404时触发。
D. HTTPError无法获取到响应头信息。

16、下列关于网络爬虫的描述中，哪一项是正确的（）？
A) 网络爬虫只能抓取静态网页数据
B) 聚焦网络爬虫不需要遵循Robots协议
**C) 通用网络爬虫主要用于搜索引擎的数据采集**
D) 增量式网络爬虫会重复抓取所有网页

17、网络爬虫的工作原理中，以下哪一步骤是最后执行的（）？
A) 获取初始URL
B) 抓取页面并获得新URL
C) 将新URL放入URL队列
**D) 判断是否满足停止条件**

18、以下哪项是网络爬虫的合法行为（）？
**A) 使用User-Agent字段伪装浏览器**
B) 高频访问导致网站瘫痪
C) 直接抓取robots.txt禁止的页面
D) 忽略网站的Robots协议

19、通用网络爬虫和聚焦网络爬虫的主要区别在于（）？
A) 是否使用代理服务器
B) 是否遵循Robots协议
**C) 是否抓取整个互联网还是特定主题数据**
D) 是否使用Python实现

20、以下哪项属于网络爬虫的防爬虫应对策略（）？
A) 提高访问频率
B) 使用固定IP地址
**C) 添加User-Agent字段**
D) 不设置超时时间

21、HTTP协议中，请求方法POST和GET的主要区别是（）？
A) POST比GET安全
B) GET可以传输文件
**C) POST数据长度无限制**
D) GET支持加密传输

22、以下哪个状态码表示“未找到资源”（）？
A) 200
B) 302
**C) 404**
D) 500

23、URL中查询字符串的参数分隔符是（）？
**A) &**
B) ?
C) #
D) :

24、HTTPS协议相比HTTP协议增加的安全特性是（）？
A) 更快的传输速度
**B) 数据加密传输**
C) 更多的请求方法
D) 更长的URL长度

25、以下哪个请求头字段用于标识客户端身份（）？
A) Accept
**B) User-Agent**
C) Content-Type
D) Referer

26、urlopen()方法默认使用的HTTP方法是（）？
**A) GET**
B) POST
C) PUT
D) DELETE

27、Urllib模块中用于解析URL的子模块是（）？
A) request
B) error
**C) parse**
D) response

28、以下哪个方法用于下载文件（）？
A) urlopen()
B) Request()
**C) urlretrieve()**
D) quote()

29、Urllib异常处理中，HTTPError继承自（）？
**A) URLError**
B) ConnectionError
C) TimeoutError
D) RequestException

30、URL编码中文字符应使用的方法是（）？
A) urlencode()
**B) quote()**
C) unquote()
D) urlparse()

31、Requests库默认使用的HTTP方法是（）？
**A) GET**
B) POST
C) PUT
D) HEAD

32、以下哪个方法用于设置会话对象（）？
A) get()
**B) Session()**
C) post()
D) request()

33、Requests中处理代理的参数是（）？
**A) proxies**
B) headers
C) cookies
D) auth

34、以下哪个属性表示响应状态码（）？
A) text
B) content
**C) status_code**
D) encoding

35、Requests处理超时的参数是（）？
**A) timeout**
B) connect
C) read
D) stream

## 2.多选题

1、网络爬虫的应用场景包括（）？
**A) 搜索引擎**
**B) 舆情分析**
**C) 电商平台商品聚合**
**D) 实时交通票务查询**

2、以下哪些属于网络爬虫的分类（）？
**A) 通用网络爬虫**
**B) 聚焦网络爬虫**
**C) 深层网络爬虫**
**D) 分布式网络爬虫**

3、网络爬虫的工作流程包含以下哪些步骤（）？
**A) 获取初始URL**
**B) 过滤无关URL**
C) 重复抓取已下载页面
**D) 存储已抓取URL**

4、Robots协议中的关键选项包括（）？
**A) User-agent**
**B) Disallow**
**C) Sitemap**
**D) Crawl-delay**

5、网络爬虫的实现技术中，Python的优势包括（）？
**A) 语法简洁**
**B) 丰富的第三方库**
C) 高性能多线程处理
D) 自动识别验证码

6、HTTP请求格式包含以下哪些部分（）？
**A) 请求行**
**B) 请求头**
**C) 空行**
**D) 请求体**

7、动态网页与静态网页的区别体现在（）？
**A) 数据加载方式**
**B) 是否依赖数据库**
**C) URL格式**
**D) 页面更新频率**

8、Fiddler工具的主要功能包括（）？
**A) 抓包分析**
**B) HTTPS解密**
**C) 请求重发**
D) 数据库连接

9、HTTP响应头中常见的字段有（）？
**A) Content-Type**
**B) Set-Cookie**
**C) Cache-Control**
D) User-Agent

10、URL的组成元素包括（）？
**A) scheme**
**B) host**
**C) query**
**D) fragment**

11、Urllib库包含的模块有（）？
**A) request**
**B) parse**
**C) error**
D) response

12、urlopen()方法的参数包括（）？
**A) url**
**B) data**
**C) timeout**
D) headers

13、URL解析结果包含的字段有（）？
**A) scheme**
**B) netloc**
C) params
**D) fragment**

14、Urllib处理异常的方式包括（）？
**A) try-except**
**B) try-finally**
**C) try-else**
D) try-catch

15、urlretrieve()方法的优点包括（）？
**A) 支持文件下载**
B) 自动设置超时
**C) 提供下载进度回调**
D) 支持断点续传

16、Requests支持的请求方法包括（）？
**A) GET**
**B) PUT**
**C) OPTIONS**
**D) TRACE**

17、Session对象可以保持的参数包括（）？
**A) Cookie**
**B) headers**
C) timeout
D) verify

18、Requests处理文件上传需要的参数是（）？
**A) files**
B) data
C) json
D) params

19、常见的HTTP状态码包括（）？
**A) 200 OK**
**B) 302 Found**
**C) 403 Forbidden**
**D) 500 Internal Server Error**

20、Requests处理异常的模块是（）？
**A) requests.exceptions**
B) requests.auth
C) requests.cookies
D) requests.structures

## 3.填空题

1、网络爬虫的Robots协议文件名称是**robots.txt**。
2、网络爬虫抓取网页的核心流程是**发起请求→获取响应→解析内容→存储数据**。
3、Python中用于发送HTTP请求的标准库是**urllib**。
4、网络爬虫的增量式更新策略可以减少**重复抓取**。
5、网络爬虫的合法访问需要遵循**Robots**协议。
6、HTTP协议默认使用的端口号是**80**。
7、URL中#后面的部分称为**片段标识符**。
8、Fiddler的工作原理是作为**代理服务器**运行。
9、动态网页数据通常通过**AJAX**技术加载。
10、HTTPS协议使用**SSL/TLS**证书保障传输安全。
11、Urllib中用于发送请求的主模块是**request**。
12、URL编码中空格会被转换为**+**。
13、urlopen()方法返回的对象类型是**http.client.HTTPResponse**。
14、Robots协议文件的默认路径是**/robots.txt**。
15、Urllib的parse模块用于**URL解析**。
16、Requests中发送POST请求的方法是**post()**。
17、会话对象的创建方法是**Session()**。
18、Response对象的**text**属性存储响应内容。
19、Requests默认的超时设置参数是**timeout**。
20、处理JSON响应数据的快捷方法是**json()**。

## 4.判断题

(1) 在不同版本的Python中，Urllib库有两种。（**√**）
(2) 在Urllib库中，parse是一个用于实现HTTP请求模拟的模块，能够完成页面爬取、Cookie设置等工作。（**×**）
(3) 在Urllib库中，为了避免定义异常的error模块，基于Urllib库提供了一个用于解决安全缺陷以及请求延迟等问题而被推出。（**√**）
(4) Requests库是一个使用Python编写的HTTP库，基于Urllib库建立，为解决Urllib库存在的安全缺陷以及请求延迟等问题而被推出。（**×**）
(5) 在使用Requests库时会默认对SSL证书进行检查，当网站没有设置SSL证书时，就会出现证书验证错误。（**√**）
6、网络爬虫可以随意抓取任何网站的数据。（**×**）
7、通用网络爬虫的目标是抓取特定主题的网页。（**×**）
8、Robots协议具有法律强制约束力。（**×**）
9、网络爬虫的工作流程包括解析网页数据。（**√**）
10、Python是实现网络爬虫的唯一语言。（**×**）
11、GET请求的参数只能放在URL中。（**√**）
12、HTTP状态码304表示需要重新加载页面。（**×**）
13、Fiddler可以捕获所有类型的网络请求。（**√**） (注：通常指HTTP/HTTPS，但可扩展)
14、动态网页的内容在页面加载时已经确定。（**×**）
15、Cookie信息存储在服务器端。（**×**） (注：Cookie主要存储在客户端)
16、Urllib是Python内置的HTTP库。（**√**）
17、urlopen()方法可以设置请求头。（**×**） (注：需要通过Request对象设置)
18、urlretrieve()仅能下载图片文件。（**×**）
19、Robots协议必须放在网站根目录。（**√**）
20、Urllib支持自动处理HTTPS证书验证。（**×**） (注：需要额外配置)
21、Requests是Python内置的HTTP库。（**×**）
22、Session对象可以保持Cookie信息。（**√**）
23、Requests不支持文件下载功能。（**×**）
24、get()方法的params参数用于URL编码。（**√**）
25、Requests默认启用SSL证书验证。（**√**）

## 5.简答题

(1) 阐述Urllib库相关模块的作用。
**答案：** **urllib.request：用于打开和读取URL。urllib.parse：用于解析URL，支持URL的构造与拆解，也支持编码和解码。urllib.error：包含了一些由urllib.request引发的异常类，比如URLError和HTTPError。urllib.robotparser：用于解析robots.txt文件，判断哪些内容可以被抓取。**

(2) 列举HTTP请求及其作用。
**答案：** **GET：请求指定的页面信息，并返回实体主体。常用于数据检索操作。POST：向指定资源提交数据进行处理请求（如提交表单或上传文件），数据被包含在请求体中。POST请求可能会导致新的资源的建立或已有资源的修改。PUT：从客户端向服务器传送的数据取代指定文档的内容。DELETE：请求服务器删除指定的页面或资源。HEAD：类似于GET请求，只不过服务端不会返回消息体，仅返回响应头，用于获取元数据而不传输实际内容。PATCH：是对PUT方法的补充，用来对已知资源进行部分修改。不同于PUT替换整个资源表示，PATCH只更新资源的部分属性。**

3、简述网络爬虫的Robots协议的作用及核心规则。
**答案：** **Robots协议是网站管理员与爬虫之间的沟通协议，用于管理内容抓取和索引。核心规则包括User-agent（指定爬虫类型）、Disallow（禁止访问的路径）、Allow（允许访问的路径）和Sitemap（站点地图）。**

4、对比通用网络爬虫和聚焦网络爬虫的工作原理差异。
**答案：** **通用网络爬虫抓取整个互联网数据，而聚焦网络爬虫专注于特定主题数据。通用爬虫遵循广泛规则，聚焦爬虫通过过滤算法选择目标内容。**

5、列举网络爬虫的三种应用场景并简要说明。
**答案：** **搜索引擎：用于索引网页。舆情分析：监控社交媒体数据。电商平台商品聚合：抓取商品信息。**

6、解释网络爬虫的防爬虫策略中“降低访问频率”的作用。
**答案：** **降低访问频率可以避免触发网站的反爬虫机制（如IP封禁），确保爬虫合法运行。**

7、简述Python开发网络爬虫的流程步骤。
**答案：** **Python开发网络爬虫的流程包括：发送HTTP请求→解析HTML→提取数据→存储数据→循环抓取。**

8、解释HTTP请求中的Content-Type字段的作用。
**答案：** **Content-Type字段标识请求体的数据类型（如application/json）。**

9、简述动态网页数据采集的技术难点。
**答案：** **技术难点包括定位AJAX请求、解析动态渲染内容、处理加密数据。**

10、说明Fiddler工具在调试网络请求中的主要用途。
**答案：** **Fiddler用于抓包分析、调试HTTP请求、解密HTTPS流量。**

11、对比JSON和XML数据格式的优缺点。
**答案：** **JSON更轻量、易读；XML支持复杂结构但冗余。**

12、描述浏览器加载网页的完整过程。
**答案：** **浏览器发送HTTP请求→解析HTML→加载资源→执行JavaScript→渲染页面。**

13、说明urlopen()和Request()的协同工作原理。
**答案：** **urlopen()发送请求，Request()设置请求头和参数。** (更准确：Request对象封装请求信息，urlopen使用此对象发送请求)

14、描述Urllib处理重定向的机制。
**答案：** **Urllib自动处理301/302重定向。**

15、列举Urllib模块的三个主要功能。
**答案：** **发送HTTP请求、解析URL、处理异常。**

16、解释urlparse()方法的使用场景。
**答案：** **用于解析URL的组成部分（如scheme、netloc）。**

17、说明Urllib异常处理中URLError和HTTPError的区别。
**答案：** **URLError处理网络连接错误，HTTPError处理HTTP状态码错误。**

18、说明Requests与Urllib的主要区别。
**答案：** **Requests更简洁，支持Session对象；Urllib更底层，功能复杂。**

19、描述Session对象在处理登录会话中的作用。
**答案：** **Session对象可保持Cookie和headers，用于登录状态管理。**

20、列举Requests库的三个核心功能。
**答案：** **发送HTTP请求、处理响应、文件上传。**

21、解释requests.get()和requests.post()的参数差异。
**答案：** **get()的params参数自动编码URL，post()的data参数发送表单。**

22、说明Requests处理文件上传的实现方式。
**答案：** **使用files参数上传文件，如`requests.post(url, files={'file': open('file.txt', 'rb')})`。**

## 6.综合题

1、某电商网站限制同一IP每分钟最多访问10次，现需设计一个爬虫方案：
（1）如何通过代理服务器解决IP被封问题？
**答案：** **使用代理IP池轮换IP。**
（2）如何设置请求头避免被检测？
**答案：** **设置随机User-Agent和Referer头。**
（3）如何设计访问频率控制机制？
**答案：** **通过时间延迟或随机间隔控制请求频率。**

2、假设需要采集豆瓣电影TOP250榜单数据，需考虑：
（1）分析Robots协议是否允许抓取该页面
**答案：** **检查豆瓣电影页面的robots.txt文件是否允许抓取。**
（2）设计使用Urllib库的代码框架
**答案：** **使用Urllib库的代码框架：发送GET请求→解析HTML→提取数据。**
（3）应对可能出现的验证码干扰
**答案：** **通过图像识别或第三方服务绕过验证码。**

3、某新闻网站采用动态加载技术，需完成：
（1）比较Urllib和Requests库处理动态网页的能力差异
**答案：** **Urllib无法处理动态网页，需依赖后端接口；Requests支持更复杂的请求。** (更准确：两者本身都不执行JS，Requests更易于处理AJAX请求)
（2）设计使用Selenium的解决方案
**答案：** **使用Selenium模拟浏览器操作。**
（3）说明为何需要设置显式等待
**答案：** **显式等待确保元素加载完成，避免操作未加载元素。**

4、针对反爬虫策略的网站，需制定：
（1）User-Agent字段的随机化方案
**答案：** **使用随机User-Agent库（如fake_useragent）。**
（2）代理IP池的构建方法
**答案：** **构建代理IP池并轮换使用。**
（3）请求频率的随机延迟算法
**答案：** **通过random模块生成随机延迟。**

5、某政府网站要求登录后才能查看数据，需实现：
（1）模拟登录的请求流程
**答案：** **发送登录POST请求并携带Cookie。**
（2）Cookie的持久化存储方案
**答案：** **使用requests.Session()持久化存储Cookie。**
（3）异常处理机制设计
**答案：** **(根据实际情况添加，如捕获登录失败异常、网络异常等)**

6、某网站采用AJAX加载数据，需设计采集方案：
（1）如何定位AJAX请求的URL
**答案：** **通过浏览器开发者工具定位AJAX请求URL。**
（2）如何处理JSON响应数据
**答案：** **解析JSON响应并提取数据。**
（3）如何应对接口的token验证
**答案：** **模拟token生成逻辑或使用第三方库。** (或从页面中提取)

7、使用Fiddler分析某网站的登录请求，需完成：
（1）捕获POST请求的参数
**答案：** **捕获登录请求的参数（如username、password）。**
（2）模拟登录的代码实现
**答案：** **使用requests.Session()发送POST请求并保存Cookie。**
（3）处理登录后的Session保持
**答案：** **后续请求携带Cookie保持登录状态。**

8、针对HTTPS网站的数据采集，需解决：
（1）配置Fiddler的证书信任
**答案：** **安装Fiddler证书并信任。**
（2）处理SSL/TLS加密问题
**答案：** **使用requests库配置verify参数。** (或确保系统信任相关证书)
（3）分析响应中的加密数据
**答案：** **解析加密数据（如Base64或AES解密）。** (如果内容本身加密的话)

9、某网站限制非浏览器访问，需设计：
（1）添加哪些请求头字段
**答案：** **添加User-Agent、Accept等字段。**
（2）如何模拟浏览器行为
**答案：** **模拟浏览器行为（如点击、滚动）。** (更全面：还包括请求头、JS执行等)
（3）使用Selenium的必要性
**答案：** **Selenium可处理JavaScript渲染内容。**

10、分析某电商平台的价格爬取需求：
（1）处理JavaScript渲染的页面
**答案：** **使用Selenium处理JavaScript渲染页面。**
（2）应对反爬虫的IP封禁策略
**答案：** **轮换代理IP并设置请求间隔。**
（3）设计数据存储方案
**答案：** **将数据存储到数据库或CSV文件。**

11、设计一个使用Urllib爬取豆瓣电影数据的方案：
（1）分析robots.txt文件
**答案：** **检查豆瓣robots.txt文件。**
（2）编写获取页面代码
**答案：** **使用urlopen()获取页面并解析HTML。**
（3）处理页面编码问题
**答案：** **处理编码问题（如UTF-8）。**

12、针对需要登录的网站，需实现：
（1）构造POST请求的data参数
**答案：** **构造POST请求的data参数（如用户名和密码）。**
（2）保存和发送Cookie
**答案：** **使用opener.addheaders保存Cookie。** (或Requests的Session)
（3）处理登录后的跳转
**答案：** **处理登录后的跳转URL。**

13、某网站限制IP访问频率，需设计：
（1）代理IP池的实现
**答案：** **维护代理IP池并轮换使用。**
（2）请求间隔随机化
**答案：** **随机生成请求间隔时间。**
（3）异常重试机制
**答案：** **设置重试次数和异常处理。**

14、分析Urllib采集知乎数据的挑战：
（1）JavaScript渲染内容
**答案：** **JavaScript渲染内容需使用Selenium。**
（2）验证码识别
**答案：** **验证码需图像识别或第三方服务。**
（3）反爬虫策略应对
**答案：** **反爬虫需模拟浏览器行为。**

15、设计一个文件下载器：
（1）使用urlretrieve()实现
**答案：** **使用urlretrieve()下载文件。**
（2）添加下载进度显示
**答案：** **通过回调函数显示下载进度。**
（3）处理大文件分块下载
**答案：** **分块下载大文件。** (urlretrieve本身可以处理大文件，但手动分块更灵活)

16、设计一个采集微博热搜榜的方案：
（1）分析页面请求接口
**答案：** **分析微博热搜接口（如https://weibo.com/hot/search）。**
（2）处理JSON响应数据
**答案：** **解析JSON响应并提取热搜数据。**
（3）实现数据持久化存储
**答案：** **存储到数据库或文件。**

17、针对需要登录的网站，需实现：
（1）使用Session保持登录状态
**答案：** **使用Session对象发送登录请求并保存Cookie。**
（2）处理CSRF令牌验证
**答案：** **处理CSRF令牌（如XSRF-TOKEN）。**
（3）设计异常重试策略
**答案：** **设置重试次数和异常处理。**

18、某网站限制每秒请求次数，需设计：
（1）使用代理IP池
**答案：** **使用代理IP池轮换IP。**
（2）实现请求队列
**答案：** **使用队列管理请求。**
（3）设置随机请求间隔
**答案：** **随机延迟请求时间。**

19、分析Requests采集淘宝商品数据的挑战：
（1）处理JavaScript渲染
**答案：** **JavaScript渲染需使用Selenium或Pyppeteer。**
（2）应对IP封禁
**答案：** **IP封禁需轮换代理IP。**
（3）解析加密参数
**答案：** **解析加密参数（如时间戳或MD5）。**

20、设计一个文件下载器：
（1）使用stream参数
**答案：** **使用stream参数下载大文件。** (Requests库)
（2）添加断点续传
**答案：** **实现断点续传（通过Range头）。**
（3）实现多线程下载
**答案：** **多线程下载文件分片。**