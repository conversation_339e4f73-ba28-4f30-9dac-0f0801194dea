# 项目6 动态网页数据预处理

## 1.单选题
(1) 以下不属于read_csv()方法包含参数的是（ ）。
**A. io**
B. names
C. sep
D. header

(2) Pandas库中用于查看基本信息的方法是（ ）。
**A. DataFrame.info()**
B. DataFrame.unique()
C. DataFrame.head()
D. DataFrame.tail()

(3) Logstash过滤器中用于匹配日志中的IP地址的匹配模式是（ ）。
**A. IPORHOST**
B. IP
C. HOST
D. IPV4

(4) Pig内置函数中用于去除字符串头尾空格的函数是（ ）。
A. LTRIM()
B. RTRIM()
**C. TRIM()**
D. UPPER()

(5) Logstash中表示标准输入插件的是（ ）。
A. File
B. Kafka
C. Input
**D. Stdin**

6、下列关于Pandas数据结构的描述中，哪一项是正确的（）？
A) Series只能存储整数类型的数据
B) DataFrame的列索引必须唯一
C) Panel是二维数据结构
**D) DataFrame的列可以包含不同的数据类型**

7、Pandas中用于读取CSV文件的方法是（）？
A) read_excel()
B) read_sql()
**C) read_csv()**
D) read_json()

8、ELK技术栈中，负责日志数据采集的组件是（）？
A) Elasticsearch
**B) Logstash**
C) Kibana
D) Beats

9、下列关于缺失值处理的描述中，哪一项是正确的（）？
A) df.isna()用于填充缺失值
B) df.dropna()默认保留所有非空行
**C) df.fillna()可以指定填充方法**
D) 缺失值无法通过Pandas处理

10、Logstash的输入插件支持以下哪种数据源（）？
A) 仅支持文件输入
B) 不支持网络数据源
**C) 支持Kafka、文件、数据库等多种来源**
D) 仅支持数据库输入

## 2.多选题
1、下列关于Pandas数据结构的描述中，正确的有（）？
**A) Series是一维数组**
**B) DataFrame支持行索引和列索引**
**C) Panel是三维数据结构**
D) DataFrame的列类型必须一致

2、Pandas中处理缺失值的方法包括（）？
**A) dropna()**
**B) fillna()**
**C) isnull()**
**D) replace()**

3、ELK技术栈的核心组件包括（）？
**A) Elasticsearch**
**B) Logstash**
**C) Kibana**
D) Hadoop

4、Logstash过滤插件的功能包括（）？
**A) 数据解析**
**B) 数据转换**
C) 数据加密
D) 数据去重

5、Kibana的主要功能包括（）？
**A) 数据可视化**
**B) 日志分析**
C) 数据存储
D) 数据采集

## 3.填空题
1、ELK技术栈中，**Elasticsearch**用于实时搜索和分析海量数据。
2、Pandas中检查数据维度的方法是**shape()**。
3、Logstash的输出插件可以将数据写入**Elasticsearch、数据库、文件**。
4、在ELK中，**Kibana**用于创建可视化仪表板。
5、Pandas中保存数据到Excel文件的方法是**to_excel()**。

## 4.判断题
(1) Pandas库是Python的核心闭源数据分析支持库。（**×**）
(2) Pandas库中read_csv()方法默认使用“，”作为列分隔符。（**√**）
(3) Pig中加载函数PigStorage()表示将非结构化数据加载到Pig中。（**×**）
(4) Logstash中正则匹配模式IPORHOST表示匹配IPv4地址。（**×**）
(5) Logstash日志匹配模式中HTTPD_COMBINEDLOG表示匹配过滤httpd日志。（**√**）
6、Series中的数据类型必须一致。（**√**）
7、Logstash不支持从Kafka采集数据。（**×**）
8、Elasticsearch默认端口是9200。（**√**）
9、df.isnull()会返回一个布尔值。（**√**）
10、Kibana只能查看数据，不能进行交互操作。（**×**）

## 5.简答题
1、简述ELK各个工具的作用。
答案：
- **ELK栈由Elasticsearch、Logstash和Kibana三个开源工具组成，它们共同构成了一个强大的日志管理和分析平台：**
- **Elasticsearch: 是一个基于Lucene的搜索引擎，专门设计用于快速搜索和分析大量的数据。它允许你存储、搜索和分析大量数据，并且以接近实时的速度返回结果。Elasticsearch擅长处理文本搜索、复杂查询以及聚合操作，非常适合用来构建复杂的搜索功能和数据分析应用。**
- **Logstash: 是一个服务器端的数据处理管道，能够同时从多个来源采集数据，转换数据，然后将其发送到所选定的“存储库”中，比如Elasticsearch。Logstash不仅限于日志数据，还可以处理各种类型的事件数据。它通过丰富的输入、过滤和输出插件，使得数据处理变得非常灵活。**
- **Kibana: 是一个开源的数据可视化工具，专门为Elasticsearch设计，可以让Elasticsearch中的数据进行可视化展示。通过Kibana，用户可以创建各种图表、仪表板和地图来直观地探索和分析数据。它是理解大数据背后的故事的强大工具，特别适合监控系统状态、分析业务趋势等场景。**
- **综上所述，ELK栈结合了数据收集(Logstash)、存储与搜索(Elasticsearch)、以及可视化(Kibana)，为企业提供了一站式的解决方案，用于处理和理解海量的日志及事件数据。**

2、简述Pandas中DataFrame的特点。
答案：**DataFrame是表格型数据结构，支持行列索引，列类型可异构，适合处理结构化数据**。

3、ELK技术栈中三个组件的作用分别是什么？
答案：**Elasticsearch用于存储和搜索数据，Logstash用于采集和处理数据，Kibana用于可视化**。

4、如何使用Pandas处理缺失值？
答案：**使用dropna()删除缺失值，fillna()填充缺失值，isnull()检测缺失值。**

5、Logstash的过滤插件有哪些常见用途？
答案：**Logstash过滤插件用于数据解析（如Grok）、转换（如字段重命名）、格式化（如JSON转CSV）。**

6、简述Kibana在数据分析中的主要功能。
答案：**Kibana支持数据可视化（图表、地图）、日志分析（过滤、聚合）和仪表板创建。**

## 6.综合题
1、某企业需要分析其官网的访问日志，要求：使用ELK技术栈完成日志采集、处理和可视化。请设计完整的解决方案，包括数据采集方式、处理流程及可视化展示方案。
答案：
-  **使用Filebeat采集日志；**
 - **Logstash解析日志并过滤无效数据；**
 - **Elasticsearch存储数据；**
 - **Kibana创建仪表板展示访问趋势和异常。**

2、某电商平台需要对商品评论数据进行清洗和分析，要求：使用Pandas完成数据预处理（包括缺失值处理、重复值删除、数据格式转换）。请写出具体实现步骤及代码示例。
答案：
1.  **读取数据：pd.read_csv()；**
2.  **处理缺失值：fillna()；**
3. **删除重复值：drop_duplicates()；**
4.  **数据类型转换：astype()。**

3、对比分析Pandas和ELK在数据预处理中的优缺点，并说明适用场景。
答案：**Pandas适合小规模数据的灵活处理，ELK适合实时日志分析。Pandas用于结构化数据清洗，ELK用于分布式日志收集。**

4、假设某系统日志包含IP地址字段，要求：使用Logstash的GeoIP插件解析IP地址，并在Kibana中展示地域分布。请描述实现过程。
答案：
 1. **Logstash配置GeoIP插件解析IP；**
 2. **Kibana地图可视化显示地域分布；**
 3. **使用Elasticsearch存储解析后的地理信息。**

5、某机构需要监控服务器性能指标（CPU、内存、磁盘IO），要求：使用ELK技术栈完成数据采集、存储和告警功能。请设计实现方案。
答案：
1. **Beats采集性能指标；**
2. **Logstash过滤和聚合数据；**
 3. **Elasticsearch存储并设置告警规则；**
 4. **Kibana监控指标趋势。**