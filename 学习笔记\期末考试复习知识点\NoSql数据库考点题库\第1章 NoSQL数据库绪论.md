# 第1章 NoSQL数据库绪论

## 一、单选题

1、下列关于关系型数据库管理系统的描述中，哪一项是正确的（）？
A) 关系型数据库不支持事务处理
**B) 关系型数据库采用二维表结构存储数据**
C) 关系型数据库无法进行分布式部署
D) 关系型数据库不需要遵循ACID特性

2、NoSQL数据库的核心特征是（）？
A) 强一致性
B) 严格遵循关系模型
**C) 弱化事务机制**
D) 依赖完整性约束

3、下列选项中，属于大数据4V特征的是（）？
A) 高安全性
**B) 大容量（Volume）**
C) 高一致性
D) 高成本

4、云计算的三种服务模式是（）？
**A) IaaS, PaaS, SaaS**
B) DAS, NAS, SAN
C) SQL, NoSQL, NewSQL
D) 容器化, 微服务, 虚拟化

5、NoSQL数据库主要应用于哪些领域？
A) 仅限于互联网行业
**B) 互联网、电商、金融、医疗等多个行业**
C) 只能用于金融行业
D) 以上都不对

6、关系型数据库的事务特性ACID中的C代表什么？
A) 可用性
**B) 一致性**
C) 隔离性
D) 持久性

7、下列哪个选项不是大数据的4V特征之一？
A) 大容量（Volume）
B) 多样化（Variety）
C) 高速度（Velocity）
**D) 高精度（Precision）**

8、下列哪项是NoSQL数据库的特点？
A) 强调事务处理能力
**B) 弱化模式或表结构**
C) 不支持分布式部署
D) 仅支持结构化数据

9、云计算的基本特征不包括以下哪一项？
A) 按需自助服务
B) 广泛的网络访问
**C) 固定的资源配置**
D) 快速和弹性

10、在推荐系统及用户画像等日志分析应用场景下，通常不需要严格的______。
A) 数据加密
**B) 事务特性和一致性保障**
C) 数据存储能力
D) 数据查询效率

11、下列哪项不属于云计算的服务模式？
A) IaaS（基础设施即服务）
B) PaaS（平台即服务）
C) SaaS（软件即服务）
**D) HaaS（硬件即服务）**

12、下列哪种类型的NoSQL数据库最适合用于物联网设备产生的时序性数据存储？
A) 文档数据库
B) 键值对数据库
**C) 时间序列数据库**
D) 图形数据库

13、关于大数据场景下的数据采集方式，以下哪种描述正确？
A) 在线采集不能实时获取数据
B) 离线采集更适合进行实时数据分析
**C) 在线采集可以实时获取并处理数据**
D) 离线采集无法定期上传数据

14、NoSQL数据库与传统关系型数据库相比，在以下哪个方面可能表现得更为出色？
A) 支持复杂的SQL查询
**B) 对非结构化数据的支持**
C) 更强的数据一致性和事务支持
D) 更高的维护成本

## 二、多选题

1、关系型数据库的瓶颈包括（）？
**A) 难以横向扩展**
**B) 强一致性导致性能下降**
**C) 无法处理非结构化数据**
D) 支持高并发读写

2、NoSQL数据库的典型应用场景包括（）？
**A) 存储非结构化数据**
**B) 高吞吐量缓存**
**C) 时序数据管理**
D) 严格事务处理

3、大数据采集的方式包括（）？
**A) 在线采集**
**B) 离线采集**
C) 数据加密
D) 数据压缩

4、云计算的部署模式包括（）？
**A) 私有云**
**B) 社区云**
**C) 公有云**
**D) 混合云**

## 三、填空题

1、云计算的服务模式包括IaaS、PaaS和**SaaS**。
2、NoSQL数据库的四大分类是键值存储、文档存储、列存储和**图数据库**。
3、大数据的4V特征包括大容量（Volume）、多样化（Variety）、有价值（Value）和**高速率（Velocity）**。
4、容器化技术通过**容器镜像**实现轻量级虚拟化。

## 四、判断题（正确打√，错误打×）

1、关系型数据库可以轻松应对海量数据的横向扩展需求。（**×**）
2、NoSQL数据库完全取代了关系型数据库。（**×**）
3、大数据的采集需要考虑数据隐私和安全问题。（**√**）
4、容器化技术比虚拟机技术更节省资源。（**√**）

## 五、简答题

1、简述关系型数据库在处理海量数据时遇到的主要瓶颈。
**答案：** **关系型数据库在海量数据场景下，纵向扩展成本高，横向扩展难以解决分布式一致性、分区容错性等问题，且事务机制和完整性约束导致性能下降。**

2、简要说明NoSQL数据库的“CAP定理”及其在实际应用中的取舍。
**答案：** **CAP定理指出一致性（Consistency）、可用性（Availability）、分区容错性（Partition Tolerance）不可兼得。NoSQL通常优先保证AP或CP，根据业务需求权衡。**

3、大数据场景中，为什么需要NoSQL数据库？
**答案：** **大数据场景中，NoSQL的分布式特性、灵活数据模型和高吞吐能力更适合处理非结构化数据、高并发访问和弹性扩展需求。**

4、简述云计算对NoSQL数据库部署和维护的影响。
**答案：** **云计算提供按需资源分配和自动化运维能力，降低了NoSQL的部署复杂性，提升了可靠性和扩展性。**

## 六、综合题

1、结合实际应用场景，分析NoSQL数据库的诞生背景及其发展过程。
**答案：** **NoSQL诞生于互联网业务对传统数据库的性能和扩展性需求。其发展过程包括：从开源社区起步，逐步完善分布式架构，结合NewSQL融合ACID特性，最终成为大数据时代的重要数据管理工具。**

2、设计一个基于NoSQL的物联网系统解决方案，说明其技术选型和架构设计。
**答案：** **物联网系统可采用时间序列数据库（如InfluxDB）存储传感器数据，用Redis缓存实时状态，通过Kubernetes容器化部署，结合云平台实现弹性扩展。**

3、如何利用云计算和容器化技术优化NoSQL数据库的部署和运维？
**答案：** **利用Docker打包NoSQL组件，通过Kubernetes实现自动扩缩容、故障恢复和负载均衡，结合云平台的监控和日志服务优化运维效率。**

4、以电商行业为例，说明大数据采集、存储和分析的全流程，并讨论NoSQL数据库在其中的作用。
**答案：** **电商大数据流程包括：通过日志采集用户行为数据（如Apache Flume），用HBase存储商品信息，用Elasticsearch支持搜索，用Spark进行实时分析。NoSQL在存储和查询环节提供高效支持。**