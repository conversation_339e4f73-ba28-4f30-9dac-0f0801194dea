# 项目4 基于Scrapy实现动态网页数据采集与存储

## 1.单选题
(1) 一个简单的Scrapy框架包含（ ）个主要模块。
A. 1
B. 3
**C. 5**
D. 7

(2) 下列命令中，不属于全局命令的是（ ）。
A. fetch
B. startproject
C. genspider
**D. crawl**

(3) 以下不属于Scrapy框架中常用的Spider通用参数的是（ ）。
**A. HTMLFeedSpider**
B. scrapy.Spider
C. CrawlSpider
D. XMLFeedSpider

(4) Scrapy框架提供了（ ）种选择器。
A. 1
**B. 2**
C. 3
D. 4

(5) 在Scrapy框架中运行Scrapy项目时，可以使用crawl命令的（ ）参数，将数据保存到本地JSON、CSV等文件中。
**A. -o**
B. -p
C. -s
D. -f

6、Scrapy框架中哪个组件负责下载网页内容？（）
A) Item Pipeline
B) Spider
**C) Downloader**
D) Scheduler

7、下列关于Scrapy的描述，哪一项是正确的？（）
A) Scrapy只能用于静态网页的数据采集。
B) Scrapy不支持分布式爬虫。
**C) Scrapy可以使用XPath和CSS选择器解析网页。**
D) Scrapy无法处理JavaScript动态渲染的页面。

8、在Scrapy项目中，用来定义实体数据的基类是什么？（）
A) scrapy.Spider
B) scrapy.Field
**C) scrapy.Item**
D) scrapy.Request

9、Scrapy框架中的Downloader Middleware位于哪两个组件之间？（）
A) Engine和Scheduler
**B) Downloader和Engine**
C) Spider和Item Pipeline
D) Spider和Downloader

10、要运行一个已创建的Scrapy爬虫，应使用以下哪个命令？（）
A) scrapy fetch
B) scrapy runspider
**C) scrapy crawl**
D) scrapy shell

## 2.多选题
1、Scrapy框架的优点有哪些？（）
**A) 支持并发功能**
**B) 具有统一的中间件**
**C) 采用可读性很强的XPath技术解析网页**
D) 只能通过文本文件存储数据

2、以下哪些是Scrapy框架的核心组件？（）
**A) Item Pipeline**
**B) Spider**
**C) Scheduler**
**D) Selector**

3、Scrapy项目结构包含哪些重要文件或目录？（）
**A) items.py**
**B) settings.py**
**C) pipelines.py**
**D) spiders/**

4、以下哪些操作可以通过Scrapy Shell完成？（）
**A) 发送新的请求并查看响应**
**B) 使用本机浏览器打开给定的response对象**
**C) 查看响应的请求头**
**D) 打印帮助列表**

5、Scrapy框架中如何设置是否遵循robots.txt规则？（）
**A) ROBOTSTXT_OBEY=True**
B) DOWNLOAD_DELAY=3
C) ITEM_PIPELINES={}
D) USER_AGENT='Mozilla/5.0'

## 3.填空题
1、Scrapy框架的下载器称为**Downloader**。
2、Scrapy项目中自定义字段需要修改**items.py**文件。
3、在Scrapy框架中，通过**Requests和Responses**实现循环抓取网页数据。
4、Scrapy框架中用于存储爬取到的数据的部分被称为**Item Pipeline**。
5、Scrapy框架中使用**CONCURRENT_REQUESTS**来控制并发请求的数量。

## 4.判断题
(1) 在Scrapy框架中，主要通过Twisted同步网络架构的应用实现网络通信的处理工作。（**×**）
(2) 通过命令的方式，只能将数据以固定的格式存储至指定文件；而通过管道的方式，可以在管道中对数据进行处理后，再进行存储。（**√**）
(3) Scrapy框架是Python语言的一个第三方应用程序框架，主要用于爬取网站并从页面中提取结构化数据。（**√**）
(4) SPIDER_MODULES参数用于表示使用genspider命令创建新spider的模块。（**×**）
(5) XPath能够通过路径表达式从XML、HTML等结构化文件中进行节点或节点集的选取。（**√**）
6、Scrapy框架自身无法实现分布式爬虫。（**√**）
7、Scrapy框架可以使用pip安装，并且会自动安装相关依赖库。（**√**）
8、Scrapy框架中的Downloader负责接收Requests请求。（**×**）
9、Scrapy框架能够通过Shell工具方便地调试程序。（**√**）
10、Scrapy框架中的Item Pipeline主要用于下载网页内容。（**×**）

## 5.简答题
(1) 简述Scrapy框架中的中间件。
答案：
- **中间件在Scrapy中扮演着至关重要的角色，它们位于Scrapy的各个组件之间，用于处理请求、响应和item。Scrapy中有两类主要的中间件：Downloader Middleware和Spider Middleware。**
- **Downloader Middleware：位于Scrapy的引擎和下载器之间，允许你执行自定义代码来处理发送给下载器的请求和从下载器接收的响应。例如，你可以设置代理、用户代理，或者处理重定向等。**
- **Spider Middleware：位于Scrapy的引擎和Spider之间，允许你处理输入的响应和输出的item及新的请求。这对于清洗数据、过滤不需要的响应或添加额外的信息到item中非常有用。**

(2) 简述Scrapy项目的构建流程。
答案：
- **构建一个Scrapy项目的基本步骤如下：**
- **创建Scrapy项目：首先使用scrapy startproject [project_name]命令创建一个新的Scrapy项目。这会生成一个基础的项目结构。**
- **定义Spider：在项目内使用scrapy genspider [spider_name] [domain]命令创建新的Spider，或者手动在spiders目录下添加新的Spider脚本。Spider定义了如何抓取某个网站或一组网站的数据。**
- **配置项目：根据需求调整settings.py文件中的设置，如User-Agent、并发请求数量、延迟时间等。**
- **编写Item Pipelines：如果需要对抓取的数据进行清理、验证和持久化处理，则需要编写Item Pipeline。每个Pipeline是一个独立的Python类，实现了对item的一系列处理步骤。**
- **测试Spider：使用scrapy crawl [spider_name]命令运行Spider，并观察其行为是否符合预期。**
- **部署与扩展：一旦Spider开发完成且功能正常，可以通过Scrapy Cloud或其他方式部署你的爬虫，并考虑增加更多功能如使用中间件、扩展、自定义配置等。**

3、简述Scrapy框架的主要组成部分及其功能。
答案：
Scrapy框架主要包括以下组件：
- **Scrapy Engine：负责整个系统的数据流处理。**
- **Scheduler：调度器，负责接收请求并将其交给下载器。**
- **Downloader：下载器，负责从网络中获取网页内容。**
- **Spiders：爬虫，用于解析网页并提取所需数据。**
- **Item Pipeline：管道，用于处理爬虫提取的数据。**

4、解释Scrapy框架中的Spider组件的作用。
答案：
**Spider组件是Scrapy的核心，它定义了如何抓取特定网站的数据。包括如何发起初始请求，如何解析响应以提取数据以及是否需要跟进新的链接。

## 6.综合题
1、结合实际应用场景，设计一个利用Scrapy框架进行数据采集的方案，并说明其工作流程。
答案：
**在设计一个利用Scrapy框架进行数据采集的方案时，首先需要确定目标网站及其结构。然后创建Scrapy项目，定义爬虫规则，使用XPath或CSS选择器来解析页面，并通过Item Pipeline处理和存储数据。工作流程通常包括：启动爬虫 -> 发送请求 -> 接收响应 -> 解析数据 -> 存储数据。
