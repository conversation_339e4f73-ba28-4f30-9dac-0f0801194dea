# Hadoop大数据项目实践手册

## 📋 项目概述

### 🎯 项目目标
掌握完整的大数据项目开发流程：
- **环境搭建** → **数据导入** → **MapReduce预处理** 
- **Hive数仓建设** → **HQL分析** → **Spark/SparkSQL挖掘** 
- **数据可视化** → **成果汇报**

### 📊 数据集说明
本项目基于高速公路车流量数据，包含以下数据集：
- `vehicle_type.csv` - 车辆类型字典
- `vehicle_trails.csv` - 车辆行驶轨迹（核心数据）
- `toll.csv` - 收费站信息
- `service.csv` - 服务区信息  
- `section.csv` - 路段信息
- `gantry.csv` - 门架信息

---

## 🔧 第一部分：环境搭建

### 1.1 虚拟机集群配置

#### 📋 集群架构
```
Master节点：NameNode, ResourceManager, SecondaryNameNode, JobHistoryServer
Slave节点：DataNode, NodeManager
- slave1：DataNode, NodeManager
- slave2：DataNode, NodeManager, Metastore, HiveServer2
- slave3：DataNode, NodeManager
```

#### 🔧 网络配置
1. **域名解析配置**
   ```bash
   # 编辑各节点 /etc/hosts
   vim /etc/hosts
   
   # 添加集群节点映射
   ************* master
   ************* slave1
   ************* slave2
   ************* slave3
   ```

2. **SSH免密登录**
   ```bash
   # 生成密钥对
   ssh-keygen -t rsa
   
   # 复制公钥到各节点
   ssh-copy-id root@slave1
   ssh-copy-id root@slave2
   ssh-copy-id root@slave3
   ```

### 1.2 Hadoop集群搭建

#### 📦 软件版本要求
- Linux：CentOS 7+
- JDK：1.8+
- Hadoop：3.x
- Maven：3.6+

#### ⚙️ 环境变量配置
```bash
# 编辑 ~/.bashrc
export JAVA_HOME=/usr/local/jdk1.8.0_271
export HADOOP_HOME=/usr/local/hadoop-3.2.1
export PATH=$PATH:$JAVA_HOME/bin:$HADOOP_HOME/bin:$HADOOP_HOME/sbin
export HADOOP_CONF_DIR=$HADOOP_HOME/etc/hadoop
```

#### 🚀 集群启动命令
```bash
# 启动集群
start-all.sh
start-historyserver.sh

# 停止集群
stop-all.sh

# 监控页面
# NameNode: http://master:9870
# ResourceManager: http://master:8088
# JobHistory: http://master:19888
```

### 1.3 Hive环境配置

#### 🗄️ MySQL安装配置
```bash
# 在master节点安装MySQL
yum install mysql-server
systemctl start mysqld
systemctl enable mysqld

# 创建Hive元数据库
mysql -u root -p
CREATE DATABASE hive_metastore;
CREATE USER 'hive'@'%' IDENTIFIED BY 'hive123';
GRANT ALL ON hive_metastore.* TO 'hive'@'%';
FLUSH PRIVILEGES;
```

#### 🐝 Hive服务配置
```bash
# 环境变量
export HIVE_HOME=/usr/local/hive-3.1.2
export PATH=$PATH:$HIVE_HOME/bin

# 启动Metastore (在slave2)
nohup hive --service metastore &

# 启动HiveServer2 (在slave2)
nohup hive --service hiveserver2 &

# 连接测试
beeline -u ************************* -n root
```

### 1.4 Spark集群配置

#### ⚡ Spark环境变量
```bash
export SPARK_HOME=/usr/local/spark-3.1.1
export PATH=$PATH:$SPARK_HOME/bin:$SPARK_HOME/sbin
```

#### 🚀 Spark服务启动
```bash
# 启动Spark集群
start-all.sh

# 启动History Server
start-history-server.sh

# 启动Thrift Server
start-thriftserver.sh

# 监控页面：http://master:8080
# Thrift Server连接：*************************
```

### 1.5 开发环境配置

#### 🛠️ IDEA项目配置
1. **创建Maven项目**
2. **导入Hadoop JAR包**（`$HADOOP_HOME/share/hadoop/`）
3. **配置日志依赖**
4. **复制集群配置文件**到`resources`目录

#### 📦 Maven依赖示例
```xml
<dependency>
    <groupId>org.apache.hadoop</groupId>
    <artifactId>hadoop-client</artifactId>
    <version>3.2.1</version>
</dependency>
```

---

## 📊 第二部分：项目实践任务

### 任务1：HDFS数据导入

#### 🎯 学习目标
- 掌握HDFS基本命令操作
- 理解HDFS分布式存储架构
- 实现数据集的批量导入

#### 📋 实施步骤

**步骤1：准备本地数据**
```bash
# 创建本地数据目录
mkdir -p /root/highway_data

# 上传CSV文件到此目录
ls -lrt /root/highway_data/
```

**步骤2：创建HDFS目录**
```bash
# 创建HDFS目录结构
hdfs dfs -mkdir -p /user/hadoop/highway_data
```

**步骤3：批量上传数据**
```bash
# 上传各个CSV文件
hdfs dfs -put /root/highway_data/vehicle_type.csv /user/hadoop/highway_data/
hdfs dfs -put /root/highway_data/vehicle_trails.csv /user/hadoop/highway_data/
hdfs dfs -put /root/highway_data/toll.csv /user/hadoop/highway_data/
hdfs dfs -put /root/highway_data/service.csv /user/hadoop/highway_data/
hdfs dfs -put /root/highway_data/section.csv /user/hadoop/highway_data/
hdfs dfs -put /root/highway_data/gantry.csv /user/hadoop/highway_data/
```

**步骤4：验证数据完整性**
```bash
# 查看文件列表
hdfs dfs -ls /user/hadoop/highway_data/

# 检查文件内容
hdfs dfs -tail /user/hadoop/highway_data/gantry.csv
```

---

### 任务2：MapReduce无效车牌统计

#### 🎯 学习目标
- 掌握MapReduce编程模型
- 学会使用正则表达式进行数据清洗
- 实现分布式数据统计

#### 📋 技术架构
```
Input: vehicle_trails.csv
↓
Mapper: 提取车牌号 → 正则校验 → 输出无效车牌
↓
Reducer: 统计无效车牌出现次数
↓
Output: <无效车牌, 统计次数>
```

#### 💻 代码实现

**Mapper类**
```java
public class InvalidVehiclePlateMapper extends Mapper<LongWritable, Text, Text, IntWritable> {
    private static final String VALID_PLATE_REGEX = "^(WJ)?[京津沪...]\\w{5,6}$";
    
    @Override
    protected void map(LongWritable key, Text value, Context context) {
        String[] fields = value.toString().split(",");
        if (fields.length > 2) {
            String vlp = fields[2].trim(); // 车牌号字段
            
            if (!vlp.matches(VALID_PLATE_REGEX) || vlp.isEmpty()) {
                context.write(new Text(vlp), new IntWritable(1));
            }
        }
    }
}
```

**Reducer类**
```java
public class InvalidVehiclePlateReducer extends Reducer<Text, IntWritable, Text, IntWritable> {
    @Override
    protected void reduce(Text key, Iterable<IntWritable> values, Context context) {
        int count = 0;
        for (IntWritable value : values) {
            count += value.get();
        }
        context.write(key, new IntWritable(count));
    }
}
```

**Driver类**
```java
public class InvalidVehiclePlateDriver extends Configured implements Tool {
    @Override
    public int run(String[] args) throws Exception {
        Job job = Job.getInstance(getConf(), "invalid-vehicle-plate-count");
        job.setJarByClass(InvalidVehiclePlateDriver.class);
        
        job.setMapperClass(InvalidVehiclePlateMapper.class);
        job.setReducerClass(InvalidVehiclePlateReducer.class);
        
        job.setOutputKeyClass(Text.class);
        job.setOutputValueClass(IntWritable.class);
        
        FileInputFormat.addInputPath(job, new Path(args[0]));
        FileOutputFormat.setOutputPath(job, new Path(args[1]));
        
        return job.waitForCompletion(true) ? 0 : 1;
    }
}
```

---

### 任务3：Hive数据仓库建设

#### 🎯 学习目标
- 掌握Hive数据仓库设计
- 学会创建外部表和管理表
- 实现批量数据加载

#### 📋 数据仓库分层设计
```
ODS层（原始数据层）
├── ods_gsgl_vehicle_trails (外部表)
├── ods_gsgl_vehicle_type
├── ods_gsgl_toll
├── ods_gsgl_service
├── ods_gsgl_section
└── ods_gsgl_gantry
```

#### 📊 建表脚本

**创建数据库**
```sql
CREATE DATABASE IF NOT EXISTS highway;
USE highway;
```

**创建车辆轨迹表（外部表）**
```sql
CREATE EXTERNAL TABLE ods_gsgl_vehicle_trails (
    record_id STRING COMMENT '记录ID',
    gantry_code STRING COMMENT '门架编码',
    vlp STRING COMMENT '车牌号',
    capture_time STRING COMMENT '抓拍时间',
    direction STRING COMMENT '行驶方向',
    crossing_type STRING COMMENT '通行类型',
    identify_vtype STRING COMMENT '识别车型'
) COMMENT '车辆轨迹原始数据表'
ROW FORMAT DELIMITED 
FIELDS TERMINATED BY ','
STORED AS TEXTFILE
LOCATION '/user/hadoop/highway_data/ods_gsgl_vehicle_trails';
```

**创建其他维度表**
```sql
-- 路段信息表
CREATE TABLE ods_gsgl_section (
    section_code STRING COMMENT '路段编码',
    section_name STRING COMMENT '路段名称',
    road_code STRING COMMENT '道路编码',
    road_name STRING COMMENT '道路名称'
) COMMENT '路段信息表'
ROW FORMAT DELIMITED 
FIELDS TERMINATED BY ','
STORED AS TEXTFILE;

-- 车辆类型表
CREATE TABLE ods_gsgl_vehicle_type (
    vtype_code STRING COMMENT '车型编码',
    vtype_name STRING COMMENT '车型名称',
    vtype_desc STRING COMMENT '车型描述'
) COMMENT '车辆类型字典表'
ROW FORMAT DELIMITED 
FIELDS TERMINATED BY ','
STORED AS TEXTFILE;
```

#### 📥 数据加载
```sql
-- 加载数据到管理表
LOAD DATA INPATH '/user/hadoop/highway_data/section.csv' 
INTO TABLE ods_gsgl_section;

LOAD DATA INPATH '/user/hadoop/highway_data/vehicle_type.csv' 
INTO TABLE ods_gsgl_vehicle_type;

LOAD DATA INPATH '/user/hadoop/highway_data/toll.csv' 
INTO TABLE ods_gsgl_toll;

-- 验证数据加载
SELECT COUNT(*) FROM ods_gsgl_vehicle_trails;
SELECT * FROM ods_gsgl_section LIMIT 10;
```

---

### 任务4：HiveQL车流量统计分析

#### 🎯 学习目标
- 掌握复杂HiveQL查询编写
- 学会使用窗口函数进行数据去重
- 实现多维度车流量统计

#### 📋 数据处理流程
```
ODS层 → 数据清洗 → STD层 → 业务聚合 → DM层
```

#### 🧹 数据清洗（STD层）

**清洗车辆轨迹数据**
```sql
-- 创建标准化轨迹表
CREATE TABLE std_gsgl_vehicle_trails AS
SELECT 
    record_id,
    gantry_code,
    TRIM(REPLACE(vlp, '"', '')) as vlp,
    from_unixtime(unix_timestamp(capture_time, 'yyyy/MM/dd HH:mm:ss'), 'yyyy-MM-dd HH:mm:ss') as capture_time,
    CASE 
        WHEN direction = '1' THEN '上行'
        WHEN direction = '2' THEN '下行'
        ELSE direction 
    END as direction,
    CASE 
        WHEN crossing_type = '1' THEN '正常通行'
        WHEN crossing_type = '2' THEN '倒车'
        ELSE crossing_type 
    END as crossing_type,
    identify_vtype
FROM (
    SELECT *,
        LAG(capture_time, 1, capture_time) OVER (
            PARTITION BY TRIM(REPLACE(vlp, '"', '')), gantry_code, direction 
            ORDER BY capture_time
        ) as prev_time
    FROM ods_gsgl_vehicle_trails
    WHERE TRIM(REPLACE(vlp, '"', '')) REGEXP '^(WJ)?[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼][A-Z0-9]{5,6}$'
      AND TRIM(REPLACE(vlp, '"', '')) IS NOT NULL
      AND TRIM(REPLACE(vlp, '"', '')) != ''
) t
WHERE unix_timestamp(capture_time, 'yyyy-MM-dd HH:mm:ss') - unix_timestamp(prev_time, 'yyyy-MM-dd HH:mm:ss') > 60
   OR prev_time = capture_time;
```

**清洗维度表**
```sql
-- 标准化门架表
CREATE TABLE std_gsgl_gantry AS
SELECT 
    gantry_code,
    TRIM(gantry_name) as gantry_name,
    section_code,
    CASE 
        WHEN pile_no REGEXP '^K[0-9]+\\+[0-9]+$' THEN
            CAST(SUBSTRING(pile_no, 2, LOCATE('+', pile_no) - 2) AS DOUBLE) + 
            CAST(SUBSTRING(pile_no, LOCATE('+', pile_no) + 1) AS DOUBLE) / 1000
        ELSE NULL 
    END as pile_no_km,
    latitude,
    longitude
FROM ods_gsgl_gantry
WHERE gantry_code IS NOT NULL;
```

#### 📊 车流量统计（DM层）

**路线级车流量统计**
```sql
-- 路线总流量统计
CREATE TABLE dm_gsgl_road_total_flow AS
SELECT 
    s.road_code,
    s.road_name,
    COUNT(DISTINCT t.vlp) as total_vehicles,
    COUNT(*) as total_records
FROM std_gsgl_vehicle_trails t
JOIN std_gsgl_gantry g ON t.gantry_code = g.gantry_code
JOIN std_gsgl_section s ON g.section_code = s.section_code
GROUP BY s.road_code, s.road_name;
```

**路线级小时车流量趋势**
```sql
-- 路线小时流量统计
CREATE TABLE dm_gsgl_road_flow_hour AS
SELECT 
    s.road_code,
    s.road_name,
    HOUR(t.capture_time) as traffic_hour,
    COUNT(DISTINCT t.vlp) as hourly_vehicles,
    COUNT(*) as hourly_records
FROM std_gsgl_vehicle_trails t
JOIN std_gsgl_gantry g ON t.gantry_code = g.gantry_code
JOIN std_gsgl_section s ON g.section_code = s.section_code
GROUP BY s.road_code, s.road_name, HOUR(t.capture_time)
ORDER BY s.road_code, traffic_hour;
```

**路段级车流量统计**
```sql
-- 路段总流量统计
CREATE TABLE dm_gsgl_section_total_flow AS
SELECT 
    s.section_code,
    s.section_name,
    s.road_name,
    COUNT(DISTINCT t.vlp) as total_vehicles,
    COUNT(*) as total_records
FROM std_gsgl_vehicle_trails t
JOIN std_gsgl_gantry g ON t.gantry_code = g.gantry_code
JOIN std_gsgl_section s ON g.section_code = s.section_code
GROUP BY s.section_code, s.section_name, s.road_name;
```

---

### 任务5：HiveQL拥堵分析

#### 🎯 学习目标
- 掌握复杂窗口函数应用
- 学会计算车辆行驶速度
- 实现拥堵指数计算

#### 📊 拥堵分析指标
- **平均速度**：相邻门架间车辆行驶速度
- **拥堵指数TI**：`TI = (Vmax-Vavg)/Vmax × 100 + Qactual/Qcapacity × 100`
- **高峰时段**：按TI排序识别拥堵时段
- **拥堵热点**：门架级拥堵排名

#### 🚗 速度计算

**车辆轨迹排序**
```sql
-- 为每辆车的轨迹点排序
CREATE TABLE dm_gsgl_ranked_trails AS
SELECT 
    t.*,
    g.pile_no_km,
    ROW_NUMBER() OVER (
        PARTITION BY t.vlp, t.direction 
        ORDER BY t.capture_time
    ) as rn
FROM std_gsgl_vehicle_trails t
JOIN std_gsgl_gantry g ON t.gantry_code = g.gantry_code
WHERE g.pile_no_km IS NOT NULL;
```

**计算相邻门架间速度**
```sql
-- 计算车辆在相邻门架间的平均速度
CREATE TABLE dm_gsgl_speed_trails AS
SELECT 
    t1.vlp,
    t1.gantry_code as start_gantry,
    t2.gantry_code as end_gantry,
    t1.capture_time as start_time,
    t2.capture_time as end_time,
    ABS(t2.pile_no_km - t1.pile_no_km) as distance_km,
    (unix_timestamp(t2.capture_time) - unix_timestamp(t1.capture_time)) as time_seconds,
    (ABS(t2.pile_no_km - t1.pile_no_km) / 
     NULLIF((unix_timestamp(t2.capture_time) - unix_timestamp(t1.capture_time)), 0) * 3600
    ) as avg_speed_kmph
FROM dm_gsgl_ranked_trails t1
JOIN dm_gsgl_ranked_trails t2 
  ON t1.vlp = t2.vlp 
  AND t1.direction = t2.direction 
  AND t2.rn = t1.rn + 1
WHERE (unix_timestamp(t2.capture_time) - unix_timestamp(t1.capture_time)) > 0
  AND (ABS(t2.pile_no_km - t1.pile_no_km) / 
       NULLIF((unix_timestamp(t2.capture_time) - unix_timestamp(t1.capture_time)), 0) * 3600
      ) BETWEEN 10 AND 200;
```

#### 📈 拥堵指数计算

**小时级拥堵指数**
```sql
-- 计算每小时路段拥堵指数
CREATE TABLE dm_gsgl_congestion_index AS
SELECT 
    s.road_name,
    s.section_name,
    t.direction,
    HOUR(t.capture_time) as traffic_hour,
    COUNT(DISTINCT t.vlp) as traffic_volume,
    AVG(sp.avg_speed_kmph) as avg_speed,
    -- 拥堵指数计算：速度因子 + 流量因子
    ((120 - COALESCE(AVG(sp.avg_speed_kmph), 60)) / 120 * 100) + 
    (COUNT(DISTINCT t.vlp) / 1500.0 * 100) as congestion_index
FROM std_gsgl_vehicle_trails t
JOIN std_gsgl_gantry g ON t.gantry_code = g.gantry_code
JOIN std_gsgl_section s ON g.section_code = s.section_code
LEFT JOIN dm_gsgl_speed_trails sp ON t.vlp = sp.vlp AND t.gantry_code = sp.start_gantry
GROUP BY s.road_name, s.section_name, t.direction, HOUR(t.capture_time);
```

**拥堵热点识别**
```sql
-- 门架级拥堵热点排名
CREATE TABLE dm_gsgl_gantry_congestion_index AS
SELECT 
    g.gantry_code,
    g.gantry_name,
    s.section_name,
    COUNT(DISTINCT t.vlp) as total_vehicles,
    AVG(sp.avg_speed_kmph) as avg_speed,
    ((120 - COALESCE(AVG(sp.avg_speed_kmph), 60)) / 120 * 100) + 
    (COUNT(DISTINCT t.vlp) / 1500.0 * 100) as congestion_index
FROM std_gsgl_vehicle_trails t
JOIN std_gsgl_gantry g ON t.gantry_code = g.gantry_code
JOIN std_gsgl_section s ON g.section_code = s.section_code
LEFT JOIN dm_gsgl_speed_trails sp ON t.vlp = sp.vlp AND t.gantry_code = sp.start_gantry
GROUP BY g.gantry_code, g.gantry_name, s.section_name
ORDER BY congestion_index DESC
LIMIT 20;
```

---

### 任务6：Spark车型差异分析

#### 🎯 学习目标
- 掌握Spark Core编程
- 学会Scala函数式编程
- 实现分布式数据统计

#### 💻 Scala代码实现

**创建Scala项目**
```scala
package com.highway.scala

import org.apache.spark.{SparkConf, SparkContext}

object VehicleTypeCount {
  def main(args: Array[String]): Unit = {
    // 创建Spark配置和上下文
    val conf = new SparkConf()
      .setAppName("VehicleTypeCount")
      .setMaster("yarn")
    
    val sc = new SparkContext(conf)
    
    // 车型编码映射
    val vehicleTypeMap = Map(
      "1" -> "客车",
      "2" -> "货车", 
      "3" -> "专项作业车",
      "4" -> "其他"
    )
    
    // 读取HDFS数据（注意分隔符是\001）
    val inputPath = "/user/hive/warehouse/highway.db/std_gsgl_vehicle_trails"
    val lines = sc.textFile(inputPath)
    
    // 解析数据并统计车型
    val vehicleTypeCounts = lines
      .map(line => {
        val fields = line.split("\001") // Hive默认分隔符
        if (fields.length >= 7) {
          val vtypeCode = fields(6) // identify_vtype字段
          vehicleTypeMap.getOrElse(vtypeCode, "未知车型")
        } else {
          "数据异常"
        }
      })
      .map(vtype => (vtype, 1))
      .reduceByKey(_ + _)
    
    // 输出结果到控制台
    println("=== 车型统计结果 ===")
    vehicleTypeCounts.collect().foreach {
      case (vtype, count) => println(s"$vtype: $count")
    }
    
    // 保存结果到HDFS
    val outputPath = "/user/hadoop/highway_data/VehicleTypeCount"
    vehicleTypeCounts
      .coalesce(1)
      .saveAsTextFile(outputPath)
    
    sc.stop()
  }
}
```

#### 🔧 执行配置
```bash
# 设置HDFS权限
hdfs dfs -chmod 777 /user/hadoop/highway_data

# 提交Spark作业
spark-submit \
  --class com.highway.scala.VehicleTypeCount \
  --master yarn \
  --deploy-mode client \
  /path/to/spark-highway.jar
```

---

### 任务7：SparkSQL交通状况画像

#### 🎯 学习目标
- 掌握PySpark编程
- 学会PyEcharts可视化
- 构建交通大屏

#### 🐍 Python实现

**PySpark查询脚本**
```python
from pyspark.sql import SparkSession
from pyecharts.charts import Line, Bar, Geo, Page
from pyecharts import options as opts
from pyecharts.globals import ChartType
import pandas as pd

# 创建SparkSession
spark = SparkSession.builder \
    .appName("HighwayTrafficAnalysis") \
    .config("hive.metastore.uris", "thrift://slave2:9083") \
    .enableHiveSupport() \
    .getOrCreate()

# 1. 时间分布分析 - 高峰时段识别
def analyze_hourly_traffic():
    sql = """
    SELECT 
        traffic_hour,
        SUM(traffic_volume) as total_vehicles,
        AVG(avg_speed) as avg_speed,
        AVG(congestion_index) as avg_congestion_index
    FROM highway.dm_gsgl_congestion_index
    GROUP BY traffic_hour
    ORDER BY traffic_hour
    """
    
    df = spark.sql(sql).toPandas()
    
    # 创建时间趋势图
    line = Line()
    line.add_xaxis(df['traffic_hour'].tolist())
    line.add_yaxis("车流量", df['total_vehicles'].tolist())
    line.add_yaxis("平均速度", df['avg_speed'].tolist(), yaxis_index=1)
    line.add_yaxis("拥堵指数", df['avg_congestion_index'].tolist(), yaxis_index=1)
    
    line.set_global_opts(
        title_opts=opts.TitleOpts(title="24小时交通流量趋势"),
        xaxis_opts=opts.AxisOpts(name="小时"),
        yaxis_opts=opts.AxisOpts(name="车流量", position="left"),
        legend_opts=opts.LegendOpts(pos_top="10%")
    )
    
    return line

# 2. 空间分布分析 - 热力图
def analyze_spatial_distribution():
    sql = """
    SELECT 
        s.section_name,
        g.latitude,
        g.longitude,
        SUM(ci.traffic_volume) as total_volume,
        AVG(ci.congestion_index) as avg_congestion
    FROM highway.dm_gsgl_congestion_index ci
    JOIN highway.std_gsgl_gantry g ON ci.section_name = g.section_name
    JOIN highway.std_gsgl_section s ON g.section_code = s.section_code
    WHERE g.latitude IS NOT NULL AND g.longitude IS NOT NULL
    GROUP BY s.section_name, g.latitude, g.longitude
    """
    
    df = spark.sql(sql).toPandas()
    
    # 创建地理热力图
    geo = Geo()
    geo.add_schema(maptype="china")
    
    data_pair = [(row['section_name'], row['avg_congestion']) for _, row in df.iterrows()]
    
    geo.add(
        "拥堵热力图",
        data_pair,
        type_=ChartType.HEATMAP
    )
    
    geo.set_global_opts(
        title_opts=opts.TitleOpts(title="高速公路拥堵热力图"),
        visualmap_opts=opts.VisualMapOpts(
            min_=0, max_=