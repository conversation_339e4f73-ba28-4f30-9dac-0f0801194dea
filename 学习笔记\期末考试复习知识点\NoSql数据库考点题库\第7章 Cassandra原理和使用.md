# 第7章 Cassandra原理和使用

## 一、简答题

1、简述Cassandra的核心特点及其与关系型数据库的区别。
**答案：** **Cassandra采用对等环形结构、无主节点设计，支持高可用性和水平扩展。与关系型数据库相比，其分布式特性更强，但缺乏JOIN和事务支持。**

2、解释Cassandra数据模型中的行键、列族和超级列族的概念。
**答案：** **行键是唯一标识，列族包含标准列，超级列族包含超级列（子列）。数据模型通过行键、列族和超级列族分层组织。**

3、描述Cassandra的机架感知策略及其在集群部署中的作用。
**答案：** **机架感知策略通过配置节点的机架位置，将数据副本分布到不同机架，避免单点故障。**

## 二、综合题

1、设计一个基于Cassandra的电商订单系统，说明其数据模型和核心操作。
**答案：** **数据模型：使用列族存储订单信息，主键为订单ID。核心操作包括INSERT订单、SELECT查询、DELETE过期订单。**

2、结合实际场景，分析Cassandra的CAP理论权衡策略（R+W>N与R+W<=N）。
**答案：** **R+W>N保证强一致性，例如R=N, W=1（读取所有副本，写入一个副本即可），适合金融交易场景；R+W<=N则优先保证可用性，例如R=1, W=1, N=3（读写一个副本即可，共三个副本），适合对一致性要求不高的场景。**

## 三、单选题

1、Cassandra默认的分区器是（）？
A) RandomPartitioner
**B) Murmur3Partitioner**
C) ByteOrderedPartitioner
D) OrderPreservingPartitioner

2、下列关于CQL的描述中，哪一项是正确的（）？
A) CQL支持JOIN查询
B) CQL支持事务机制
C) CQL支持GROUP BY聚合
**D) CQL支持分页查询（PAGING）**

3、Cassandra的预写日志（CommitLog）存储路径是（）？
A) /var/lib/cassandra/data
**B) /var/lib/cassandra/commitlog**
C) /var/lib/cassandra/hints
D) /var/lib/cassandra/saved_caches

## 四、多选题

1、Cassandra支持的副本复制策略包括（）？
**A) SimpleStrategy**
**B) NetworkTopologyStrategy**
C) LocalStrategy
D) RackAwareStrategy

2、CQL中支持的集合类型包括（）？
**A) list**
**B) map**
**C) set**
**D) tuple**

3、Cassandra的读写一致性等级包括（）？
**A) ONE**
**B) QUORUM**
**C) ALL**
**D) LOCAL_QUORUM**

## 五、填空题

1、Cassandra的默认配置文件名称是**cassandra.yaml**。
2、CQL中用于删除数据的命令是**DELETE**。
3、Cassandra的Gossip协议用于**集群成员管理和故障检测**。
4、CQL中用于设置分簇列排序的语法是**WITH CLUSTERING ORDER BY**。

## 六、判断题（正确打√，错误打×）

1、Cassandra的主从复制是同步的。（**×**）
2、CQL支持对非主键列的条件查询。（**×**）
3、Cassandra的计数器列可以作为主键。（**×**）
4、CQL中UPDATE语句可以修改主键列。（**×**）