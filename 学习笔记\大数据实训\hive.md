好的，这里将整个 HiveQL 脚本分解为一个个独立的小步骤，并附上每一步的具体代码和说明。

---

### 阶段一：环境准备 (Environment Setup)

这个阶段为后续所有操作准备数据库环境。

#### 步骤 1: 创建数据库

*   **目的**: 创建一个名为 `highway` 的数据库来存放所有相关的表。
*   **代码**:
    ```sql
    CREATE DATABASE IF NOT EXISTS highway;
    ```
*   **说明**: `IF NOT EXISTS` 是一个安全检查，如果 `highway` 数据库已经存在，此命令不会执行任何操作，也不会报错，确保了脚本的可重复执行性。

#### 步骤 2: 切换到指定数据库

*   **目的**: 将当前操作环境设置为 `highway` 数据库，后续所有创建表的命令都将在此数据库中执行。
*   **代码**:
    ```sql
    USE highway;
    ```
*   **说明**: 执行此命令后，就不再需要在每个表名前都加上 `highway.` 前缀了。

---

### 阶段二：数据表结构定义 (Schema Definition)

这个阶段定义所有数据表的结构，包括列、数据类型和存储格式。

#### 步骤 3: 创建路段信息表 (ods_gsgl_section)

*   **目的**: 创建用于存储高速公路路段基础信息的表。
*   **代码**:
    ```sql
    -- 先删除旧表，确保从干净状态开始
    DROP TABLE IF EXISTS ods_gsgl_section;
    -- 创建新表
    CREATE TABLE ods_gsgl_section (
        id INT COMMENT '主键',
        road_code STRING COMMENT '路线编码',
        road_name STRING COMMENT '路线名称',
        section_code STRING COMMENT '路段编码',
        section_name STRING COMMENT '路段名称',
        start_pile_no DOUBLE COMMENT '起点桩号',
        end_pile_no DOUBLE COMMENT '终点桩号',
        org_code STRING COMMENT '机构代码',
        org_name STRING COMMENT '机构名称',
        mileage DOUBLE COMMENT '里程',
        open_date TIMESTAMP COMMENT '通车日期'
    )
    COMMENT '路段信息表'
    ROW FORMAT DELIMITED
    FIELDS TERMINATED BY ','
    STORED AS TEXTFILE;
    ```
*   **说明**: 该表定义了路段信息。`FIELDS TERMINATED BY ','` 指定了源数据文件（如 CSV）是以逗号作为列分隔符的。

#### 步骤 4: 创建车型库表 (ods_gsgl_vehicle_type)

*   **目的**: 创建用于存储车辆类型信息的表。
*   **代码**:
    ```sql
    DROP TABLE IF EXISTS ods_gsgl_vehicle_type;
    CREATE TABLE ods_gsgl_vehicle_type (
        plate_number STRING COMMENT '车牌号码',
        plate_color STRING COMMENT '车牌颜色',
        vehicle_type STRING COMMENT '识别车型',
        axle_count STRING COMMENT '车轴数',
        wheelbase STRING COMMENT '轴距（厘米）'
    )
    COMMENT '车型库'
    ROW FORMAT DELIMITED
    FIELDS TERMINATED BY ','
    STORED AS TEXTFILE;
    ```
*   **说明**: 该表用于关联车牌和具体的车辆物理属性。

#### 步骤 5: 创建收费站信息表 (ods_gsgl_toll)

*   **目的**: 创建用于存储收费站基础信息的表。
*   **代码**:
    ```sql
    DROP TABLE IF EXISTS ods_gsgl_toll;
    CREATE TABLE ods_gsgl_toll (
        id INT COMMENT '主键',
        road_code STRING COMMENT '路线编号',
        toll_code STRING COMMENT '收费站编号',
        toll_name STRING COMMENT '收费站名称',
        pile_no STRING COMMENT '桩号',
        org_name STRING COMMENT '所属机构名称',
        org_code STRING COMMENT '机构编码',
        longitude STRING COMMENT '经度',
        latitude STRING COMMENT '纬度',
        open_date TIMESTAMP COMMENT '开通日期',
        toll_alias_name STRING COMMENT '收费站别名',
        section_code STRING COMMENT '所属路段编码'
    )
    COMMENT '收费站信息'
    ROW FORMAT DELIMITED
    FIELDS TERMINATED BY ','
    STORED AS TEXTFILE;
    ```
*   **说明**: 存储每个收费站的位置、编码、名称等静态信息。

#### 步骤 6: 创建服务区信息表 (service)

*   **目的**: 创建用于存储高速公路服务区信息的表。
*   **代码**:
    ```sql
    DROP TABLE IF EXISTS ods_gsgl_service;
    CREATE TABLE service (
        id INT COMMENT '主键',
        road_code STRING COMMENT '路线编号',
        id_card STRING COMMENT '服务区编号',
        service_code STRING COMMENT '服务区编码',
        service_name STRING COMMENT '服务区名称',
        pile_no STRING COMMENT '桩号',
        direction CHAR(1) COMMENT '方向',
        org_name STRING COMMENT '机构名称',
        org_code STRING COMMENT '机构编码',
        longitude STRING COMMENT '经度',
        latitude STRING COMMENT '纬度',
        business_date TIMESTAMP COMMENT '营业日期',
        parking_num INT COMMENT '车位数',
        section_code STRING COMMENT '所属路段',
        section_name STRING COMMENT '所属路段名称',
        city_code STRING COMMENT '城市编码',
        district_name STRING COMMENT '行政区名称',
        has_cross CHAR(1) COMMENT '是否设置了卡口',
        charge_num INT COMMENT '充电桩数',
        food_service CHAR(1) COMMENT '是否餐饮服务',
        stay_service CHAR(1) COMMENT '是否提供住宿服务',
        automobile_service CHAR(1) COMMENT '是否提供汽修服务',
        supermarket_service CHAR(1) COMMENT '是否提供超市服务',
        gas_service CHAR(1) COMMENT '是否提供加油服务',
        wc_service CHAR(1) COMMENT '是否提供厕所服务'
    )
    COMMENT '服务区信息表'
    ROW FORMAT DELIMITED
    FIELDS TERMINATED BY ','
    STORED AS TEXTFILE;
    ```
*   **说明**: 详细记录了服务区的设施和服务项目。*注意：DROP语句是 `ods_gsgl_service`，而 CREATE 语句是 `service`，这可能是笔误，但代码会按此执行。*

#### 步骤 7: 创建门架信息表 (gantry)

*   **目的**: 创建用于存储龙门架（Gantry）基础信息的表。
*   **代码**:
    ```sql
    DROP TABLE IF EXISTS ods_gsgl_gantry;
    CREATE TABLE gantry (
        id INT COMMENT '主键',
        gantry_code STRING COMMENT '门架编号（分离）',
        gantry_name STRING COMMENT '门架名称',
        road_code STRING COMMENT '所属路线编码',
        section_code STRING COMMENT '所属路段编码',
        toll_code STRING COMMENT '所属收费站编码',
        org_code STRING COMMENT '所属管理中心机构编码',
        direction VARCHAR(1) COMMENT '方向',
        pile_no STRING COMMENT '桩号',
        lon STRING COMMENT '经度',
        lat STRING COMMENT '纬度',
        remark STRING COMMENT '备注',
        is_virtual STRING COMMENT '是否虚门架',
        mileage STRING COMMENT '里程',
        boundary_gantry_type STRING COMMENT '边界门架类型',
        gantry_type STRING COMMENT '门架类型',
        city_code STRING COMMENT '城市编码',
        district_name STRING COMMENT '行政区名称',
        gantry_name_alias STRING COMMENT '门架别名'
    )
    COMMENT '门架信息表'
    ROW FORMAT DELIMITED
    FIELDS TERMINATED BY ','
    STORED AS TEXTFILE;
    ```
*   **说明**: 存储了用于不停车收费的龙门架的详细信息。*注意：与上一步类似，DROP 和 CREATE 的表名不完全一致。*

#### 步骤 8: 创建车辆轨迹外部表 (ods_gsgl_vehicle_trails)

*   **目的**: 创建一个**外部表**来映射到 HDFS 上已存在的车辆轨迹原始数据。
*   **代码**:
    ```sql
    DROP TABLE IF EXISTS ods_gsgl_vehicle_trails;
    CREATE EXTERNAL TABLE ods_gsgl_vehicle_trails (
        id STRING COMMENT '主键',
        vlp STRING COMMENT '车牌号',
        vlpc STRING COMMENT '车牌颜色',
        identify_vtype STRING COMMENT '识别车型',
        identify_axle_count STRING COMMENT '识别车辆轴数',
        crossing_type STRING COMMENT '卡口类型',
        capture_time STRING COMMENT '通过时间',
        data_from STRING COMMENT '数据来源',
        gantry_type STRING COMMENT '门架类型',
        gantry_code STRING COMMENT '门架编号',
        gantry_name STRING COMMENT '门架名称',
        direction STRING COMMENT '通过卡口方向',
        toll_code STRING COMMENT '所属收费站编码',
        update_time STRING COMMENT '更新时间',
        send_time STRING COMMENT '抓拍服务器时间',
        plateno_des STRING COMMENT '加密车牌',
        plateno_display STRING COMMENT '脱敏车牌'
    )
    COMMENT '车辆轨迹'
    ROW FORMAT DELIMITED
    FIELDS TERMINATED BY ','
    STORED AS TEXTFILE
    LOCATION '/user/hadoop/highway_data/ods_gsgl_vehicle_trails';
    ```
*   **说明**:
    *   `CREATE EXTERNAL TABLE` 表示这是一个外部表。
    *   `LOCATION` 子句指定了数据文件在 HDFS 中的存储位置。
    *   与内部表不同，删除（`DROP`）外部表时，只会删除 Hive 中的元数据（表结构），而不会删除 HDFS 上的原始数据文件，这样做更安全。

---

### 阶段三：数据加载 (Data Loading)

这个阶段将 HDFS 上的原始数据文件加载到对应的 Hive 表中。

#### 步骤 9: 加载数据到路段表

*   **目的**: 将 `section.csv` 文件中的数据加载到 `ods_gsgl_section` 表。
*   **代码**:
    ```sql
    LOAD DATA INPATH '/user/hadoop/highway_data/section.csv' INTO TABLE ods_gsgl_section;
    ```

#### 步骤 10: 加载数据到门架表

*   **目的**: 将 `gantry.csv` 文件中的数据加载到 `gantry` 表。
*   **代码**:
    ```sql
    LOAD DATA INPATH '/user/hadoop/highway_data/gantry.csv' INTO TABLE gantry;
    ```

#### 步骤 11: 加载数据到服务区表

*   **目的**: 将 `service.csv` 文件中的数据加载到 `service` 表。
*   **代码**:
    ```sql
    LOAD DATA INPATH '/user/hadoop/highway_data/service.csv' INTO TABLE service;
    ```

#### 步骤 12: 加载数据到收费站表

*   **目的**: 将 `toll.csv` 文件中的数据加载到 `ods_gsgl_toll` 表。
*   **代码**:
    ```sql
    LOAD DATA INPATH '/user/hadoop/highway_data/toll.csv' INTO TABLE ods_gsgl_toll;
    ```

#### 步骤 13: 加载数据到车辆轨迹表

*   **目的**: 将 `vehicle_trails.csv` 文件中的数据加载到 `ods_gsgl_vehicle_trails` 表。
*   **代码**:
    ```sql
    LOAD DATA INPATH '/user/hadoop/highway_data/vehicle_trails.csv' INTO TABLE ods_gsgl_vehicle_trails;
    ```
*   **说明**: `LOAD DATA` 命令会将指定路径的文件**移动**到表定义时 `LOCATION` 指定的目录中。

#### 步骤 14: 加载数据到车型库表

*   **目的**: 将 `vehicle_type.csv` 文件中的数据加载到 `ods_gsgl_vehicle_type` 表。
*   **代码**:
    ```sql
    LOAD DATA INPATH '/user/hadoop/highway_data/vehicle_type.csv' INTO TABLE ods_gsgl_vehicle_type;
    ```

---

### 阶段四：数据验证 (Data Verification)

这个阶段通过执行简单的查询，检查数据是否已成功加载且格式正确。

#### 步骤 15: 统计车辆轨迹总记录数

*   **目的**: 快速检查 `ods_gsgl_vehicle_trails` 表的数据量，确认数据已加载。
*   **代码**:
    ```sql
    SELECT COUNT(*) FROM ods_gsgl_vehicle_trails;
    ```

#### 步骤 16: 验证路段信息表数据

*   **目的**: 查看 `ods_gsgl_section` 表的前 10 条记录，检查数据列是否对齐，内容是否符合预期。
*   **代码**:
    ```sql
    select * from ods_gsgl_section t LIMIT 10;
    ```

#### 步骤 17: 验证车型库数据

*   **目的**: 查看 `ods_gsgl_vehicle_type` 表的前 10 条记录。
*   **代码**:
    ```sql
    select * from ods_gsgl_vehicle_type t LIMIT 10;
    ```

#### 步骤 18: 验证车辆轨迹数据

*   **目的**: 查看 `ods_gsgl_vehicle_trails` 表的前 10 条记录。
*   **代码**:
    ```sql
    select * from ods_gsgl_vehicle_trails t LIMIT 10;
    ```

#### 步骤 19: 验证收费站基础信息数据

*   **目的**: 查看 `ods_gsgl_toll` 表的前 10 条记录。
*   **代码**:
    ```sql
    select * from ods_gsgl_toll t LIMIT 10;
    ```

#### 步骤 20: 验证服务区信息表数据

*   **目的**: 查看 `service` 表的前 10 条记录。
*   **代码**:
    ```sql
    select * from service t LIMIT 10;
    ```

#### 步骤 21: 验证门架信息表数据

*   **目的**: 查看 `gantry` 表的前 10 条记录。
*   **代码**:
    ```sql
    select * from gantry t LIMIT 10;
    ```