# Hadoop大数据项目实践手册

## 📋 目录

- [项目概述](#项目概述)
- [环境准备](#环境准备)
  - [基础环境配置](#基础环境配置)
  - [Hadoop集群搭建](#hadoop集群搭建)
  - [Hive环境配置](#hive环境配置)
  - [Spark集群配置](#spark集群配置)
  - [开发环境配置](#开发环境配置)
- [项目任务](#项目任务)
  - [数据准备阶段](#数据准备阶段)
  - [数据处理阶段](#数据处理阶段)
  - [数据分析阶段](#数据分析阶段)
  - [可视化与洞察](#可视化与洞察)
- [项目成果](#项目成果)
- [附录](#附录)

---

## 🎯 项目概述

### 项目目标
掌握从环境搭建、数据导入、MapReduce预处理、Hive数仓建设与HQL分析、Spark/SparkSQL分析与挖掘，到最终可视化与成果汇报的**全流程大数据项目实践**。

### 技术栈
- **计算框架**: Hadoop MapReduce, Apache Spark
- **数据仓库**: Apache Hive
- **开发语言**: Java, Scala, Python, HQL, SparkSQL
- **可视化**: PyEcharts
- **开发工具**: IntelliJ IDEA, DBeaver, Maven

### 数据集说明
| 数据文件                 | 描述       | 数据量级 |
| -------------------- | -------- | ---- |
| `vehicle_trails.csv` | 车辆通行轨迹数据 | 大规模  |
| `vehicle_type.csv`   | 车辆类型字典   | 小规模  |
| `gantry.csv`         | 门架基础信息   | 中等规模 |
| `section.csv`        | 路段基础信息   | 小规模  |
| `toll.csv`           | 收费站信息    | 小规模  |
| `service.csv`        | 服务区信息    | 小规模  |

---

## 🔧 环境准备

### 基础环境配置

#### 1. 虚拟机集群规划

| 节点名称 | IP配置 | 角色分配 |
|---------|--------|----------|
| master | 配置静态IP | 主节点 |
| slave1 | 配置静态IP | 从节点1 |
| slave2 | 配置静态IP | 从节点2 + Hive服务 |
| slave3 | 配置静态IP | 从节点3 |

#### 2. 域名解析配置

```bash
# 编辑所有节点的 /etc/hosts 文件
vim /etc/hosts

# 添加以下内容
192.168.x.x master
192.168.x.x slave1
192.168.x.x slave2
192.168.x.x slave3
```

> ⚠️ **注意**: 物理机的 hosts 文件也需要添加相同配置

### Hadoop集群搭建

#### 1. 软件版本要求
- **操作系统**: CentOS 7.x / Ubuntu 18.04+
- **JDK**: 1.8.x
- **Hadoop**: 3.x.x
- **虚拟化**: VMware Workstation Pro

#### 2. 集群架构

```
Master节点组件:
├── NameNode (HDFS主节点)
├── ResourceManager (YARN主节点)
├── SecondaryNameNode (辅助NN)
└── JobHistoryServer (作业历史)

Slave节点组件:
├── DataNode (HDFS数据节点)
└── NodeManager (YARN计算节点)
```

#### 3. 环境变量配置

```bash
# 编辑 ~/.bashrc 或 /etc/profile
export JAVA_HOME=/path/to/jdk
export HADOOP_HOME=/path/to/hadoop
export PATH=$PATH:$JAVA_HOME/bin:$HADOOP_HOME/bin:$HADOOP_HOME/sbin
```

#### 4. 集群管理命令

| 操作 | 命令 | 说明 |
|------|------|------|
| 启动集群 | `start-all.sh` | 启动HDFS和YARN |
| 启动历史服务器 | `mr-jobhistory-daemon.sh start historyserver` | 启动作业历史 |
| 停止集群 | `stop-all.sh` | 停止所有服务 |

#### 5. Web UI监控端口

| 服务 | 端口 | 访问地址 |
|------|------|----------|
| NameNode | 9870 | http://master:9870 |
| ResourceManager | 8088 | http://master:8088 |
| JobHistory | 19888 | http://master:19888 |

### Hive环境配置

#### 1. 软件安装清单
- Apache Hive 3.x.x
- MySQL 5.7+ (安装在master节点)
- MySQL JDBC驱动
- DBeaver (数据库管理工具)

#### 2. Hive架构配置

```
远程Metastore模式:
├── MySQL (master) - 元数据存储
├── Metastore Service (slave2) - 元数据服务
└── HiveServer2 (slave2) - Thrift服务
```

#### 3. 关键配置

```bash
# 环境变量
export HIVE_HOME=/path/to/hive
export PATH=$PATH:$HIVE_HOME/bin

# Warehouse目录
hdfs dfs -mkdir -p /user/hive/warehouse
hdfs dfs -chmod 777 /user/hive/warehouse
```

#### 4. 服务管理

```bash
# MySQL服务
systemctl start mysqld
systemctl enable mysqld

# Hive Metastore (在slave2执行)
nohup hive --service metastore &

# HiveServer2 (在slave2执行)
nohup hive --service hiveserver2 &
```

#### 5. 连接测试

```bash
# Beeline连接
beeline -u ************************* -n hadoop

# DBeaver连接
URL: *************************
用户名: hadoop
```

### Spark集群配置

#### 1. 集群架构

| 节点 | Spark组件 |
|------|-----------|
| master | Master, HistoryServer, ThriftServer |
| slave1-3 | Worker |
| slave2 | Hive Metastore/HiveServer2 |

#### 2. 服务管理命令

```bash
# 启动Spark集群
$SPARK_HOME/sbin/start-all.sh

# 启动历史服务器
$SPARK_HOME/sbin/start-history-server.sh

# 启动Thrift Server
$SPARK_HOME/sbin/start-thriftserver.sh
```

#### 3. Web UI端口

| 服务 | 端口 | 说明 |
|------|------|------|
| Spark Master | 8080 | 集群监控 |
| Spark History | 18080 | 作业历史 |
| Thrift Server | 10016 | JDBC连接 |

### 开发环境配置

#### Maven配置

```bash
# 1. 检查JDK
java -version

# 2. 下载解压Maven
tar -zxvf apache-maven-3.x.x-bin.tar.gz

# 3. 配置环境变量
export MAVEN_HOME=/path/to/maven
export PATH=$PATH:$MAVEN_HOME/bin

# 4. 验证安装
mvn -version
```

---

## 📊 项目任务

### 数据准备阶段

#### 任务1: HDFS数据导入

**目标要点**
- 理解HDFS架构原理
- 熟练使用HDFS命令
- 规范化数据存储目录

**实施步骤**

```bash
# 1. 创建本地暂存目录
mkdir -p /root/highway_data
# 上传所有CSV文件到此目录

# 2. 创建HDFS目录结构
hdfs dfs -mkdir -p /user/hadoop/highway_data

# 3. 批量上传数据
hdfs dfs -put /root/highway_data/*.csv /user/hadoop/highway_data/

# 4. 验证上传结果
hdfs dfs -ls -R /user/hadoop/highway_data/

# 5. 数据完整性检查
hdfs dfs -tail /user/hadoop/highway_data/gantry.csv
```

### 数据处理阶段

#### 任务2: MapReduce无效车牌统计

**目标要点**
- MapReduce编程模型理解
- 正则表达式数据清洗
- 分布式计算实践

**项目配置**

1. **Maven项目结构**
   ```
   hadoop-highway/
   ├── pom.xml
   ├── src/
   │   ├── main/
   │   │   ├── java/
   │   │   └── resources/
   │   │       ├── core-site.xml
   │   │       ├── hdfs-site.xml
   │   │       ├── yarn-site.xml
   │   │       └── log4j.properties
   ```

2. **pom.xml依赖配置**
   ```xml
   <dependencies>
       <dependency>
           <groupId>log4j</groupId>
           <artifactId>log4j</artifactId>
           <version>1.2.17</version>
       </dependency>
       <!-- 其他依赖 -->
   </dependencies>
   ```

**核心代码结构**

```java
// Mapper: 识别无效车牌
public class InvalidPlateMapper extends Mapper<...> {
    private static final Pattern VALID_PLATE = Pattern.compile(
        "^(WJ)?[京津沪...]..."
    );
    
    @Override
    protected void map(...) {
        // 提取vlp字段
        // 正则匹配判断
        // 输出<无效车牌, 1>
    }
}

// Reducer: 统计汇总
public class InvalidPlateReducer extends Reducer<...> {
    @Override
    protected void reduce(...) {
        // 累加计数
        // 输出<无效车牌, count>
    }
}
```

#### 任务3: Hive数据仓库构建

**目标要点**
- 数据仓库分层设计
- 内部表与外部表选择
- 数据加载策略

**数据表设计规范**

| 表类型 | 命名前缀 | 存储方式 | 适用场景 |
|--------|---------|----------|----------|
| 原始层 | ods_gsgl_ | 内部表/外部表 | 原始数据存储 |
| 标准层 | std_gsgl_ | 内部表 | 清洗后数据 |
| 汇总层 | dm_gsgl_ | 内部表 | 分析结果 |

**建表脚本示例**

```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS highway
COMMENT '高速公路数据仓库';
USE highway;

-- 创建外部表（大数据量）
CREATE EXTERNAL TABLE IF NOT EXISTS ods_gsgl_vehicle_trails (
    vlp STRING COMMENT '车牌号',
    gantry_code STRING COMMENT '门架编码',
    capture_time STRING COMMENT '通过时间',
    -- 其他字段...
) COMMENT '车辆通行轨迹原始数据'
ROW FORMAT DELIMITED 
FIELDS TERMINATED BY ','
STORED AS TEXTFILE
LOCATION '/user/hadoop/highway_data/ods_gsgl_vehicle_trails';

-- 创建内部表（小数据量）
CREATE TABLE IF NOT EXISTS ods_gsgl_gantry (
    gantry_code STRING COMMENT '门架编码',
    gantry_name STRING COMMENT '门架名称',
    -- 其他字段...
) COMMENT '门架基础信息表'
ROW FORMAT DELIMITED 
FIELDS TERMINATED BY ','
STORED AS TEXTFILE;

-- 数据加载
LOAD DATA INPATH '/user/hadoop/highway_data/gantry.csv' 
INTO TABLE ods_gsgl_gantry;
```

### 数据分析阶段

#### 任务4: 高速公路车流量统计

**分析维度**
1. **数据预处理流程**
   ```
   原始数据 → 清洗过滤 → 格式标准化 → 去重处理 → 标准表
   ```

2. **统计指标体系**
   - 路线级总流量
   - 路段级总流量
   - 时间维度流量趋势

**数据清洗SQL示例**

```sql
-- 创建标准化车辆轨迹表
CREATE TABLE std_gsgl_vehicle_trails AS
WITH cleaned_data AS (
    SELECT 
        REPLACE(TRIM(vlp), '"', '') as vlp,
        gantry_code,
        FROM_UNIXTIME(
            UNIX_TIMESTAMP(capture_time, 'yyyy/MM/dd HH:mm:ss'),
            'yyyy-MM-dd HH:mm:ss'
        ) as capture_time,
        -- 其他字段处理...
    FROM ods_gsgl_vehicle_trails
    WHERE vlp REGEXP '^(WJ)?[京津沪...]...' 
      AND vlp IS NOT NULL
),
ranked_data AS (
    SELECT *,
        LAG(capture_time, 1, capture_time) OVER (
            PARTITION BY vlp, gantry_code, direction 
            ORDER BY capture_time
        ) as prev_time
    FROM cleaned_data
)
SELECT * FROM ranked_data
WHERE UNIX_TIMESTAMP(capture_time) - UNIX_TIMESTAMP(prev_time) > 60
   OR prev_time = capture_time;
```

#### 任务5: 高速公路拥堵分析

**核心算法**

1. **平均速度计算公式**
   ```
   速度(km/h) = 距离(km) / 时间(h)
   ```

2. **拥堵指数(TI)计算公式**
   ```
   TI = (Vmax - Vavg) / Vmax × 100 + Qactual / Qcapacity × 100
   
   其中:
   - Vmax = 120 km/h (最大设计速度)
   - Qcapacity = 1500 辆/小时 (设计通行能力)
   ```

**分析步骤**

```sql
-- 1. 门架桩号标准化
UPDATE std_gsgl_gantry
SET pile_no_km = 
    CASE 
        WHEN pile_no REGEXP '^K[0-9]+\\+[0-9]+$' THEN
            CAST(SUBSTRING(pile_no, 2, LOCATE('+', pile_no) - 2) AS DECIMAL(10,3)) +
            CAST(SUBSTRING(pile_no, LOCATE('+', pile_no) + 1) AS DECIMAL(10,3)) / 1000
        ELSE NULL
    END;

-- 2. 计算相邻门架平均速度
CREATE TABLE dm_gsgl_speed_trails AS
SELECT 
    t1.vlp,
    t1.gantry_code as from_gantry,
    t2.gantry_code as to_gantry,
    ABS(t2.pile_no_km - t1.pile_no_km) as distance_km,
    (UNIX_TIMESTAMP(t2.capture_time) - UNIX_TIMESTAMP(t1.capture_time)) as time_seconds,
    (ABS(t2.pile_no_km - t1.pile_no_km) / 
     NULLIF(UNIX_TIMESTAMP(t2.capture_time) - UNIX_TIMESTAMP(t1.capture_time), 0)) * 3600 as avg_speed_kmph
FROM dm_gsgl_ranked_trails t1
JOIN dm_gsgl_ranked_trails t2
    ON t1.vlp = t2.vlp 
    AND t1.direction = t2.direction
    AND t2.rn = t1.rn + 1
WHERE (UNIX_TIMESTAMP(t2.capture_time) - UNIX_TIMESTAMP(t1.capture_time)) > 0
  AND calculated_speed BETWEEN 10 AND 200;
```

#### 任务6: Spark车型差异分析

**技术要点**
- Spark Core RDD操作
- 数据解析与转换
- 分布式统计计算

**Scala实现示例**

```scala
object VehicleTypeCount {
  def main(args: Array[String]): Unit = {
    val conf = new SparkConf().setAppName("VehicleTypeCount")
    val sc = new SparkContext(conf)
    
    // 车型编码映射
    val vehicleTypeMap = Map(
      "1" -> "小型客车",
      "2" -> "大型客车",
      "3" -> "小型货车",
      "4" -> "大型货车"
    )
    
    // 读取并处理数据
    val vehicleTypeCounts = sc.textFile("/path/to/std_gsgl_vehicle_trails")
      .map(_.split("\001"))
      .filter(_.length > 10)
      .map(fields => fields(10)) // identify_vtype字段
      .map(code => vehicleTypeMap.getOrElse(code, "未知类型"))
      .map(vtype => (vtype, 1))
      .reduceByKey(_ + _)
      .sortBy(_._2, ascending = false)
    
    // 输出结果
    vehicleTypeCounts.foreach(println)
    vehicleTypeCounts.coalesce(1)
      .saveAsTextFile("/user/hadoop/highway_data/VehicleTypeCount")
  }
}
```

### 可视化与洞察

#### 任务7: 交通状况画像构建

**分析维度框架**

```
交通状况画像
├── 时间维度
│   ├── 高峰时段识别
│   ├── 流量时序分析
│   └── 拥堵指数变化
├── 空间维度
│   ├── 路段热力图
│   ├── 门架流量分布
│   └── 地理位置标注
└── 瓶颈识别
    ├── 低速路段TOP10
    ├── 拥堵热点分析
    └── 异常事件检测
```

**Python可视化实现**

```python
from pyspark.sql import SparkSession
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Line, Geo, Bar, Page
from pyecharts.globals import ChartType

# 初始化Spark Session
spark = SparkSession.builder \
    .appName("HighwayVisualization") \
    .config("hive.metastore.uris", "thrift://slave2:9083") \
    .enableHiveSupport() \
    .getOrCreate()

# 时间分布分析
def create_hourly_traffic_chart():
    df = spark.sql("""
        SELECT hour, 
               AVG(traffic_volume) as avg_volume,
               AVG(congestion_index) as avg_ti
        FROM highway.dm_gsgl_hourly_traffic
        GROUP BY hour
        ORDER BY hour
    """).toPandas()
    
    line = Line()
    line.add_xaxis(df['hour'].tolist())
    line.add_yaxis("车流量", df['avg_volume'].tolist())
    line.add_yaxis("拥堵指数", df['avg_ti'].tolist(), yaxis_index=1)
    line.set_global_opts(
        title_opts=opts.TitleOpts(title="24小时交通流量与拥堵指数")
    )
    return line

# 生成综合大屏
page = Page(layout=Page.SimplePageLayout)
page.add(
    create_hourly_traffic_chart(),
    create_section_heatmap(),
    create_bottleneck_ranking()
)
page.render("highway_traffic_dashboard.html")
```

#### 任务8: 交通流量关联分析

**关联分析框架**

| 分析类型 | 数据源 | 关键指标 |
|---------|--------|----------|
| 车型特征 | 车辆轨迹 + 车型字典 | 不同车型通行量、速度差异 |
| 收费站流量 | 车辆轨迹 + 收费站信息 | 各收费站小时流量 |
| 服务区流量 | 车辆轨迹 + 服务区信息 | 进出服务区流量 |

**SparkSQL分析示例**

```sql
-- 车型通行特征分析
CREATE TABLE dm_gsgl_vehicle_type_analysis AS
SELECT 
    vt.vehicle_type_name,
    DATE_FORMAT(vtr.capture_time, 'yyyy-MM-dd HH') as hour,
    COUNT(DISTINCT vtr.vlp) as vehicle_count,
    AVG(speed.avg_speed_kmph) as avg_speed
FROM std_gsgl_vehicle_trails vtr
JOIN std_gsgl_vehicle_type vt 
    ON vtr.identify_vtype = vt.vehicle_type_code
LEFT JOIN dm_gsgl_speed_trails speed
    ON vtr.vlp = speed.vlp 
    AND vtr.gantry_code = speed.from_gantry
GROUP BY vt.vehicle_type_name, DATE_FORMAT(vtr.capture_time, 'yyyy-MM-dd HH');
```

---

## 📈 项目成果

### 成果清单

1. **数据资产**
   - 标准化数据表 (std_gsgl_*)
   - 分析结果表 (dm_gsgl_*)
   - 数据质量报告

2. **分析报告**
   - 车流量统计报告
   - 拥堵分析报告
   - 车型差异分析报告
   - 关联分析报告

3. **可视化大屏**
   - 实时交通监控大屏
   - 历史趋势分析大屏
   - 管理决策支持大屏

4. **技术文档**
   - 项目实施手册
   - 代码说明文档
   - 运维操作手册

### 汇报准备

**PPT结构建议**
1. 项目背景与目标 (2页)
2. 技术架构与实施 (3页)
3. 数据处理流程 (2页)
4. 核心分析成果 (5页)
5. 可视化展示 (3页)
6. 项目价值与展望 (1页)

**汇报要点**
- 时长控制: 15分钟
- 重点突出: 业务价值
- 演示准备: 确保环境可用
- 团队分工: 明确展示部分

---

## 📚 附录

### A. 常用命令速查

#### HDFS命令
```bash
hdfs dfs -mkdir -p <path>          # 创建目录
hdfs dfs -put <local> <hdfs>       # 上传文件
hdfs dfs -get <hdfs> <local>       # 下载文件
hdfs dfs -rm -r <path>             # 删除文件/目录
hdfs dfs -ls -R <path>             # 递归列出目录
```

#### Hive命令
```bash
beeline -u ************************* -n hadoop  # 连接Hive
SHOW DATABASES;                                   # 查看数据库
SHOW TABLES;                                      # 查看表
DESCRIBE FORMATTED <table>;                       # 查看表详情
```

#### Spark命令
```bash
spark-submit --class <MainClass> <jar-file>      # 提交Spark作业
spark-shell                                       # 启动Spark Shell
pyspark                                          # 启动PySpark
```

### B. 故障排查指南

| 问题类型 | 检查项 | 解决方案 |
|---------|--------|----------|
| HDFS连接失败 | NameNode状态 | 检查9870端口，重启HDFS |
| Hive查询超时 | Metastore服务 | 检查MySQL连接，重启Metastore |
| Spark作业失败 | 资源分配 | 调整executor内存和数量 |
| 网络不通 | 防火墙设置 | 关闭防火墙或添加规则 |

### C. 性能优化建议

1. **HDFS优化**
   - 合理设置副本数
   - 使用压缩存储
   - 定期执行balance

2. **Hive优化**
   - 使用分区表
   - 开启动态分区
   - 合理设置并行度

3. **Spark优化**
   - 调整分区数
   - 使用持久化
   - 避免数据倾斜

### D. 参考资源

- [Apache Hadoop官方文档](https://hadoop.apache.org/docs/)
- [Apache Hive官方文档](https://hive.apache.org/)
- [Apache Spark官方文档](https://spark.apache.org/docs/latest/)
- [PyEcharts中文文档](https://pyecharts.org/#/zh-cn/)

---

> 📝 **文档版本**: v2.0  
> 📅 **更新日期**: 2024-12  
> 👥 **维护团队**: 大数据项目组