# Linux 操作系统期末复习测验题库 (加量版)

**说明**：请先尝试独立完成所有题目，然后再核对下方的答案。

---

## 一、选择题

1.  在Linux中，“一切皆文件”思想的体现不包括？
    A. 硬盘 `/dev/sda`
    B. Shell的管道 `|`
    C. 终端 `/dev/tty`
    D. 普通文件 `note.txt`

2.  哪个目录最可能包含重要的系统日志文件？
    A. `/tmp`
    B. `/var/log`
    C. `/etc/log`
    D. `/usr/log`

3.  要强制删除一个非空目录 `mydir`，应该使用哪个命令？
    A. `rm mydir`
    B. `rmdir mydir`
    C. `rm -rf mydir`
    D. `del mydir`

4.  `chmod 644 file.txt` 这个命令设置的权限是什么？
    A. `r--rw-r--`
    B. `rw-r--r--`
    C. `rwxr-xr-x`
    D. `rw-rw-r--`

5.  在 `vi` 编辑器中，从命令模式切换到插入模式，不可以按哪个键？
    A. `i`
    B. `a`
    C. `o`
    D. `Esc`

6.  哪个命令可以实时动态地查看系统进程信息？
    A. `ps -aux`
    B. `top`
    C. `kill`
    D. `netstat`

7.  要将目录 `/home/<USER>/data` 打包并使用gzip压缩成 `data.tar.gz`，正确的 `tar` 命令是？
    A. `tar -cvf data.tar.gz /home/<USER>/data`
    B. `tar -xzvf data.tar.gz /home/<USER>/data`
    C. `tar -czvf data.tar.gz /home/<USER>/data`
    D. `tar -fzc data.tar.gz /home/<USER>/data`

8.  想要查找当前目录下所有扩展名为 `.log` 的文件，应该用什么命令？
    A. `find . -name "*.log"`
    B. `grep ".log" .`
    C. `ls *.log`
    D. `search . -ext log`

9.  以下哪个命令用于切换用户身份？
    A. `pwd`
    B. `su`
    C. `chown`
    D. `useradd`

10. `head -n 20 file.txt` 命令的作用是？
    A. 显示文件最后20行
    B. 显示文件开头20行
    C. 显示文件第20行
    D. 创建一个20行的文件

11. 在Shell中，哪个符号用于将一个命令的输出作为另一个命令的输入？
    A. `>`
    B. `<`
    C. `|`
    D. `&`

12. 哪个文件储存了系统的用户信息？
    A. `/etc/shadow`
    B. `/etc/group`
    C. `/etc/passwd`
    D. `/etc/profile`

13. 创建一个软链接 `link_name` 指向文件 `target_file` 的正确命令是？
    A. `ln -s link_name target_file`
    B. `ln link_name target_file`
    C. `ln -s target_file link_name`
    D. `ln target_file link_name`

14. 要终止一个进程ID为 `1234` 的进程，最强硬的方式是？
    A. `kill 1234`
    B. `kill -1 1234`
    C. `kill -9 1234`
    D. `kill -15 1234`

15. `ls -l` 的输出结果中，哪一项不包含在内？
    A. 文件权限
    B. 文件所有者
    C. 文件大小
    D. 文件的inode号

16. 在`vi`中，执行了错误的修改，想撤销操作，在命令模式下应按什么键？
    A. `x`
    B. `u`
    C. `dd`
    D. `yy`

17. 哪个命令不能用来查看文件内容？
    A. `cat`
    B. `less`
    C. `more`
    D. `mkdir`

18. `mv file1.txt doc/` 命令执行后，`file1.txt` 会怎样？
    A. 被复制到 `doc` 目录
    B. 被移动到 `doc` 目录
    C. 被重命名为 `doc`
    D. 被删除

19. 查看网络接口配置信息，应该使用哪个命令？
    A. `netstat`
    B. `ping`
    C. `ifconfig` 或 `ip addr`
    D. `route`

20. 在RedHat/CentOS系统中，用于安装 `.rpm` 软件包的命令是？
    A. `apt-get install`
    B. `rpm -ivh`
    C. `yum remove`
    D. `dpkg -i`

21. `echo "hello" > world.txt` 命令会产生什么结果？
    A. 在屏幕上输出 `hello > world.txt`
    B. 如果 `world.txt` 存在，则在文件末尾追加 `hello`
    C. 创建或覆盖 `world.txt`，内容为 `hello`
    D. 在 `world.txt` 中查找 `hello`

22. `ps -ef | grep ssh` 这条命令的作用是？
    A. 查找所有包含 `ssh` 字符串的文件
    B. 启动一个名为 `grep` 的ssh连接
    C. 在所有进程中筛选出与 `ssh` 相关的进程
    D. 连接到 `ssh` 服务器并执行 `grep`

23. `touch file.txt` 命令的主要作用是？
    A. 编辑 `file.txt`
    B. 查看 `file.txt` 的类型
    C. 创建一个空的 `file.txt` 或更新其时间戳
    D. 删除 `file.txt`

24. 哪个目录通常用来存放临时文件，且系统重启后可能会被清空？
    A. `/var`
    B. `/tmp`
    C. `/opt`
    D. `/mnt`

25. `useradd -m -d /home/<USER>
    A. 设置用户的主目录
    B. 创建用户的同时创建其家目录
    C. 指定用户的密码
    D. 将用户添加到指定的用户组

26. 在`vi`的命令模式下，快速跳到文件最后一行的命令是？
    A. `$`
    B. `G`
    C. `gg`
    D. `L`

27. `rmdir` 命令的限制是？
    A. 只能删除文件
    B. 只能删除空目录
    C. 只能由root用户执行
    D. 只能删除软链接

28. 符号 `~` 在Linux Shell中代表什么？
    A. 根目录
    B. 上一级目录
    C. 当前用户的家目录
    D. 当前工作目录

---

## 二、填空题

1.  在Linux中，代表普通用户的家目录通常是 `/home/<USER>
2.  `ls -l` 命令输出的权限 `drwxr-xr--` 中，第一个字符 `d` 代表这是一个 `_______`。
3.  修改文件所有者的命令是 `_______`，修改文件所属组的命令是 `_______`。
4.  `cd ..` 命令的作用是切换到上一级目录，而 `cd -` 的作用是 `_______`。
5.  查看文件末尾内容，并且实时更新文件新增内容的命令是 `tail -f 文件名`。
6.  在 `vi` 编辑器的末行模式下，强制退出且不保存的命令是 `_______`。
7.  给一个脚本 `run.sh` 添加可执行权限，使用符号法的命令是 `chmod a+x run.sh`。
8.  将命令的输出结果覆盖写入到文件使用 `>` 符号，而追加到文件末尾使用 `_______` 符号。
9.  在 `tar` 命令中，`-c` 参数表示创建归档，而 `-x` 参数表示 `_______`。
10. 进程有前台和后台之分，在命令末尾加上 `_______` 符号可以使进程在后台运行。
11. `find / -name "*.conf"` 命令的作用是在 `_______` 目录下查找所有以 `.conf` 结尾的文件。
12. `grep` 命令用于文本搜索，如果想忽略大小写进行搜索，应该使用 `_______` 参数。
13. `ifconfig` 命令用于查看和配置网络接口，其功能与 `ip` 命令中的 `_______` 子命令类似。
14. 文件的权限可以用数字表示，`r` 是 `4`，`w` 是 `2`，`x` 是 `_______`。
15. 存放用户密码hash值的文件是 `_______`。
16. `ps` 命令用于查看进程快照，常用的参数组合是 `aux` 或者 `_______`。
17. 使用 `useradd` 创建新用户后，需要使用 `_______` 命令为其设置初始密码。
18. 解压一个名为 `archive.tar.gz` 的文件，需要同时使用 `z` 和 `x` 参数，其完整命令是 `tar -xzvf archive.tar.gz`。
19. `pwd` 命令的功能是 `_______`。
20. 在`vi`命令模式下，删除当前光标所在行的命令是 `_______`。
21. `FHS` 是Linux文件系统标准的缩写，其全称是 `_______`。
22. `ls -a` 命令可以列出所有文件，包括以 `_______` 符号开头的隐藏文件。
23. `ln -s source target` 创建的是 `_______` 链接。
24. `kill` 命令默认发送的信号编号是 `15`，代表 `_______` 信号。
25. `/bin` 目录存放的是所有用户可用的基本命令，而 `/sbin` 存放的是 `_______` 才能使用的管理命令。
26. `more` 和 `less` 都可以分页查看文件，但 `_______` 功能更强，支持前后翻页和搜索。
27. `whoami` 命令的作用是显示 `_______`。
28. 将 `file.txt` 重命名为 `new_file.txt` 的命令是 `mv file.txt new_file.txt`。

---

## 三、简答题

1.  简述Linux的四个基本哲学思想是什么？
2.  软链接（Symbolic Link）和硬链接（Hard Link）有什么主要区别？
3.  `ps` 和 `top` 命令在使用场景上有什么不同？
4.  解释 `rm -rf /` 命令的含义以及为什么它被认为Linux系统中最危险的命令之一。
5.  什么是Shell？请列举至少两种常见的Shell。
6.  解释标准输入(stdin)、标准输出(stdout)和标准错误(stderr)的概念。
7.  在进行文件打包压缩时，为什么 `tar` 命令常常和 `gzip` 或 `bzip2` 一起使用？
8.  解释 `PATH` 环境变量的作用。
9.  简述 `su` 和 `su -` 两个命令之间的区别。
10. 当你无法卸载一个正在被使用的文件系统时，应该如何排查和解决？
11. 什么是inode？它包含了文件的哪些信息？
12. `chmod u+x,g=rw,o-r file` 这个命令具体做了什么？
13. 解释 `|` (管道) 和 `>` (重定向) 的功能和区别。
14. 如何在后台运行一个长时间执行的命令？如何查看后台任务并将其调回前台？
15. 在`vi`编辑器中，命令模式、插入模式和末行模式各自的主要用途是什么？
16. 简述 `/etc/passwd`, `/etc/shadow`, `/etc/group` 三个文件的作用和关联。

---

## 答案与解析

### 一、选择题答案
1. B  2. B  3. C  4. B  5. D  6. B  7. C  8. A  9. B  10. B
11. C 12. C 13. C 14. C 15. D 16. B 17. D 18. B 19. C 20. B
21. C 22. C 23. C 24. B 25. B 26. B 27. B 28. C

### 二、填空题答案
1. `/root`
2. 目录 (directory)
3. `chown`, `chgrp`
4. 切换到上一次所在的目录
5. `tail -f 文件名`
6. `:q!`
7. `a+x`
8. `>>`
9. 提取归档 (extract)
10. `&`
11. 根目录 (`/`)
12. `-i`
13. `addr`
14. `1`
15. `/etc/shadow`
16. `-ef`
17. `passwd`
18. `tar -xzvf archive.tar.gz`
19. 显示当前工作目录的绝对路径 (Print Working Directory)
20. `dd`
21. Filesystem Hierarchy Standard
22. `.` (点)
23. 软 (symbolic)
24. TERM (terminate)
25. 超级用户 (root)
26. `less`
27. 当前登录的用户名
28. `mv file.txt new_file.txt`

### 三、简答题答案
1.  **四个基本思想**：1) 一切皆文件。2) 由众多功能单一的小程序组成。3) 尽量避免与用户交互。4) 使用纯文本文件存储配置。
2.  **软硬链接区别**：主要区别在于inode。硬链接与原文件共享inode，本质是同一个文件；软链接有自己的inode，是一个独立文件，内容是目标文件的路径。因此软链接可跨分区，硬链接不行。
3.  **`ps`与`top`区别**：`ps`是静态的，显示执行时刻的进程快照。`top`是动态的，实时刷新显示进程活动和系统负载，用于监控。
4.  **`rm -rf /`**：意为“强制、递归地删除根目录`/`下的一切”。它会无提示地删除整个操作系统和所有数据，导致系统完全崩溃，无法恢复。
5.  **Shell**：是用户与Linux内核交互的命令行界面程序。它接收用户命令，解释并交给内核执行。常见的有 `bash` (Bourne-Again SHell) 和 `zsh` (Z Shell)。
6.  **标准输入/输出/错误**：是Linux进程与外部交互的三个标准通道。`stdin` (0) 是进程接收输入的地方，`stdout` (1) 是进程输出正常结果的地方，`stderr` (2) 是进程输出错误信息的地方。
7.  **`tar`与压缩**：`tar`本身只负责将多个文件打包成一个大文件（归档），不具备压缩功能。`gzip`等是压缩工具。两者结合使用，可以先将文件归档，再对这个大文件进行压缩，以减小体积，方便传输和存储。
8.  **`PATH`环境变量**：它是一系列用冒号分隔的目录路径。当用户输入一个命令时，Shell会按顺序在`PATH`变量所包含的这些目录中查找对应的可执行文件。如果找到就执行，否则报错。
9.  **`su`与`su -`区别**：`su username` 只切换用户身份，但仍然使用原始用户的环境变量。`su - username` (或 `su -l username`) 是“login shell”方式切换，会完全模拟新用户的登录过程，加载其完整的环境变量和配置文件，工作目录也会切换到新用户的家目录。
10. **无法卸载文件系统**：通常是因为有进程正在访问该文件系统。可以使用 `fuser -m /path/to/mountpoint` 或 `lsof /path/to/mountpoint` 命令来查找是哪些进程在占用。找到后，`kill` 掉相关进程，然后再执行 `umount`。
11. **inode**：是“索引节点”，是Linux文件系统用来记录文件元数据的一种数据结构。它包含文件的权限、所有者、大小、创建时间、修改时间以及指向文件数据块的指针等，**但不包含文件名**。文件名存在目录项中，目录项将文件名和inode号关联起来。
12. **`chmod u+x,g=rw,o-r file`**：这是一个复合权限修改命令。它对`file`文件进行了如下操作：为所有者(user)增加执行权限(`u+x`)；为所属组(group)设置权限为只读只写(`g=rw`)；为其他用户(others)移除读取权限(`o-r`)。
13. **`|`与`>`**：`|` (管道) 用于连接两个命令，将前一个命令的`stdout`直接作为后一个命令的`stdin`，实现数据流在进程间的传递。 `>` (重定向) 用于将命令的`stdout`输出到指定的文件中，会覆盖文件原有内容。
14. **后台任务**：在命令末尾加 `&` 可使其在后台运行。使用 `jobs` 命令可以查看所有后台任务。使用 `fg %任务号` (如 `fg %1`) 可以将指定的后台任务调回前台执行。
15. **`vi`三模式**：**命令模式**是默认模式，用于移动光标、删除、复制、粘贴文本等操作；**插入模式**用于正常的文本内容输入；**末行模式** (通过在命令模式下输入`:`) 用于执行保存、退出、搜索、替换等扩展命令。
16. **三个用户文件**：`/etc/passwd` 存储用户信息（用户名、UID、GID、家目录、Shell），是公开可读的。`/etc/shadow` 存储用户的密码哈希值和密码策略，只有root可读，增强了安全性。`/etc/group` 定义了用户组及其成员。