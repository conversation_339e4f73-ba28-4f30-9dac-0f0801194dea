# Nginx负载均衡完全配置指南

## 1. 负载均衡基础概念

### 什么是负载均衡？

**生动比喻**：想象一个繁忙的银行，如果只有一个柜台，客户就要排长队。负载均衡就像银行的叫号系统，智能地把客户分配到不同的柜台，让每个柜台的工作量都比较均匀。

### 为什么需要负载均衡？

- **提高性能**：多台服务器同时工作，处理能力倍增
- **提高可用性**：一台服务器挂了，其他服务器继续工作
- **扩展性**：需要更多处理能力时，直接加服务器
- **维护友好**：可以轮流维护服务器，不影响服务

## 2. Nginx负载均衡算法

### 2.1 轮询（Round Robin）- 默认算法

**比喻**：就像排队点餐，按顺序一个一个来，公平公正。

```nginx
upstream backend {
    server ************;
    server ************;
    server ************;
}

server {
    listen 80;
    location / {
        proxy_pass http://backend;
    }
}
```

### 2.2 加权轮询（Weighted Round Robin）

**比喻**：有些收银员经验丰富速度快，可以处理更多客户，所以给他们分配更多的客户。

```nginx
upstream backend {
    server ************ weight=3;  # 权重3，处理更多请求
    server ************ weight=2;  # 权重2
    server ************ weight=1;  # 权重1，处理最少请求
}
```

### 2.3 IP哈希（IP Hash）

**比喻**：像会员制银行，每个客户都有固定的专属柜台，确保同一客户总是去同一个柜台。

```nginx
upstream backend {
    ip_hash;  # 根据客户端IP分配服务器
    server ************;
    server ************;
    server ************;
}
```

**使用场景**：需要保持会话状态的应用（如购物车、用户登录状态）

### 2.4 最少连接（Least Connections）

**比喻**：智能排队系统，总是把新客户分配给当前排队人数最少的柜台。

```nginx
upstream backend {
    least_conn;  # 连接数最少的服务器优先
    server ************;
    server ************;
    server ************;
}
```

### 2.5 一致性哈希（需要第三方模块）

**比喻**：像智能快递柜，根据包裹特征分配到特定格子，即使有格子坏了，大部分包裹还是能找到对应位置。

## 3. 服务器状态管理

### 3.1 服务器参数配置

```nginx
upstream backend {
    server ************:8080 weight=3 max_fails=2 fail_timeout=30s;
    server ************:8080 weight=2 max_fails=2 fail_timeout=30s;
    server ************:8080 weight=1 backup;  # 备份服务器
    server ************:8080 down;             # 临时下线
}
```

**参数说明**：

- `weight`：权重，默认为1
- `max_fails`：最大失败次数，默认为1
- `fail_timeout`：失败超时时间，默认10秒
- `backup`：备份服务器，只有其他服务器都挂了才使用
- `down`：标记服务器临时不参与负载均衡

### 3.2 健康检查原理

**比喻**：就像银行定期检查每个柜台是否正常工作，发现柜台出问题就暂时不派客户过去。

```nginx
# Nginx自带的被动健康检查
upstream backend {
    server ************ max_fails=3 fail_timeout=30s;
    server ************ max_fails=3 fail_timeout=30s;
}

# 如果服务器连续3次请求失败，就标记为不可用30秒
```

## 4. 完整配置示例

### 4.1 基础Web应用负载均衡

```nginx
# /etc/nginx/nginx.conf

# 定义后端服务器组
upstream web_backend {
    # 加权轮询
    server ************:3000 weight=3 max_fails=2 fail_timeout=30s;
    server ************:3000 weight=2 max_fails=2 fail_timeout=30s;
    server ************:3000 weight=1 max_fails=2 fail_timeout=30s;
    
    # 备份服务器
    server ************:3000 backup;
    
    # 保持连接，提高性能
    keepalive 32;
}

# 虚拟主机配置
server {
    listen 80;
    server_name www.example.com;
    
    # 访问日志
    access_log /var/log/nginx/lb_access.log;
    error_log /var/log/nginx/lb_error.log;
    
    location / {
        # 代理到后端服务器组
        proxy_pass http://web_backend;
        
        # 代理设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        
        # 错误页面
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
```

### 4.2 API服务负载均衡

```nginx
# API服务器组
upstream api_backend {
    # 使用IP哈希保持会话一致性
    ip_hash;
    
    server ************:8080;
    server ************:8080;
    server ************:8080;
}

server {
    listen 80;
    server_name api.example.com;
    
    # API路由
    location /api/ {
        proxy_pass http://api_backend;
        
        # API特殊设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # 支持WebSocket
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置（API可能处理时间较长）
        proxy_connect_timeout 10s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
    }
}
```

### 4.3 文件服务负载均衡

```nginx
# 文件服务器组
upstream file_backend {
    # 最少连接算法，适合文件传输
    least_conn;
    
    server ************:9000 weight=2;
    server ************:9000 weight=2;
    server ************:9000 weight=1;
}

server {
    listen 80;
    server_name files.example.com;
    
    # 文件上传大小限制
    client_max_body_size 100M;
    
    location /files/ {
        proxy_pass http://file_backend;
        
        # 文件传输优化
        proxy_request_buffering off;  # 不缓冲请求体，适合大文件上传
        proxy_buffering off;          # 不缓冲响应，适合大文件下载
        
        # 长连接设置
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        
        # 超时设置（文件传输可能很慢）
        proxy_connect_timeout 10s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
}
```

## 5. 高级配置技巧

### 5.1 基于路径的负载均衡

```nginx
# 不同的后端服务器组
upstream web_servers {
    server ************:3000;
    server ************:3000;
}

upstream api_servers {
    server ************:8080;
    server ************:8080;
}

upstream static_servers {
    server ************:80;
    server ************:80;
}

server {
    listen 80;
    server_name example.com;
    
    # 根据不同路径分发到不同服务器组
    location / {
        proxy_pass http://web_servers;
    }
    
    location /api/ {
        proxy_pass http://api_servers;
    }
    
    location /static/ {
        proxy_pass http://static_servers;
    }
}
```

### 5.2 动态负载均衡（需要Nginx Plus或第三方模块）

```nginx
# 使用nginx-upstream-fair模块
upstream backend {
    fair;  # 响应时间最短的服务器优先
    server ************;
    server ************;
    server ************;
}
```

### 5.3 会话保持（Session Sticky）

```nginx
# 方法1：使用IP Hash
upstream backend {
    ip_hash;
    server ************;
    server ************;
}

# 方法2：使用Cookie（需要第三方模块）
upstream backend {
    sticky cookie srv_id expires=1h path=/;
    server ************;
    server ************;
}
```

## 6. 监控和故障处理

### 6.1 监控配置

```nginx
# 状态监控页面
server {
    listen 8080;
    server_name localhost;
    
    location /nginx_status {
        stub_status on;
        access_log off;
        allow 127.0.0.1;
        allow ***********/24;
        deny all;
    }
    
    # 后端服务器状态（需要第三方模块）
    location /upstream_status {
        upstream_status;
        access_log off;
        allow 127.0.0.1;
        deny all;
    }
}
```

### 6.2 日志分析

```nginx
# 自定义日志格式
log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                '$status $body_bytes_sent "$http_referer" '
                '"$http_user_agent" "$http_x_forwarded_for" '
                'upstream_addr=$upstream_addr '
                'upstream_status=$upstream_status '
                'upstream_response_time=$upstream_response_time '
                'request_time=$request_time';

server {
    access_log /var/log/nginx/access.log main;
}
```

## 7. 性能优化

### 7.1 连接优化

```nginx
upstream backend {
    server ************;
    server ************;
    
    # 保持长连接
    keepalive 16;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

server {
    location / {
        proxy_pass http://backend;
        
        # 启用HTTP/1.1和长连接
        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }
}
```

### 7.2 缓存策略

```nginx
# 缓存配置
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=my_cache:10m 
                 max_size=10g inactive=60m use_temp_path=off;

server {
    location / {
        proxy_pass http://backend;
        
        # 缓存设置
        proxy_cache my_cache;
        proxy_cache_valid 200 302 10m;
        proxy_cache_valid 404 1m;
        proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
        proxy_cache_background_update on;
        proxy_cache_lock on;
        
        # 缓存头信息
        add_header X-Cache-Status $upstream_cache_status;
    }
}
```

## 8. 故障排查

### 8.1 常见问题和解决方案

**问题1：某台服务器频繁被标记为不可用**

```nginx
# 调整健康检查参数
upstream backend {
    server ************ max_fails=5 fail_timeout=60s;  # 增加容错次数和时间
}
```

**问题2：负载不均衡**

```nginx
# 检查权重设置
upstream backend {
    server ************ weight=1;  # 确保权重合理
    server ************ weight=1;
}
```

**问题3：会话丢失**

```nginx
# 使用ip_hash保持会话
upstream backend {
    ip_hash;
    server ************;
    server ************;
}
```

### 8.2 调试命令

```bash
# 检查nginx配置
nginx -t

# 重新加载配置
nginx -s reload

# 查看错误日志
tail -f /var/log/nginx/error.log

# 查看访问日志
tail -f /var/log/nginx/access.log

# 检查后端服务器连接
curl -I http://************:3000/

# 测试负载均衡
for i in {1..10}; do curl -s http://example.com/ | grep "Server"; done
```

## 9. 实战部署步骤

### 第一步：准备后端服务器

```bash
# 在每台后端服务器上启动应用
# 服务器1 (************)
node app.js --port 3000

# 服务器2 (************)  
node app.js --port 3000

# 服务器3 (************)
node app.js --port 3000
```

### 第二步：配置Nginx负载均衡

```bash
# 创建配置文件
sudo nano /etc/nginx/sites-available/loadbalancer

# 启用站点
sudo ln -s /etc/nginx/sites-available/loadbalancer /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启服务
sudo systemctl reload nginx
```

### 第三步：测试负载均衡

```bash
# 测试脚本
#!/bin/bash
for i in {1..20}; do
    echo "Request $i:"
    curl -s http://your-domain.com/ | grep "Server ID"
    sleep 1
done
```

## 10. 最佳实践总结

1. **选择合适的算法**：
    
    - 无状态应用：轮询或最少连接
    - 有状态应用：IP哈希
    - 性能差异大：加权轮询
2. **合理设置参数**：
    
    - `max_fails`：根据应用稳定性调整
    - `fail_timeout`：给服务器恢复时间
    - `weight`：根据服务器性能分配
3. **监控和日志**：
    
    - 定期检查服务器状态
    - 分析访问日志找出问题
    - 设置告警机制
4. **安全考虑**：
    
    - 隐藏后端服务器信息
    - 设置适当的访问控制
    - 定期更新和维护

负载均衡是提高系统可用性和性能的重要手段，通过合理配置可以让你的服务更加稳定和高效！