# 第3章 MongoDB原理和使用

## 一、单选题

1、下列关于MongoDB集合的描述中，哪一项是正确的（）？
A) 集合必须定义固定的字段结构
**B) 集合中的文档可以具有不同的字段结构**
C) 集合名称必须全大写
D) 集合不支持嵌套文档

2、MongoDB的默认端口号是（）？
A) 3306
**B) 27017**
C) 5432
D) 8080

3、下列关于YAML格式的描述中，哪一项是错误的（）？
A) YAML对大小写敏感
**B) 使用制表符进行缩进**
C) 字符串可以不使用引号标注
D) 相同层级的元素需要左对齐

4、MongoDB中用于更新单个文档的方法是（）？
A) update
**B) updateOne**
C) updateMany
D) replaceOne

5、MongoDB中的基本元素包括哪些？
A) 数据库、表、记录
**B) 数据库、集合、文档**
C) 数据库、文件、记录
D) 数据库、集合、行

6、MongoDB采用哪种格式进行存储和编码传输？
A) JSON
**B) BSON**
C) XML
D) YAML

7、BSON的主要优势之一是什么？
A) 更高的压缩率
B) 支持更多的数据类型
**C) 更快的解析速度**
D) 更小的文件大小

8、每个MongoDB文档必须拥有一个唯一的标识符，称为？
A) UUID
**B) ObjectID**
C) GUID
D) ID

9、在BSON中，时间戳通常用什么表示？
A) Date
B) Time
**C) Timestamp**
D) DateTime

10、以下哪个选项不是BSON支持的数据类型？
A) string
B) boolean
C) integer
**D) image**

11、在MongoDB中，下列哪个字段用于唯一标识文档？
**A) _id**
B) id
C) unique_id
D) key

12、MongoDB的配置文件通常使用哪种格式？
A) JSON
B) XML
**C) YAML**
D) CSV

13、以下哪项是YAML格式的基本规则之一？
A) YAML对大小写不敏感
B) YAML使用制表符进行缩进
C) YAML字符串需要用双引号包裹
**D) YAML通过缩进来表示层级关系**

14、在YAML格式中，以下哪种方式正确表示一个包含多个元素的数组（**A**）？
A)
`items:`
  - `item1`
  - `item2`
  - `item3`
B)
`items:`
  * `item1`
  * `item2`
  * `item3`
C)
`items:`
  `-> item1`
  `-> item2`
  `-> item3`
D)
`items:`
	  `item1,`
	  `item2,`
	  `item3`

15、在MongoDB中，如何插入一个新的文档到集合中？
**A) db.collection.insertOne()**
B) db.collection.add()
C) db.collection.create()
D) db.collection.put()

16、以下哪个选项是正确的插入多个文档的方法？
**A) db.collection.insertMany([])**
B) db.collection.insertMultiple([])
C) db.collection.bulkInsert([])
D) db.collection.addMany([])

17、在插入文档时，如果未指定_id字段，MongoDB会自动添加一个什么类型的值？
A) String
B) Integer
**C) ObjectID**
D) Date

18、以下哪个命令用于更新集合中的单个文档？
A) db.collection.update()
**B) db.collection.updateOne()**
C) db.collection.modifyOne()
D) db.collection.editOne()

19、在更新操作中，以下哪个操作符用于设置字段的新值？
**A) $set**
B) $add
C) $update
D) $modify

20、以下哪个选项可以用来更新多个匹配的文档？
**A) db.collection.updateMany()**
B) db.collection.updateAll()
C) db.collection.replaceMany()
D) db.collection.modifyAll()

21、以下哪个命令用于查询集合中的所有文档？
A) db.collection.findAll()
**B) db.collection.find({})**
C) db.collection.getAll()
D) db.collection.searchAll()

22、以下哪个操作符用于限制返回的文档数量？
**A) $limit**
B) $count
C) $size
D) $range

23、以下哪个操作符用于排序返回的文档？
A) $order
**B) $sort**
C) $arrange
D) $sequence

24、以下哪个命令用于替换集合中的一个文档？
A) db.collection.replace()
**B) db.collection.replaceOne()**
C) db.collection.swapOne()
D) db.collection.exchangeOne()

25、 在以下查询中，哪一个会返回所有 place.province 为 "Shandong" 且 place.city 为 "qingdao" 的文档？B
`A. db.fruitshop.find({"place":{"province":"Shandong","city":"qingdao"}})`
`B. db.fruitshop.find({"place.province":"Shandong", "place.city":"qingdao"})`
`C. db.fruitshop.find({"province":"Shandong", "city":"qingdao"})`
`D. db.fruitshop.find({"place":{"city":"qingdao","province":"Shandong"}})`

26、 如何将 fruitshop 集合中第一个匹配到的 pricelist 数组中值为 1.8 的元素更新为 2.0？**A**
`A. db.fruitshop.updateOne({"pricelist": 1.8}, {set: {"pricelist.": 2.0}})`
`B. db.fruitshop.updateMany({"pricelist": 1.8}, {set: {"pricelist.[]": 2.0}})`
`C. db.fruitshop.updateOne({"pricelist": 1.8}, {$set: {"pricelist": 2.0}})`
`D. db.fruitshop.updateMany({}, {set: {"pricelist.": 2.0}})`

27、 如何从 fruitshop 集合中删除所有 pricelist 数组中值大于 1.8 的条目？**A**
`A. db.fruitshop.updateMany({}, {pull: {"pricelist": {gt: 1.8}}})`
`B. db.fruitshop.updateMany({}, {pop: {"pricelist": {gt: 1.8}}})`
`C. db.fruitshop.updateMany({}, {pull: {"pricelist": 1.8}})`
`D. db.fruitshop.updateMany({}, {unset: {"pricelist": {gt: 1.8}}})`

28、如何向 fruitshop 集合中 name 为 "apple" 的文档的 pricelist 数组末尾添加 [2, 3]？**C**
`A. db.fruitshop.updateOne({"name": "apple"}, {$push: {"pricelist": [2, 3]}})`
`B. db.fruitshop.updateOne({"name": "apple"}, {$addToSet: {"pricelist": [2, 3]}})`
`C. db.fruitshop.updateOne({"name": "apple"}, {push: {"pricelist": {each: [2, 3]}}})`
`D. db.fruitshop.updateOne({"name": "apple"}, {$push: {"pricelist": 2, 3}})`

29、 为了提高对 place.province 字段的查询性能，应该使用哪种方式创建索引？***B***
`A. db.fruitshop.createIndex({"place": 1})`
`B. db.fruitshop.createIndex({"place.province": 1})`
`C. db.fruitshop.createIndex({"province": 1})`
`D. db.fruitshop.createIndex({"place.city": 1})`

30、使用MongoDB Compass连接到MongoDB服务实例时，以下哪种方式是正确的？
A. 只能连接到本地的MongoDB实例
**B. 可以连接到任何网络位置的MongoDB服务实例，包括MongoDB Atlas中的服务实例**
C. 只能通过命令行连接到MongoDB实例
D. 必须先在MongoDB中创建用户才能连接

31、在MongoDB Compass中，以下哪种方法可以用来导入数据？
A. 只能通过JSON格式导入
B. 只能通过CSV格式导入
**C. 可以通过JSON或CSV格式导入**
D. 无法直接导入数据，只能手动插入

32、以下哪个代码片段是正确的用于同步方式连接MongoDB服务器？
A. client = MongoClient("mongo://localhost:27017/")
B. client = MongoClient("localhost", "27017")
**C. client = MongoClient("mongodb://localhost:27017")**
D. client = MongoClient()

33、在MongoDB中，如何为location字段创建一个地理空间索引（2dsphere）？
A. db.places.createIndex({"location":"2d"})
**B. db.places.createIndex({"location":"2dsphere"})**
C. db.places.createIndex({"location":1})
D. db.places.createIndex({"coordinates":"2dsphere"})

34、要将集合fruitshop中所有name为apple的文档的qty字段增加5，应该使用以下哪个命令？**B**
`A. db.fruitshop.updateMany({"name":"apple"}, {$set: {"qty": 5}})`
`B. db.fruitshop.updateMany({"name":"apple"}, {$inc: {"qty": 5}})`
`C. db.fruitshop.updateOne({"name":"apple"}, {$inc: {"qty": 5}})`
`D. db.fruitshop.updateMany({"name":"apple"}, {$push: {"qty": 5}})`

35、GridFS文件系统主要用于：
A. 存储小于16MB的文件
**B. 存储大于16MB的文件**
C. 仅用于存储图片文件
D. 仅用于存储文本文件

36、如果需要查询pricelist数组中至少有一个元素大于1.8的文档，应使用以下哪个查询？  **B**

`A. db.fruitshop.find({"pricelist": {$gt: 1.8}})`

`B. db.fruitshop.find({"pricelist": {$elemMatch: {$gt: 1.8}}})`

`C. db.fruitshop.find({"pricelist.$": {$gt: 1.8}})`

`D. db.fruitshop.find({"pricelist.0": {$gt: 1.8}})`

37、要在MongoDB中对name和role字段进行全文检索，应使用以下哪个命令来创建全文索引？**A**
`A. db.avengers.createIndex({"name":"text", "role":"text"})`
`B. db.avengers.createIndex({"name":"text"})`
`C. db.avengers.createIndex({"role":"text"})`
`D. db.avengers.createIndex({"$**":"text"})`

38、批量更新文档
以下哪个命令能够批量更新集合中匹配条件的所有文档？**C**
`A. db.collection.update({"field":"value"}, {$set: {"newField":"newValue"}})`
`B. db.collection.updateOne({"field":"value"}, {$set: {"newField":"newValue"}})`
`C. db.collection.updateMany({"field":"value"}, {$set: {"newField":"newValue"}})`
`D. db.collection.replaceOne({"field":"value"}, {"newField":"newValue"})`

39、 MongoDB的异步访问模式依赖于哪些库？
A. Pymongo 和 Motor
**B. Asyncio 和 Motor**
C. PyMongo 和 Asyncio
D. Motor 和 Pymongo

## 二、多选题

1、MongoDB支持的索引类型包括（）？
**A) 单字段索引**
**B) 复合索引**
**C) 哈希索引**
**D) 全文索引**

2、MongoDB聚合操作中的管道操作符包括（）？
**A) $group**
**B) $match**
**C) $sort**
**D) $limit**

3、MongoDB中可以用于数组操作的操作符包括（）？
**A) $push**
**B) $pull**
**C) $each**
**D) $slice**

4、MongoDB Compass客户端支持的功能包括（）？
**A) 数据导入**
**B) 索引管理**
**C) 聚合操作**
**D) 数据导出**

## 三、填空题

1、MongoDB的默认数据存储路径是**/data/db** **。
2、BSON中用于表示日期时间的数据类型是 Date。
3、MongoDB中用于删除集合的命令是**db.collection.drop()**。
4、MongoDB聚合操作中用于计算平均值的运算符是  **$avg**。

## 四、判断题（正确打√，错误打×）

1、MongoDB的_id字段可以手动指定。（**√**）
2、MongoDB的全文索引不支持中文分词。（**√**） (注：早期版本可能不支持，但后续版本通过插件或内置支持增强了中文分词能力，此处答案根据题库原始答案)
3、MongoDB的$near操作符用于范围查询。（**×**）
4、MongoDB的GridFS用于存储小于16MB的文件。（**×**）

## 五、简答题

1、简述MongoDB中BSON格式的主要优势。
**答案：** **BSON格式通过二进制存储字段长度，提高检索速度；支持更多数据类型（如Date、Binary）；解析效率高。**

2、MongoDB的CAP理论在分布式系统中如何应用？
**答案：** **MongoDB采用AP原则（可用性和分区容错性优先），在分布式环境中通过副本集实现高可用性。**

3、解释MongoDB中复合索引的创建规则及其生效条件。
**答案：** **复合索引需按字段顺序匹配查询条件，排序方向需一致。例如，索引{name:1, tags:1}支持按name和tags排序，但不支持tags单独查询。**

4、描述MongoDB中$geoNear操作符的作用及使用场景。
==$geoNear用于按距离排序查询地理位置，适用于地图服务、位置推荐等场景==

## 六、综合题

1、结合电商场景，设计一个基于MongoDB的库存管理系统，说明其数据模型和核心操作。
**答案：** **数据模型：商品集合包含商品ID、名称、库存、价格等字段。操作包括库存增减（$inc）、分页查询（skip+limit）、聚合统计（$group）。**

2、分析MongoDB在物联网实时监控系统中的应用，并说明其技术选型依据。
**答案：** **使用文档型存储，每个传感器设备数据作为文档存储，结合时间序列查询和聚合操作，通过副本集保证高可用性。**

3、如何利用MongoDB的全文检索功能优化用户搜索体验？请设计实现方案。
**答案：** **为商品名称和描述字段建立全文索引，使用$text操作符进行模糊搜索，结合$caseSensitive控制大小写敏感。**

4、设计一个基于MongoDB的地理空间查询系统，用于查找附近的POI（兴趣点）。
**答案：** **使用GeoJSON存储POI坐标，创建2dsphere索引，通过$near查询附近地点，并结合$geoWithin限定范围。**