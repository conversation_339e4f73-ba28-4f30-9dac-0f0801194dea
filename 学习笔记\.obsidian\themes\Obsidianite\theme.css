/** ---------------------------------------------------------------
**  ██████╗ ██████╗ ███████╗██╗██████╗ ██╗ █████╗ ███╗   ██╗██╗████████╗███████╗
** ██╔═══██╗██╔══██╗██╔════╝██║██╔══██╗██║██╔══██╗████╗  ██║██║╚══██╔══╝██╔════╝
** ██║   ██║██████╔╝███████╗██║██║  ██║██║███████║██╔██╗ ██║██║   ██║   █████╗
** ██║   ██║██╔══██╗╚════██║██║██║  ██║██║██╔══██║██║╚██╗██║██║   ██║   ██╔══╝
** ╚██████╔╝██████╔╝███████║██║██████╔╝██║██║  ██║██║ ╚████║██║   ██║   ███████╗
**  ╚═════╝ ╚═════╝ ╚══════╝╚═╝╚═════╝ ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝╚═╝   ╚═╝   ╚══════╝
**  —— Version 2.1.0
**  —— Made with 💎 by <PERSON>
** Changes Log @see: https://github.com/bennyxguo/Obsidian-Obsidianite/blob/main/CHANGELOG.md
** --------------------------------------------------------------- */

/******************************************
   Currently only comes in dark theme,
   Light theme is still working in progress,
   but it wil come soon or later.
******************************************/

/* .theme-light {
  --background-primary: #fbfbfb;
  --background-primary-alt: #fbfbfb;
  --background-secondary: #fbfbfb;
  --background-secondary-alt: #2e3236;
  --text-normal: #333;
  --text-faint: #b2b2b2;
  --text-title-h1: #333;
  --text-title-h2: #333;
  --text-title-h3: #333;
  --text-title-h4: #333;
  --text-title-h5: #333;
  --text-link: #b4b4b4;
  --text-a: #db4d52;
  --text-a-hover: #db4d52;
  --text-mark: #d3ffa4;
  --pre-code: #ffffff;
  --interactive-accent: #92a1a1;
  --interactive-before: #5e6565;
  --background-modifier-border: #92a1a17a;
  --blockquote-border: #d6555f;
  --tag-background: rgba(14, 210, 247, 0.15);
  --interactive-accent-rgb: 61, 215, 251;
  --font-family-editor: Avenir, 'Avenir Next';
  --font-family-preview: Avenir, 'Avenir Next';
} */

.theme-dark {
  --background-primary: #100e17;
  --background-primary-alt: #0d0b12;
  --background-secondary: #191621;
  --background-secondary-alt: #0d0b12;
  --text-normal: #bebebe;
  --text-accent: #0fb6d6;
  --text-sub-accent: #f4569d;
  --text-dim: #45aaff;
  --text-faint: #7aa2f7;
  --text-title-h1: var(--text-accent);
  --text-title-h2: #cbdbe5;
  --text-title-h3: #cbdbe5;
  --text-title-h4: #cbdbe5;
  --text-title-h5: #cbdbe5;
  --text-link: #b4b4b4;
  --text-a: #6bcafb;
  --text-a-hover: #6bcafb;
  --text-mark: #263d92;
  --code-background: var(--background-secondary);
  --interactive-accent: rgba(14, 210, 247, 0.5);
  --interactive-accent-hover: rgba(14, 210, 247, 0.8);
  --interactive-before: #5e6565;
  --blockquote-border: #4aa8fb;
  --tag-background: rgba(14, 210, 247, 0.15);
  --interactive-accent-rgb: #3dd7fb;
  --font-family-editor: 'Rubik';
  --font-family-preview: 'Rubik';
  --bg-sub-accent-55: rgba(244, 86, 157, 0.55);
  --bg-accent-55: rgba(14, 210, 247, 0.55);
  --bg-accent-25: rgba(14, 210, 247, 0.25);
  --text-highlight-bg: rgba(244, 86, 157, 0.25);
  --background-modifier-border: rgba(14, 210, 247, 0.05);
  --table-border-color: rgb(14, 210, 247, 0.15);
  --test-color: rgb(122, 162, 247);
  --editor-border-color: #101014;
}

/******************************************
**   ██████╗██╗   ██╗███████╗████████╗ ██████╗ ███╗   ███╗██╗███████╗███████╗
**  ██╔════╝██║   ██║██╔════╝╚══██╔══╝██╔═══██╗████╗ ████║██║╚══███╔╝██╔════╝
** ██║     ██║   ██║███████╗   ██║   ██║   ██║██╔████╔██║██║  ███╔╝ █████╗
** ██║     ██║   ██║╚════██║   ██║   ██║   ██║██║╚██╔╝██║██║ ███╔╝  ██╔══╝
** ╚██████╗╚██████╔╝███████║   ██║   ╚██████╔╝██║ ╚═╝ ██║██║███████╗███████╗
**  ╚═════╝ ╚═════╝ ╚══════╝   ╚═╝    ╚═════╝ ╚═╝     ╚═╝╚═╝╚══════╝╚══════╝
** —— You can customized the theme using the variables below
******************************************/

:root {
  /***************************************/
  /*    FONTS RELATED                    */
  /***************************************/

  /** Font Customization **/
  --default-font: 'Rubik', 'Glow Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', sans-serif;

  --default-font-size: 18px;

  /** Main text font weight **/
  --body-font-weight: 450;

  /** Font family for hash-tags **/
  --tag-font-family: 'OperatorMonoSSmLig-Book', 'Glow Sans SC', '华文细黑', 'STXihei', 'PingFang TC',
    '微软雅黑体', 'Microsoft YaHei New', '微软雅黑', 'Microsoft Yahei', '宋体', 'SimSun',
    'Helvetica Neue', 'Helvetica', Arial, sans-serif !important;

  /** Font specially for codes **/
  --code-mono-font: 'OperatorMonoSSmLig-Book';
}

/**-------------------**
| CUSTOMIZED TAG COLOURS
**--------------------**/

/* For preview mode */
a.tag[href*='#todo'],
a.tag[href*='#待完成'] {
  background-color: #be2e5e;
  color: #fff;
}
/* For Editor Mode */
.cm-s-obsidian .CodeMirror-line span.cm-tag-todo:not(.cm-formatting-hashtag) {
  color: #ee6a96;
}

a.tag[href*='#working-draft'],
a.tag[href*='#进行中'] {
  background-color: #4d3ca6;
  color: #fff;
}

.cm-s-obsidian .CodeMirror-line span.cm-tag-working-draft:not(.cm-formatting-hashtag) {
  color: #a897ff;
}

a.tag[href*='#notes'],
a.tag[href*='#笔记'] {
  background-color: #17b978;
  color: #fff;
}

.cm-s-obsidian .CodeMirror-line span.cm-tag-notes:not(.cm-formatting-hashtag) {
  color: #45e0a2;
}

a.tag[href*='#knowledge'],
a.tag[href*='#知识'] {
  background-color: #005792;
  color: #fff;
}

.cm-s-obsidian .CodeMirror-line span.cm-tag-knowledge:not(.cm-formatting-hashtag) {
  color: #6cbdf3;
}

a.tag[href*='#writing'],
a.tag[href*='#文章'] {
  background-color: #f95959;
  color: #fff;
}

.cm-s-obsidian .CodeMirror-line span.cm-tag-writing:not(.cm-formatting-hashtag) {
  color: #ff7a7a;
}

a.tag[href*='#ideas'],
a.tag[href*='#想法'] {
  background-color: #ffc93c;
  color: #000;
}

.cm-s-obsidian .CodeMirror-line span.cm-tag-ideas:not(.cm-formatting-hashtag) {
  color: #ffdc82;
}

/**************************************
| GENERAL STYLES
| -------------------------------------
| Obsidian main controls related.
/**************************************/

::-webkit-scrollbar {
  opacity: 1;
  width: 0.6rem;
}

::-webkit-scrollbar-thumb {
  background-color: var(--background-modifier-border);
  border-radius: 999px;
}

::-webkit-scrollbar-track,
::-webkit-scrollbar-track-piece {
  background: var(--background-secondary);
}

.markdown-preview-view .internal-link.is-unresolved {
  opacity: 0.7;
}

.titlebar {
  background-color: var(--background-primary-alt);
}

.status-bar {
  background-color: var(--background-primary-alt);
  border-color: #101014;
  color: var(--text-faint);
  position: absolute;
  margin: auto;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  border-top-left-radius: 8px;
  padding: 8px 6px 6px 10px;
  max-height: unset;

  opacity: 0.4;
  transition: 0.5s;
}

.status-bar:hover {
  opacity: 1;
  transition: 0.2s;
}

.titlebar-text {
  color: var(--text-accent);
}

.theme-dark .notice {
  background-color: var(--background-primary-alt);
  color: var(--text-accent);
}

/** NAVIGATION / SIDE-DOCK **/
.nav-file-title,
.nav-folder-title {
  border: 1px solid transparent;
}

.nav-folder-collapse-indicator {
  color: var(--text-accent);
}
.nav-file.is-active > .nav-file-title,
.nav-file.is-active > .nav-folder-title,
.nav-file.is-active > .nav-folder-collapse-indicator,
.nav-folder.is-active > .nav-file-title,
.nav-folder.is-active > .nav-folder-title,
.nav-folder.is-active > .nav-folder-collapse-indicator {
  color: var(--text-accent);
}

/** Side-dock icons **/
.side-dock-ribbon-tab,
.side-dock-ribbon-action,
.workspace-ribbon-collapse-btn,
.workspace-tab-header {
  transition: all 350ms ease-in-out;
  opacity: 0.5;
  cursor: pointer;
}
.side-dock-ribbon-tab:hover,
.side-dock-ribbon-action:hover,
.workspace-ribbon-collapse-btn:hover,
.workspace-tab-header:hover {
  opacity: 1;
  color: var(--text-accent);
}

/** Left side menu -- collapsed **/
.workspace-ribbon.is-collapsed {
  background-color: var(--background-primary-alt);
}
/** Left side menu border **/
.workspace-ribbon.mod-left.is-collapsed,
.workspace-ribbon.mod-right.is-collapsed {
  border-color: var(--editor-border-color);
}

/** Navigation tabs **/
.is-focused .workspace-tab-header.is-active {
  opacity: 1;
}

.is-focused .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-icon,
.is-focused .mod-active .workspace-tab-header.is-active .workspace-tab-header-inner-title {
  background: var(--background-modifier-border);
  color: var(--text-accent);
}

.nav-file-title.is-active,
.nav-folder-title.is-active {
  background: var(--background-secondary);
  border: 1px solid var(--bg-accent-25);
  color: var(--text-accent);
}

body:not(.is-grabbing) .nav-file-title:hover,
body:not(.is-grabbing) .nav-folder-title:hover {
  background: var(--background-modifier-border);
  border: 1px solid var(--interactive-accent-hover);
}

/** Folder Lists **/
body:not(.is-grabbing) .nav-file-title:hover,
body:not(.is-grabbing) .nav-folder-title:hover {
  color: var(--text-accent);
}
/** Folder list active arrow **/
body:not(.is-grabbing) .nav-file-title:hover .nav-folder-collapse-indicator,
body:not(.is-grabbing) .nav-folder-title:hover .nav-folder-collapse-indicator {
  color: var(--text-accent);
}
/** Folder List main title **/
.nav-folder.mod-root > .nav-file-title,
.nav-folder.mod-root > .nav-folder-title {
  color: var(--text-accent);
}

/** View Header title **/
.workspace-leaf.mod-active .view-header-title {
  color: var(--text-accent);
}

/** Little tag styles (at links) **/
.pane-list-item:hover .pane-list-item-ending-flair {
  background-color: var(--background-secondary);
  color: var(--text-accent);
}

/** SETTINGS STYLES **/
.checkbox-container.is-enabled {
  background-color: rgba(14, 210, 247, 0.7);
}

.workspace-drop-overlay {
  background-color: var(--background-primary-alt);
}

/* except list markers */
span.cm-formatting-list,
/*code block backticks */ span.cm-formatting-code-block.cm-hmd-codeblock,
/* optionally header hashes */ span.cm-formatting-header {
  display: inline !important;
}

/***************************************/
/*    EDITOR GENERAL STYLES            */
/***************************************/

.markdown-source-view.mod-cm6 .task-list-label {
  position: relative;
  top: -2px;
  padding-top: 0;
}

/* OBSIDIANITE CHECKBOXES */

.contains-task-list .task-list-item .contains-task-list {
  padding-inline-start: 5px;
}

.contains-task-list .task-list-item ul.contains-task-list::before {
  left: -46px !important;
  border-width: 2px;
}

.contains-task-list .task-list-item {
  position: relative;
  vertical-align: middle;
  box-sizing: border-box;
  padding-left: 30px;
}

.markdown-preview-view ul > li.task-list-item {
  text-indent: -3em;
}

.contains-task-list .task-list-item input[type='checkbox'] {
  position: relative;
  top: 2px;
  left: -8px;
  width: 20px;
  height: 20px;
  margin: 0;
  opacity: 0;
  z-index: 10;
}

.markdown-preview-view .list-collapse-indicator {
  margin-left: -80px !important;
  box-sizing: border-box;
}

.contains-task-list .task-list-item::before {
  content: '';
  position: absolute;
  top: 2px;
  left: -25px;
  width: 20px;
  height: 20px;
  display: inline-flex;
  justify-content: center;
  border: 2px solid #9e9e9e;
  margin-right: 15px;
  border-radius: 3px;
  transition: all 0.3s;
  box-sizing: border-box;
  z-index: 1;
}

.contains-task-list .is-checked.task-list-item::before {
  border: 10px solid var(--text-accent);
  animation: bounce 300ms;
}

.contains-task-list .is-checked.task-list-item::after {
  content: '';
  position: absolute;
  top: 8px;
  left: -21px;
  border-right: 3px solid transparent;
  border-bottom: 3px solid transparent;
  transform: rotate(45deg);
  transform-origin: 0% 100%;
  animation: checked-box 125ms 250ms forwards;
  z-index: 5;
}

.markdown-preview-view ul > li.task-list-item.is-checked {
  text-decoration: line-through !important;
  color: var(--interactive-accent) !important;
}

.markdown-preview-view ul > li.task-list-item.is-checked ul > li:not(.is-checked) {
  text-decoration: none !important;
  color: var(--text-normal) !important;
}

@keyframes checked-box {
  0% {
    width: 0;
    height: 0;
    border-color: #212121;
    transform: translate(0, 0) rotate(45deg);
  }
  33% {
    width: 4px;
    height: 0;
    border-color: #212121;
    transform: translate(0, 0) rotate(45deg);
  }
  100% {
    width: 4px;
    height: 8px;
    border-color: #212121;
    transform: translate(0, -8px) rotate(45deg);
  }
}

@keyframes bounce {
  0% {
    border-width: 2px;
    /* transform: scale(1); */
  }
  33% {
    border-width: 4px;
    /* transform: scale(0.7); */
  }
  100% {
    border-width: 10px;
    /* transform: scale(1); */
  }
}

/**************************************
| MAIN EDITOR / PREVIEW MODE
| -------------------------------------
| Write in the same section to keep them
| relatively sync with css.
/**************************************/

/**-------------------**
| HEADING STYLES
**--------------------**/
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--default-font);
  font-weight: 600;
}

.HyperMD-header.HyperMD-header-2,
.HyperMD-header.HyperMD-header-3,
.HyperMD-header.HyperMD-header-4,
.HyperMD-header.HyperMD-header-5,
.HyperMD-header.HyperMD-header-6 {
  border-bottom: 1px solid;
  border-width: 1px;
  border-image-slice: 1;
  border-image-source: linear-gradient(to right, var(--text-sub-accent), #100e17, #100e17, #100e17);
}

.HyperMD-header {
  padding: 20px;
}

.markdown-preview-section h1,
.cm-header-1 {
  font-size: 34px;
  color: var(--text-title-h1);
}

.markdown-preview-section h2,
.cm-header-2 {
  font-size: 26px;
  color: var(--text-title-h2);
}

.markdown-preview-section h3,
.cm-header-3 {
  font-size: 22px;
  color: var(--text-title-h3);
}

.markdown-preview-section h4,
.cm-header-4 {
  font-size: 18px;
  color: var(--text-title-h4);
}

.markdown-preview-section h5,
.cm-header-5 {
  font-size: 18px;
  color: var(--text-title-h5);
}

.markdown-preview-section h6,
.cm-header-6 {
  font-size: 18px;
  color: var(--text-title-h5);
}

/** headings */
.markdown-preview-view h2,
.markdown-preview-view h3,
.markdown-preview-view h4,
.markdown-preview-view h5,
.markdown-preview-view h6 {
  /* padding-top: 1.25rem; */
  margin: 45px 0 20px 0;
  position: relative;
  padding-bottom: 10px;
  border-bottom: 1px solid;
  border-width: 35%;
  border-image-slice: 1;
  border-image-source: linear-gradient(to right, var(--text-sub-accent), #100e17, #100e17, #100e17);
}

.view-header-icon {
  color: var(--text-accent);
}

/** Adding different # colors **/

.cm-formatting.cm-formatting-header.cm-header {
  color: var(--text-accent);
  opacity: 0.45;
}

/**-------------------**
| HR STYLES
**--------------------**/

/** hr styles -- PREVIEW MODE */
.cm-line hr,
.markdown-preview-view hr {
  margin-block-start: 4em;
  margin-block-end: 4em;
  border: none;
  height: 0;
  border-bottom: 1px solid;
  border-image-slice: 1;
  border-width: 1px;
  border-image-source: linear-gradient(to right, transparent, var(--text-accent), transparent);
}

.cm-line hr::after,
.markdown-preview-view hr::after {
  content: '§';
  display: inline-block;
  position: absolute;
  left: 50%;
  transform: translate(-50%, -50%) rotate(60deg);
  transform-origin: 50% 50%;
  padding: 0.5rem;
  color: var(--text-sub-accent);
  background-color: var(--background-primary);
}

/**-------------------**
| STRONG/BOLD STYLES
**--------------------**/

.cm-strong,
strong {
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  padding: 0;
  color: #7aa2f7;
  background-color: #7aa2f7;
  background-image: linear-gradient(62deg, #87c2fd 0%, #dcb9fc 100%) !important;
}

strong .math.math-inline .MathJax {
  position: inherit !important;
}

.cm-strong .cm-selection,
strong::selection {
  -webkit-text-fill-color: var(--text-faint);
}

/**-------------------**
| <KBD> STYLING
**--------------------**/

.cm-strong kbd,
strong kbd {
  -webkit-text-fill-color: initial;
}

/**-------------------**
| ITALIC STYLES
**--------------------**/

.cm-em,
em {
  color: #bb9af7 !important;
  font-family: OperatorMonoSSmLig-Book, Rubik, var(--default-font) !important;
}

.cm-em.cm-formatting-em {
  display: inline-flex;
  width: 0.45rem;
  font-size: 0.6rem;
  vertical-align: text-top;
}

/**-------------------**
| LISTING STYLES (ul, li, ol)
**--------------------**/

.cm-s-obsidian span.cm-formatting-list {
  color: var(--text-accent);
}

.markdown-source-view.mod-cm6 .cm-indent::before {
  border-width: 2px;
  border-color: var(--text-accent);
  margin-left: -1px;
  opacity: 0.35;
  transition: opacity 500ms linear ease-in-out;
}

.markdown-source-view.mod-cm6 .cm-active-indent::before {
  opacity: 0.8;
}

/**-------------------**
| LINKS STYLING
**--------------------**/

/** editor mode **/
.cm-s-obsidian span.cm-link:not(.cm-formatting-link) .cm-underline,
.cm-s-obsidian span.cm-hmd-internal-link .cm-underline {
  color: var(--text-normal);
  -webkit-text-fill-color: var(--text-normal);
  background-position: 0 100%;
  background-repeat: repeat-x;
  background-size: 5px 5px;
  text-decoration: none;
}

cm-s-obsidian span.cm-link:not(.cm-formatting-link) .cm-underline {
  background-image: linear-gradient(
    to bottom,
    var(--bg-sub-accent-55) 0%,
    var(--bg-sub-accent-55) 100%
  );
}

.cm-s-obsidian span.cm-formatting-link {
  color: var(--text-faint) !important;
  opacity: 0.25;
}

/** preview mode **/
.external-link {
  padding: 0;
}

.internal-link,
.external-link {
  color: var(--text-normal);
  background-position: 0 100%;
  background-repeat: repeat-x;
  background-size: 5px 5px;
  text-decoration: none;
  transition: background 350ms ease-in-out;
}

.cm-s-obsidian span.cm-link:not(.cm-formatting-link) .cm-underline,
.external-link {
  background-image: linear-gradient(
    to bottom,
    var(--bg-sub-accent-55) 0%,
    var(--bg-sub-accent-55) 100%
  );
  transition: background 350ms ease-in-out;
}

.cm-s-obsidian span.cm-hmd-internal-link .cm-underline,
.internal-link {
  background-image: linear-gradient(to bottom, var(--bg-accent-55) 0%, var(--bg-accent-55) 100%);
  transition: background 350ms ease-in-out;
}

.internal-link:hover,
.cm-s-obsidian span.cm-hmd-internal-link .cm-underline:hover {
  -webkit-text-fill-color: #fff;
  color: #fff;
  background-size: 4px 50px;
  text-decoration-line: none !important;
}

.cm-s-obsidian span.cm-link:not(.cm-formatting-link) .cm-underline:hover,
.external-link:hover {
  -webkit-text-fill-color: #fff;
  color: #fff;
  background-size: 4px 50px;
  text-decoration-line: none !important;
}

/* link */
a,
.internal-link,
.cm-hmd-internal-link,
.cm-link {
  text-decoration: none !important;
  color: var(--text-normal);
  position: relative;
  z-index: 1;
}

.cm-url {
  color: var(--text-faint) !important;
  opacity: 0.4;
  font-weight: normal;
}

.cm-formatting-image {
  color: var(--text-accent) !important;
  opacity: 0.7;
}

/* link hover color */
a:hover,
.internal-link:hover {
  text-decoration: none !important;
  color: var(--text-accent);
}

/**-------------------**
| TAG STYLING
**--------------------**/

.cm-s-obsidian span.cm-hashtag {
  position: relative;
  color: var(--text-accent);
  opacity: 1;
  font-family: var(--tag-font-family);
  font-style: italic;
  text-decoration: none;
  font-size: 0.86rem;
  font-weight: 500;
}

.cm-s-obsidian span.cm-hashtag:hover {
  text-decoration-line: underline;
  text-decoration-color: var(--text-accent);
}

.cm-s-obsidian span.cm-formatting-hashtag {
  color: var(--text-faint);
}

/** tags */
a.tag {
  background-color: var(--tag-background);
  color: var(--text-accent);
  white-space: nowrap;
  border-radius: var(--tag-radius);
  padding: var(--tag-padding-y) var(--tag-padding-x);
  font-family: var(--tag-font-family);
  text-decoration: none;
  font-style: italic;
  font-size: 0.86rem;
  font-weight: 500;
}

a.tag:hover {
  border-color: var(--text-accent);
  opacity: 1;
  background-color: var(--tag-background) !important;
  text-decoration: underline !important;
  text-decoration-color: var(--text-accent);
}

h1 a.tag,
h2 a.tag,
h3 a.tag,
h4 a.tag,
h5 a.tag,
h6 a.tag {
  font-size: inherit !important;
}

/**-------------------**
| INLINE CODE STYLING
**--------------------**/

.CodeMirror-activeline
  span.cm-inline-code:not(.cm-formatting):not(.cm-hmd-indented-code):not(
    .obsidian-search-match-highlight
  ) {
  padding: 0 !important;
  margin: 0 !important;
}

.cm-s-obsidian span.cm-inline-code {
  color: rgba(14, 210, 247, 0.9) !important;
}

.cm-s-obsidian span.cm-inline-code.cm-formatting-code {
  display: inline-flex;
  min-width: 0.55rem;
}

/**-------------------**
| HIGHLIGHTING STYLES
**--------------------**/

.cm-s-obsidian span.cm-formatting-highlight,
.cm-s-obsidian span.cm-highlight {
  background-image: none !important;
  background-color: var(--text-highlight-bg);
  color: #fff;
  -webkit-text-fill-color: #fff;
  -webkit-background-clip: initial;
}

/**-------------------**
| CODE FENCE STYLING
**--------------------**/

.cm-s-obsidian span.code-block-flair:not(:empty) {
  color: var(--text-accent);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.6rem;
  background: rgba(16, 14, 23, 0.86);
  border-radius: 8px;
  padding: 0.1em 0.8em;
  margin-top: 0.3rem;
  margin-right: 0.3rem;
}

.cm-s-obsidian pre.HyperMD-codeblock {
  color: var(--text-sub-accent);
}

.cm-s-obsidian div.HyperMD-codeblock-bg {
  background-color: #191621;
}

.cm-s-obsidian div.HyperMD-codeblock-begin-bg {
  border-top-left-radius: 0.3em;
  border-top-right-radius: 0.3em;
  border: none;
}

.cm-s-obsidian div.HyperMD-codeblock-begin-bg:before {
  position: absolute;
  content: '';
  top: -5%;
  left: 0;
  width: 100%;
  height: 60%;
  margin-top: 1.5em;
  border-top-left-radius: 0.3em;
  border-top-right-radius: 0.3em;
  background-color: #191621;
}

.cm-s-obsidian div.HyperMD-codeblock-end-bg {
  border-bottom-left-radius: 0.3em;
  border-bottom-right-radius: 0.3em;
  border: none;
}

pre {
  position: relative;
}

pre[class*='language-']:before {
  font-family: Rubik, Lato, Lucida Grande, Lucida Sans Unicode, Tahoma, Sans-Serif;
  font-style: normal;
  font-weight: 700;
  font-size: 0.6rem;
  color: #fff;
  position: absolute;
  top: 0.3rem;
  right: 0.3rem;
  padding: 0;
  color: var(--text-accent) !important;
  content: '';
  text-transform: uppercase;
  background: rgba(16, 14, 23, 0.86);
  border-radius: 8px;
  padding: 0.1em 0.8em;
  z-index: 10;
}

pre[class$='javascript']:before,
pre[class$='js']:before {
  content: 'JavaScript';
}

pre[class$='typescript']:before,
pre[class$='ts']:before {
  content: 'typescript';
}

pre[class$='html']:before {
  content: 'html';
}

pre[class$='css']:before {
  content: 'css';
}

pre[class$='ejs']:before {
  content: 'ejs';
}

pre[class$='vue']:before {
  content: 'vue';
}

pre[class$='react']:before {
  content: 'react';
}

pre[class$='shell']:before {
  content: 'Shell';
}

pre[class$='powershell']:before {
  content: 'PowerShell';
}

pre[class$='json']:before {
  content: 'Json';
}

pre[class$='python']:before {
  content: 'python';
}

/**-------------------**
| BLOCKQUOTE STYLING
**--------------------**/

/* Remove blockquote left margin */
blockquote {
  margin-inline-start: 0;
}

/* blockquote style overwrite */

.markdown-source-view.mod-cm6.is-live-preview .HyperMD-quote,
.markdown-preview-view blockquote {
  position: relative;
  padding: 1rem 2rem 1rem 3rem;
  color: #bdbdbd;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  margin-bottom: 2em;
  margin-right: 0 !important;
  border-left: 3px rgba(14, 210, 247, 0.5) solid;
  border-top: transparent;
  border-bottom: transparent;
  border-right: transparent;
  background: linear-gradient(135deg, rgba(32, 28, 41, 0.45), #100e17);
}

.markdown-source-view.mod-cm6.is-live-preview .HyperMD-quote::before,
.markdown-preview-view blockquote::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0px;
  height: 2px;
  width: 60%;
  background: linear-gradient(90deg, rgba(13, 185, 215, 0.5), #13111a);
}

.markdown-source-view.mod-cm6.is-live-preview .HyperMD-quote + .HyperMD-quote::before {
  display: none;
}

.HyperMD-quote::after,
.markdown-preview-view blockquote::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0px;
  height: 2px;
  width: 25%;
  background: linear-gradient(90deg, rgba(13, 185, 215, 0.5), #15131c);
}

.HyperMD-quote.HyperMD-quote-2::before,
.HyperMD-quote.HyperMD-quote-3::before,
.HyperMD-quote.HyperMD-quote-4::before,
.HyperMD-quote.HyperMD-quote-5::before {
  height: 0;
}

.markdown-preview-view blockquote p {
  position: relative;
}

.markdown-preview-view blockquote p:first-of-type::before {
  content: '!!';
  font-style: italic;
  font-weight: 700;
  font-size: 18px;
  color: var(--text-accent);
  position: absolute;
  top: 0.1rem;
  left: -1.8rem;
}

/**-------------------**
| FRONT-META STYLING
**--------------------**/

.cm-s-obsidian span.cm-def,
.cm-s-obsidian span.cm-atom {
  color: var(--text-faint);
}
.cm-s-obsidian span.cm-meta {
  color: var(--text-accent);
}

.cm-s-obsidian span.cm-string {
  color: var(--text-sub-accent);
}

/**-------------------**
| CODE STYLING
**--------------------**/

/** inline code */
.cm-s-obsidian
  span.cm-inline-code:not(.cm-formatting):not(.cm-hmd-indented-code):not(
    .obsidian-search-match-highlight
  ),
.markdown-preview-view code {
  overflow-wrap: break-word;
  background-color: rgba(14, 210, 247, 0.05);
  word-wrap: break-word;
  padding: 0 5px;
  border-radius: 0.3rem;
  color: rgba(14, 210, 247, 0.9) !important;
}

.theme-dark :not(pre) > code[class*='language-'],
.theme-dark pre[class*='language-'] {
  background-color: var(--background-secondary);
}

.markdown-preview-view img {
  border-radius: 8px;
}

/** code syntax theme **/

.theme-dark .cm-s-obsidian pre.HyperMD-codeblock span.cm-comment {
  color: #6272a4;
}
.theme-dark .cm-s-obsidian pre.HyperMD-codeblock span.cm-string,
.theme-dark .cm-s-obsidian pre.HyperMD-codeblock span.cm-string-2 {
  color: #f1fa8c;
}
.theme-dark .cm-s-obsidian pre.HyperMD-codeblock span.cm-number {
  color: #bd93f9;
}
.theme-dark .cm-s-obsidian pre.HyperMD-codeblock span.cm-variable {
  color: #50fa7b;
}
.theme-dark .cm-s-obsidian pre.HyperMD-codeblock span.cm-variable-2 {
  color: white;
}
.theme-dark .cm-s-obsidian pre.HyperMD-codeblock span.cm-def {
  color: #50fa7b;
}
.theme-dark .cm-s-obsidian pre.HyperMD-codeblock span.cm-operator {
  color: #ff79c6;
}
.theme-dark .cm-s-obsidian pre.HyperMD-codeblock span.cm-keyword {
  color: #ff79c6;
}
.theme-dark .cm-s-obsidian pre.HyperMD-codeblock span.cm-atom {
  color: #bd93f9;
}
.theme-dark .cm-s-obsidian pre.HyperMD-codeblock span.cm-meta {
  color: #f8f8f2;
}
.theme-dark .cm-s-obsidian pre.HyperMD-codeblock span.cm-tag {
  color: #ff79c6;
}
.theme-dark .cm-s-obsidian pre.HyperMD-codeblock span.cm-attribute {
  color: #50fa7b;
}
.theme-dark .cm-s-obsidian pre.HyperMD-codeblock span.cm-qualifier {
  color: #50fa7b;
}
.theme-dark .cm-s-obsidian pre.HyperMD-codeblock span.cm-property {
  color: #66d9ef;
}
.theme-dark .cm-s-obsidian pre.HyperMD-codeblock span.cm-builtin {
  color: #50fa7b;
}
.theme-dark .cm-s-obsidian pre.HyperMD-codeblock span.cm-variable-3,
.theme-dark .cm-s-obsidian pre.HyperMD-codeblock span.cm-type {
  color: #ffb86c;
}

/** prism.js dracular theme **/

/*
* Dracula Theme for Prism.JS
*
* <AUTHOR> Costa
* e-mail: <EMAIL>
*
* @contributor Jon Leopard
* e-mail: <EMAIL>
*
* @license MIT 2016-2018
*/

pre::-webkit-scrollbar {
  width: 14px;
}

code[class*='language-'],
pre[class*='language-'] {
  color: #ccc;
  background: rgb(40, 41, 54);
  text-shadow: none;
  font-family: 'OperatorMonoSSmLig-Book', PT Mono, Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono',
    monospace !important;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;

  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;

  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}

pre[class*='language-']::-moz-selection,
pre[class*='language-'] ::-moz-selection,
code[class*='language-']::-moz-selection,
code[class*='language-'] ::-moz-selection {
  text-shadow: none;
  background-color: #5a5f80;
}

pre[class*='language-']::selection,
pre[class*='language-'] ::selection,
code[class*='language-']::selection,
code[class*='language-'] ::selection {
  text-shadow: none;
  background-color: #5a5f80;
}

@media print {
  code[class*='language-'],
  pre[class*='language-'] {
    text-shadow: none;
  }
}

/* Code blocks */
:not(pre) > code[class*='language-'],
pre[class*='language-'] {
  background: rgba(40, 41, 54, 1);
}

/* Inline code */
:not(pre) > code[class*='language-'] {
  padding: 4px 7px;
  border-radius: 0.3em;
  white-space: normal;
}

.limit-300 {
  height: 300px !important;
}

.limit-400 {
  height: 400px !important;
}

.limit-500 {
  height: 500px !important;
}

.limit-600 {
  height: 600px !important;
}

.limit-700 {
  height: 700px !important;
}

.limit-800 {
  height: 800px !important;
}

.theme-dark .token.comment {
  color: rgba(98, 114, 164, 1);
}

.theme-dark .token.prolog {
  color: rgba(207, 207, 194, 1);
}

.theme-dark .token.tag {
  color: rgba(220, 104, 170, 1);
}

.theme-dark .token.entity {
  color: rgba(139, 233, 253, 1);
}

.theme-dark .token.atrule {
  color: rgba(98, 239, 117, 1);
}

.theme-dark .token.url {
  color: rgba(102, 217, 239, 1);
}

.theme-dark .token.selector {
  color: rgba(207, 207, 194, 1);
}

.theme-dark .token.string {
  color: rgba(241, 250, 140, 1);
}

.theme-dark .token.property {
  color: rgba(255, 184, 108, 1);
}

.theme-dark .token.important {
  color: rgba(255, 121, 198, 1);
  font-weight: bold;
}

.theme-dark .token.punctuation {
  color: white;
}

.theme-dark .token.number {
  color: rgba(189, 147, 249, 1);
}

.theme-dark .token.function {
  color: rgba(80, 250, 123, 1);
}

.theme-dark .token.class-name {
  color: rgba(255, 184, 108, 1);
}

.theme-dark .token.keyword {
  color: rgba(255, 121, 198, 1);
}

.theme-dark .token.boolean {
  color: rgba(255, 184, 108, 1);
}

.theme-dark .token.operator {
  color: rgba(139, 233, 253, 1);
}

.theme-dark .token.char {
  color: rgba(255, 135, 157, 1);
}

.theme-dark .token.regex {
  color: rgba(80, 250, 123, 1);
}

.theme-dark .token.variable {
  color: rgba(80, 250, 123, 1);
}

.theme-dark .token.constant {
  color: rgba(255, 184, 108, 1);
}

.theme-dark .token.symbol {
  color: rgba(255, 184, 108, 1);
}

.theme-dark .token.builtin {
  color: rgba(255, 121, 198, 1);
}

.theme-dark .token.attr-value {
  color: #7ec699;
}

.theme-dark .token.deleted {
  color: #e2777a;
}

.theme-dark .token.namespace {
  color: #e2777a;
}

.theme-dark .token.bold {
  font-weight: bold;
}

.theme-dark .token.italic {
  font-style: italic;
}

.theme-dark .token {
  color: #ff79c6;
}

.language-cpp .theme-dark .token.string {
  color: #8be9fd;
}

.language-c .theme-dark .token.string {
  color: #8be9fd;
}

.theme-dark .language-css .token.selector {
  color: rgba(80, 250, 123, 1);
}

.theme-dark .language-css .token.property {
  color: rgba(255, 184, 108, 1);
}

.language-java span.theme-dark .token.class-name {
  color: #8be9fd;
}

.language-java .theme-dark .token.class-name {
  color: #8be9fd;
}

.language-markup .theme-dark .token.attr-value {
  color: rgba(102, 217, 239, 1);
}

.language-markup .theme-dark .token.tag {
  color: rgba(80, 250, 123, 1);
}

.language-objectivec .theme-dark .token.property {
  color: #66d9ef;
}

.language-objectivec .theme-dark .token.string {
  color: #50fa7b;
}

.language-php .theme-dark .token.boolean {
  color: #8be9fd;
}

.language-php .theme-dark .token.function {
  color: #ff79c6;
}

.language-php .theme-dark .token.keyword {
  color: #66d9ef;
}

.language-ruby .theme-dark .token.symbol {
  color: #8be9fd;
}

.language-ruby .theme-dark .token.class-name {
  color: #cfcfc2;
}

pre.line-numbers {
  position: relative;
  padding-left: 3.8em;
  counter-reset: linenumber;
}

pre.line-numbers > code {
  position: relative;
  white-space: inherit;
}

.line-numbers .line-numbers-rows {
  position: absolute;
  pointer-events: none;
  top: 0;
  font-size: 100%;
  left: -3.8em;
  width: 3em; /* works for line-numbers below 1000 lines */
  letter-spacing: -1px;
  border-right: 1px solid #999;

  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.line-numbers-rows > span {
  pointer-events: none;
  display: block;
  counter-increment: linenumber;
}

.line-numbers-rows > span:before {
  content: counter(linenumber);
  color: #999;
  display: block;
  padding-right: 0.8em;
  text-align: right;
}
