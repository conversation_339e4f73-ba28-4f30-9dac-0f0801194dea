# 第2章 NoSQL数据库的基本原理

## 一、单选题

1、下列关于关系型数据库与非关系型数据库的描述中，哪一项是正确的（）？
A) 关系型数据库采用键值对存储模型
B) 非关系型数据库严格遵循ACID特性
C) 关系型数据库支持分布式部署且无需牺牲一致性
**D) 非关系型数据库通常不支持固定表结构**

2、关系模型中，实体标识符的作用是（）？
A) 描述实体的属性
**B) 唯一标识一个实体**
C) 定义实体之间的联系
D) 确定实体的分类

3、CAP理论中，以下哪一项描述是错误的（）？
A) 一致性（Consistency）要求所有节点数据相同
B) 可用性（Availability）要求系统始终响应请求
C) 分区容错性（Partition Tolerance）要求系统在部分节点故障时仍可用
**D) CAP理论要求分布式系统必须同时满足C、A、P**

4、下列关于键值对存储模式的描述中，哪一项是正确的（）？
A) 键值对存储模式支持复杂的关联查询
B) 键值对存储模式的值可以是任意结构化数据
**C) 键值对存储模式通常基于哈希分片**
D) 键值对存储模式需要定义固定的列名和值域

5、CAP理论中的C代表什么？
A) 可用性
**B) 一致性**
C) 分区容忍性
D) 基本可用性

6、BASE理论中的S代表什么？
A) 强一致性
**B) 软状态**
C) 最终一致性
D) 高可用性

7、下列哪个不是关系型数据库事务的隔离级别？
A) 读未提交
B) 读已提交
C) 可重复读
**D) 最终一致性**

8、在两段锁协议中，扩展阶段指的是？
A) 数据删除阶段
B) 数据插入阶段
**C) 获取锁的过程**
D) 数据更新阶段

9、下列哪种NoSQL数据库适合用于处理社交网络分析？
A) 键值对数据库
B) 文档数据库
**C) 图数据库**
D) 列族数据库

10、NoSQL数据库通常不支持以下哪种特性？
A) 弱化模式或表结构
**B) 支持复杂的SQL查询**
C) 快速检索和读写能力
D) 分布式部署

11、下列哪个选项是NoSQL数据库的特点？
A) 强调事务处理能力
**B) 弱化完整性约束**
C) 不支持非结构化数据
D) 需要预定义表结构

12、在CAP理论中，如果选择了强一致性和分区容忍性，则必须放弃什么？
A) 一致性
**B) 可用性**
C) 分区容忍性
D) 最终一致性

13、Paxos算法主要用于解决什么问题？
A) 数据加密
**B) 分布式共识**
C) 数据压缩
D) 数据备份

14、下列哪种NoSQL数据库使用了键值对存储模式？
**A) Redis**
B) MongoDB
C) Cassandra
D) Neo4j

15、在分布式系统中，当主节点出现故障时，常用的恢复方法是什么？
A) 主从复制
B) 数据冗余
**C) 自动选举新的主节点**
D) 手动恢复

## 二、多选题

1、关系型数据库的完整性约束包括（）？
**A) 域完整性**
**B) 实体完整性**
**C) 参照完整性**
**D) 用户定义完整性**

2、分布式系统的一致性问题可能涉及以下哪些因素（）？
**A) 网络延迟**
**B) 节点故障**
C) 数据分片策略
**D) 多副本同步**

3、NoSQL的常见存储模式包括（）？
**A) 键值对存储**
**B) 文档式存储**
**C) 列存储**
**D) 图存储**

4、布隆过滤器的特点包括（）？
**A) 存储空间占用低**
B) 支持数据删除操作
**C) 存在一定的误报率**
**D) 检索速度快**

## 三、填空题

1、CAP理论中的三个特性分别是**一致性**、**可用性**和**分区容错性**。
2、文档式存储模式中，数据通常以**JSON**格式描述。
3、分布式大数据处理的核心原则是**计算本地化**。
4、Paxos算法主要用于解决**分布式共识**问题。

## 四、判断题（正确打√，错误打×）

1、关系型数据库的事务机制完全适用于分布式环境。（**×**）
2、非关系型数据库支持强一致性。（**×**）
3、布隆过滤器可以精确判断元素是否存在集合中。（**×**）
4、CAP理论要求分布式系统必须牺牲一致性以保证可用性。（**×**）

## 五、简答题

1、简述关系型数据库在分布式部署时遇到的主要瓶颈。
**答案：** **关系型数据库在分布式部署时，难以保证强一致性与高可用性，事务处理复杂且性能下降。**

2、简要说明CAP理论与BASE理论的核心区别。
**答案：** **CAP理论强调一致性、可用性、分区容错性的不可兼得，而BASE理论通过弱一致性（最终一致性）实现高可用性。**

3、文档式存储模式的优势体现在哪些方面？
**答案：** **文档式存储模式支持灵活的数据结构、嵌套查询，适合处理半结构化数据，且查询效率较高。**

4、简述Paxos算法在分布式共识中的作用。
**答案：** **Paxos算法用于分布式系统中达成共识，解决多节点间的数据一致性问题。**

## 六、综合题

1、结合实际应用场景，分析关系型数据库与NoSQL数据库在数据模型和一致性要求上的取舍。
**答案：** **关系型数据库适用于强一致性场景（如金融交易），而NoSQL适用于高可用性场景（如社交网络）。**

2、设计一个基于列存储模式的大数据分析系统，说明其架构设计和技术选型。
**答案：** **列存储模式适合OLAP场景，可结合Hadoop生态进行分布式处理，选择HBase或Cassandra作为底层存储。**

3、如何利用布隆过滤器优化NoSQL数据库的查询性能？
**答案：** **布隆过滤器可用于快速判断数据是否存在，减少不必要的磁盘或网络IO，提升查询性能。**

4、分析CAP理论在互联网应用中的实际应用案例，并讨论其设计权衡。
**答案：** **CAP理论在互联网应用中需根据业务需求权衡，例如电商系统优先保证可用性，容忍短暂不一致。**