# 选择题

1. 软件工程的概念是在（B）年被首次提出的。
    
    A.1949
    
    B.1968
    
    C.1972
    
    D.1989
    
2. 下列不属于软件工程的目标的是（C）。
    
    A. 提高软件产品的质量
    
    B. 提高软件产品的可靠性
    
    C. 减少软件产品的需求
    
    D. 控制软件产品的开发成本
    
3. 软件危机产生的主要原因是（D）。
    
    A. 软件工具落后
    
    B. 软件生产能力不足
    
    C. 人们对软件认识不够
    
    D. 软件本身的特点及开发方法
    
4. 人们公认的第一门面向对象语言是（A）。
    
    A.Simula67
    
    B.Smalltalk
    
    C.C++
    
    D.Java
    
5. 下列程序设计语言中不支持面向对象的特性的是（B）。
    
    A.C++
    
    B.ANSIC
    
    C.Java
    
    D.Objective-C
    
6. 下列不是面向对象方法的相关概念的是（D）。
    
    A. 封装
    
    B. 继承
    
    C. 多态
    
    D. 结构
    
7. (A) 是面向对象方法中用来描述 “对用户隐藏对象的属性和实现细节” 的概念。
    
    A. 封装
    
    B. 继承
    
    C. 多态
    
    D. 抽象
    
8. 下列不属于面向对象方法的优势的是（C）。
    
    A. 复用性强
    
    B. 改善了软件结构
    
    C. 软件的执行效率更高
    
    D. 抽象更符合人类的思维习惯
    
9. 下列关于模型的表述，不正确的是（A）。
    
    A. 建模语言只能通过图形表示
    
    B. 模型所描绘的系统蓝图既可以包括详细的计划，也可以包括总体计划
    
    C. 模型可以帮助开发组生成有用的工作产品
    
    D. 最好的模型总是与现实世界联系密切
    
10. UML 的全称是（B）。
    
    A.UnifyModelingLanguage
    
    B.UnifiedModelingLanguage
    
    C.UnifiedModemLanguage
    
    D.UnifiedMakingLanguage
    
11. UML 主要应用于（D）。
    
    A. 基于螺旋模型的结构化开发方法
    
    B. 基于需求动态定义的原型化方法
    
    C. 基于数据的数据流开发方法
    
    D. 基于对象的面向对象方法
    
12. 下列面向对象方法中不是 UML 所融合的方法的是（D）。
    
    A.Booch
    
    B.OOSE
    
    C.OMT
    
    D.Coad/Yourdon
    
13. OMT 是由（C）提出的。
    
    A. 布奇
    
    B. 朗博
    
    C. 科德
    
    D. 雅各布森
    
14. 在 UML 所融合的方法中，（B）是以用例来驱动需求获取的。
    
    A.Booch
    
    B.OOSE
    
    C.OMT
    
    D.Coad/Yourdon
    
15. 正式的 UML2.0 规范是在（B）年通过的。
    
    A.2001
    
    B.2003
    
    C.2005
    
    D.2007
    
16. 下列表述中不属于 UML 的目标的是（C）。
    
    A. 为用户提供了一种可用的、富有表达力的、可视化的建模语言
    
    B. 支持独立于程序设计语言和开发过程的规范
    
    C. 成为一种独立的程序设计语言
    
    D. 推动面向对象建模工具市场的发展
    
17. 下列事物中不属于 UML 中结构事物的是（D）。
    
    A. 类
    
    B. 组件
    
    C. 节点
    
    D. 状态机
    
18. 描述了一组动作序列的模型元素是（C）。
    
    A. 类
    
    B. 接口
    
    C. 用例
    
    D. 组件
    
19. 在 UML 中表示一般事物与特殊事物之间的关系的是（B）。
    
    A. 关联关系
    
    B. 泛化关系
    
    C. 依赖关系
    
    D. 实现关系
    
20. 我们可以使用 UML 中的（A）来描述图书馆与书的关系。
    
    A. 关联关系
    
    B. 泛化关系
    
    C. 依赖关系
    
    D. 实现关系
    
21. UML 使用（D）来描述接口和实现接口的类之间的关系。
    
    A. 关联关系
    
    B. 泛化关系
    
    C. 依赖关系
    
    D. 实现关系
    
22. 下列 UML 图中不属于结构图的是（D）。
    
    A. 类图
    
    B. 对象图
    
    C. 组件图
    
    D. 顺序图
    
23. 下列 UML 图中不是 UML2 规范中新增加的图的是（A）。
    
    A. 类图
    
    B. 交互概览图
    
    C. 组合结构图
    
    D. 时间图
    
24. 下列选项中不属于 UML 的扩展机制的是（C）。
    
    A. 约束
    
    B. 构造型
    
    C. 注释
    
    D. 标记值
    
25. 当我们需要表示某个元素的特性信息时，我们可以使用（D）这种扩展机制。
    
    A. 约束
    
    B. 构造型
    
    C. 注释
    
    D. 标记值
    
26. 在 “4+1” 视图模型中，（B）主要用来描述软件各个模块的组织方式。
    
    A. 逻辑视图
    
    B. 开发视图
    
    C. 进程视图
    
    D. 物理视图
    
27. 构造块包括事物、关系和图三方面的内容，其中事物是对模型中关键元素的抽象体现，关系是事物和事物间联系的方式，图是相关的事物及其关系的聚合。（A）
    
    A. 正确
    
    B. 错误
    
28. 结构事物总称为 （A），常见的结构事物有类、接口、用例、协作、组件、节点。
    
    A. 结构元素
    
    B. 行为元素
    
    C. 分组元素
    
    D. 注释元素
    
29. （B） 是系统中封装好的模块化部件，仅将外部接口暴露出来，内部实现被隐藏。
    
    A. 类
    
    B. 组件
    
    C. 接口
    
    D. 用例
    
30. 分组事物又称组织事物，是 UML 模型的组织部分，主要的分组事物是（C）。
    
    A. 类
    
    B. 组件
    
    C. 包
    
    D. 接口
    
31. 关联关系描述不同类元的实例之间的连接，（C）关系表示两个类元的实例具有整体和部分的关系。
    
    A. 关联
    
    B. 依赖
    
    C. 聚合
    
    D. 泛化
    
32. 交互过程如何产生系统的行为，用来描述系统的动态行为模型。（A）
    
    A. 正确
    
    B. 错误
    
33. 结构图捕获事物与事物之间的静态关系，用来描述系统的静态结构模型；（A）捕获事物的动态行为。
    
    A. 行为图
    
    B. 用例图
    
    C. 类图
    
    D. 组件图
    
34. 在面向对象系统建模中，最常见的两种划分是类型 - 实例、（A）。
    
    A. 整体 - 部分
    
    B. 关联 - 依赖
    
    C. 继承 - 实现
    
    D. 抽象 - 具体
    
35. “4+1” 视图架构中，（B）视图将系统功能分解，反映系统内部是如何组织和协作来实现功能的。
    
    A. 逻辑
    
    B. 开发
    
    C. 进程
    
    D. 物理
    
36. （B）视图主要用来描述软件各个模块的组织方式。
    
    A. 逻辑
    
    B. 开发
    
    C. 进程
    
    D. 物理
    
37. （C）视图主要描述系统的运行特性。
    
    A. 逻辑
    
    B. 开发
    
    C. 进程
    
    D. 物理
    
38. （D）视图主要描述系统的硬件配置。
    
    A. 逻辑
    
    B. 开发
    
    C. 进程
    
    D. 物理
    
39. （D）视图从项目需求入手，将四个视图合为一个整体，是离用户最近的视图。
    
    A. 逻辑
    
    B. 开发
    
    C. 进程
    
    D. 用例
    
40. 构造块就是 UML 中的事物。（B）
    
    A. 正确
    
    B. 错误
    
41. UML 中的行为事物通常用来描述模型中的动态部分。（A）
    
    A. 正确
    
    B. 错误
    
42. UML 中的注释可以隐藏起来。（A）
    
    A. 正确
    
    B. 错误
    
43. UML 中的关系负责连接两个同种类的模型元素。（B）
    
    A. 正确
    
    B. 错误
    
44. 所有的 UML 图都不依赖于元素符号的大小和位置。（A）
    
    A. 正确
    
    B. 错误
    
45. UML 的每个图形符号都暗示了该元素的规格说明。（A）
    
    A. 正确
    
    B. 错误
    
46. 类操作的可见性（公有、私有或保护）可以通过 UML 的通用划分来表示。（A）
    
    A. 正确
    
    B. 错误
    
47. UML 的用户可以随意对 UML 进行任意形式的扩展。（B）
    
    A. 正确
    
    B. 错误
    
48. UML 中的约束使用花括号中的文本来表示。（A）
    
    A. 正确
    
    B. 错误
    
49. “4+1” 视图模型中的开发视图将 4 个视图结合为一个整体。（A）
    
    A. 正确
    
    B. 错误
    
50. 在 UML 中，类图主要通过系统中的类及类之间的关系来描述系统的动态结构。（B）
    
    A. 正确
    
    B. 错误
    
51. 在 UML 中，用于描述系统功能的图是（B）。
    
    A. 类图
    
    B. 用例图
    
    C. 状态图
    
    D. 活动图
    
52. 下列关于用例图的说法，不正确的是（B）。
    
    A. 用例图展示了系统的功能需求
    
    B. 用例图中的参与者一定是人
    
    C. 用例图中的用例表示系统的一个功能单元
    
    D. 用例图中的关联关系表示参与者和用例之间的交互
    
53. 在用例图中，用来表示系统边界的是（A）。
    
    A. 矩形框
    
    B. 椭圆
    
    C. 菱形
    
    D. 小人图标
    
54. 用例之间的关系不包括（D）。
    
    A. 包含关系
    
    B. 扩展关系
    
    C. 泛化关系
    
    D. 实现关系
    
55. 在用例描述中，用来表示用例执行前系统必须满足的条件的是（A）。
    
    A. 前置条件
    
    B. 后置条件
    
    C. 基本事件流
    
    D. 备选事件流
    
56. 下列图中，用于描述系统中类的结构和关系的是（B）。
    
    A. 用例图
    
    B. 类图
    
    C. 对象图
    
    D. 状态图
    
57. 类图中，表示类之间的关联关系的线通常带有（A）。
    
    A. 箭头
    
    B. 实心圆点
    
    C. 菱形
    
    D. 波浪线
    
58. 在类图中，用来表示类的继承关系的是（A）。
    
    A. 实线箭头，箭头端带有空心三角形
    
    B. 实线箭头，箭头端带有实心三角形
    
    C. 虚线箭头，箭头端带有空心三角形
    
    D. 虚线箭头，箭头端带有实心三角形
    
59. 表示类之间的关联关系的多重性不包括（D）。
    
    A. 1..1
    
    B. 0..*
    
    C. 2..5
    
    D. 无限大
    
60. 在类图中，表示类的属性和操作的可见性，“+” 表示（A）。
    
    A. 公有
    
    B. 受保护
    
    C. 私有
    
    D. 包可见性
    
61. 对象图是（B）的实例，显示了系统在某一时刻的对象和它们之间的关系。
    
    A. 用例图
    
    B. 类图
    
    C. 状态图
    
    D. 活动图
    
62. 对象图中的链是（A）的实例。
    
    A. 关联关系
    
    B. 泛化关系
    
    C. 依赖关系
    
    D. 实现关系
    
63. 在 UML 中，用于描述对象之间的动态交互过程的图是（C）。
    
    A. 类图
    
    B. 对象图
    
    C. 顺序图
    
    D. 状态图
    
64. 顺序图中的基本元素不包括（D）。
    
    A. 对象
    
    B. 生命线
    
    C. 消息
    
    D. 状态
    
65. 在顺序图中，用来表示对象存在时间的垂直虚线是（A）。
    
    A. 生命线
    
    B. 激活期
    
    C. 消息线
    
    D. 对象线
    
66. 顺序图中的消息类型不包括（D）。
    
    A. 简单消息
    
    B. 返回消息
    
    C. 异步消息
    
    D. 紧急消息
    
67. 用来描述对象之间交互关系的空间组织结构的图是（B）。
    
    A. 类图
    
    B. 协作图
    
    C. 顺序图
    
    D. 状态图
    
68. 协作图中的基本元素不包括（D）。
    
    A. 对象
    
    B. 链
    
    C. 消息
    
    D. 生命线
    
69. 在协作图中，用来连接对象与对象的元素是（B）。
    
    A. 关联关系
    
    B. 链
    
    C. 生命线
    
    D. 消息
    
70. 协作图中的消息的重复序列用（A）表示。
    
    A. 循环
    
    B. 递归
    
    C. 迭代
    
    D. 并发
    
71. 用于对对象的生命周期建模的图是（B）。
    
    A. 类图
    
    B. 状态图
    
    C. 活动图
    
    D. 协作图
    
72. 状态图中的基本元素不包括（D）。
    
    A. 状态
    
    B. 转移
    
    C. 事件
    
    D. 消息
    
73. 状态图中的状态不包括（D）。
    
    A. 初态
    
    B. 中间态
    
    C. 终态
    
    D. 转移态
    
74. 表示状态之间转换的触发条件的是（D）。
    
    A. 事件
    
    B. 动作
    
    C. 活动
    
    D. 护卫条件
    
75. 在状态图中，允许嵌套其他状态的状态是（B）。
    
    A. 简单状态
    
    B. 复合状态
    
    C. 初态
    
    D. 终态
    
76. 活动图主要用于描述系统的（B）。
    
    A. 静态结构
    
    B. 动态行为
    
    C. 对象交互
    
    D. 状态变化
    
77. 活动图中的基本元素不包括（D）。
    
    A. 活动
    
    B. 动作
    
    C. 判断节点
    
    D. 状态
    
78. 在活动图中，用来表示决策点的元素是（B）。
    
    A. 初始节点
    
    B. 判断节点
    
    C. 活动节点
    
    D. 终止节点
    
79. 活动图中的泳道用于表示（C）。
    
    A. 活动的执行顺序
    
    B. 并发活动
    
    C. 活动的责任者
    
    D. 活动的分支
    
80. 用于展示系统物理结构的 UML 图是（B）。
    
    A. 类图
    
    B. 组件图
    
    C. 用例图
    
    D. 状态图
    
81. 组件图中的基本元素不包括（C）。
    
    A. 组件
    
    B. 接口
    
    C. 类
    
    D. 依赖关系
    
82. 表示组件之间依赖关系的线通常带有（A）。
    
    A. 箭头
    
    B. 实心圆点
    
    C. 菱形
    
    D. 波浪线
    
83. 在 UML 中，用于描述系统硬件部署的图是（B）。
    
    A. 组件图
    
    B. 部署图
    
    C. 用例图
    
    D. 类图
    
84. 部署图中的基本元素不包括（D）。
    
    A. 节点
    
    B. 组件
    
    C. 关联关系
    
    D. 用例
    
85. 表示节点之间通信的线通常带有（A）。
    
    A. 箭头
    
    B. 实心圆点
    
    C. 菱形
    
    D. 波浪线
    
86. 下列关于 RUP 的说法，不正确的是（D）。
    
    A. RUP 是一种迭代和增量的开发过程
    
    B. RUP 包括多个阶段和迭代
    
    C. RUP 强调文档的重要性
    
    D. RUP 不适用于小型项目
    
87. RUP 中的核心工作流不包括（B）。
    
    A. 需求工作流
    
    B. 设计工作流
    
    C. 测试工作流
    
    D. 部署工作流
    
88. 在 RUP 中，用来描述过程的是（D）。
    
    A. 阶段
    
    B. 迭代
    
    C. 用例
    
    D. 里程碑
    
89. RUP 中的起始阶段的主要任务是（A）。
    
    A. 确定项目范围和目标
    
    B. 进行详细设计
    
    C. 编写代码
    
    D. 测试系统
    
90. 下列图中，不属于 UML 的交互图的是（C）。
    
    A. 顺序图
    
    B. 协作图
    
    C. 活动图
    
    D. 通信图
    
91. 在 UML 中，用来表示系统中对象之间的通信关系的图是（C）。
    
    A. 类图
    
    B. 对象图
    
    C. 通信图
    
    D. 状态图
    
92. 通信图中的基本元素不包括（D）。
    
    A. 对象
    
    B. 消息
    
    C. 链
    
    D. 生命线
    
93. 在通信图中，用来表示对象之间通信路径的线是（B）。
    
    A. 关联关系
    
    B. 链
    
    C. 消息线
    
    D. 生命线
    
94. 通信图中的消息类型不包括（D）。
    
    A. 简单消息
    
    B. 返回消息
    
    C. 异步消息
    
    D. 紧急消息
    
95. 用于描述系统中对象之间的结构关系的图是（B）。
    
    A. 类图
    
    B. 对象图
    
    C. 通信图
    
    D. 部署图
    
96. 下列关于对象图的说法，不正确的是（C）。
    
    A. 对象图显示了某时刻的一组对象及它们的关系
    
    B. 对象图中的主要元素是链与对象
    
    C. 对象图中的链是泛化关系的实例
    
    D. 对象图主要用于说明系统在某一特定时刻的具体运行状态
    
97. 在 UML 中，用来表示系统中对象的物理部署的图是（B）。
    
    A. 组件图
    
    B. 部署图
    
    C. 对象图
    
    D. 类图
    
98. 部署图中的节点不包括（C）。
    
    A. 处理器
    
    B. 设备
    
    C. 对象
    
    D. 存储器
    
99. 表示节点之间通信路径的线通常带有（A）。
    
    A. 箭头
    
    B. 实心圆点
    
    C. 菱形
    
    D. 波浪线
    
100. 在 UML 中，用于描述系统的功能需求和行为的图是（B）。
    
    A. 类图
    
    B. 用例图
    
    C. 对象图
    
    D. 部署图
    
    以下是 50 道新的不重复选择题：
    
101. 在 UML 中，用于描述系统功能需求的图是（A）。
    
    A. 用例图
    
    B. 类图
    
    C. 状态图
    
    D. 组件图
    
102. 下列关于用例图的描述，正确的是（C）。
    
    A. 用例图展示了系统的内部结构
    
    B. 用例图中的参与者只能是人
    
    C. 用例图用于描述系统与用户之间的交互功能
    
    D. 用例图中的用例表示系统内部的类
    
103. 在用例图中，用于表示系统边界的是（B）。
    
    A. 椭圆
    
    B. 矩形框
    
    C. 菱形
    
    D. 小人图标
    
104. 下列图中，用于描述系统中对象的静态结构的是（B）。
    
    A. 用例图
    
    B. 对象图
    
    C. 状态图
    
    D. 活动图
    
105. 对象图中的基本元素是（B）。
    
    A. 用例
    
    B. 对象和链
    
    C. 状态
    
    D. 活动
    
106. 在 UML 中，用于描述对象之间交互顺序的图是（B）。
    
    A. 类图
    
    B. 顺序图
    
    C. 协作图
    
    D. 状态图
    
107. 顺序图中的基本元素不包括（C）。
    
    A. 对象
    
    B. 生命线
    
    C. 状态
    
    D. 消息
    
108. 在顺序图中，表示对象激活的垂直矩形是（B）。
    
    A. 生命线
    
    B. 激活期
    
    C. 消息线
    
    D. 对象线
    
109. 顺序图中的消息类型不包括（D）。
    
    A. 同步消息
    
    B. 异步消息
    
    C. 返回消息
    
    D. 紧急消息
    
110. 用于强调对象之间的空间组织结构和交互关系的图是（B）。
    
    A. 类图
    
    B. 协作图
    
    C. 顺序图
    
    D. 状态图
    
111. 协作图中的基本元素不包括（D）。
    
    A. 对象
    
    B. 链
    
    C. 消息
    
    D. 生命线
    
112. 在协作图中，用来表示对象之间通信路径的元素是（B）。
    
    A. 关联关系
    
    B. 链
    
    C. 生命线
    
    D. 消息线
    
113. 协作图中的消息类型不包括（D）。
    
    A. 简单消息
    
    B. 返回消息
    
    C. 异步消息
    
    D. 紧急消息
    
114. 用于描述对象生命周期行为的图是（B）。
    
    A. 类图
    
    B. 状态图
    
    C. 活动图
    
    D. 协作图
    
115. 状态图中的基本元素不包括（D）。
    
    A. 状态
    
    B. 转移
    
    C. 事件
    
    D. 消息
    
116. 状态图中的状态不包括（D）。
    
    A. 初态
    
    B. 中间态
    
    C. 终态
    
    D. 跳转态
    
117. 在状态图中，用于描述状态之间转换的触发因素的是（A）。
    
    A. 事件
    
    B. 动作
    
    C. 活动
    
    D. 护卫条件
    
118. 允许嵌套其他状态的状态是（B）。
    
    A. 简单状态
    
    B. 复合状态
    
    C. 初态
    
    D. 终态
    
119. 活动图主要用于描述系统的（B）。
    
    A. 静态结构
    
    B. 动态行为
    
    C. 对象交互
    
    D. 状态变化
    
120. 活动图中的基本元素不包括（D）。
    
    A. 活动
    
    B. 动作
    
    C. 判断节点
    
    D. 状态
    
121. 在活动图中，用来表示流程分支的元素是（B）。
    
    A. 初始节点
    
    B. 判断节点
    
    C. 活动节点
    
    D. 终止节点
    
122. 活动图中的泳道用于表示（C）。
    
    A. 活动的执行顺序
    
    B. 并发活动
    
    C. 活动的责任者
    
    D. 活动的分支
    
123. 用于展示系统物理结构和组件之间关系的 UML 图是（B）。
    
    A. 类图
    
    B. 组件图
    
    C. 用例图
    
    D. 状态图
    
124. 组件图中的基本元素不包括（C）。
    
    A. 组件
    
    B. 接口
    
    C. 类
    
    D. 依赖关系
    
125. 表示组件之间依赖关系的线通常带有（A）。
    
    A. 箭头
    
    B. 实心圆点
    
    C. 菱形
    
    D. 波浪线
    
126. 在 UML 中，用于描述系统硬件和软件部署情况的图是（B）。
    
    A. 组件图
    
    B. 部署图
    
    C. 用例图
    
    D. 类图
    
127. 部署图中的基本元素不包括（D）。
    
    A. 节点
    
    B. 组件
    
    C. 关联关系
    
    D. 用例
    
128. 表示节点之间通信的线通常带有（A）。
    
    A. 箭头
    
    B. 实心圆点
    
    C. 菱形
    
    D. 波浪线
    
129. 下列关于 RUP 的描述，正确的是（C）。
    
    A. RUP 是一种线性开发过程
    
    B. RUP 不支持迭代开发
    
    C. RUP 包括多个阶段和迭代
    
    D. RUP 仅适用于大型项目
    
130. RUP 中的核心工作流包括（A）。
    
    A. 需求工作流
    
    B. 设计工作流
    
    C. 测试工作流
    
    D. 部署工作流
    
131. 在 RUP 中，用来划分开发周期的时间间隔是（B）。
    
    A. 阶段
    
    B. 迭代
    
    C. 用例
    
    D. 里程碑
    
132. RUP 中的细化阶段的主要任务是（B）。
    
    A. 确定项目范围和目标
    
    B. 进行详细设计
    
    C. 编写代码
    
    D. 测试系统
    
133. 下列图中，不属于 UML 的结构图的是（C）。
    
    A. 类图
    
    B. 对象图
    
    C. 状态图
    
    D. 组件图
    
134. 在 UML 中，用来表示系统中对象之间的物理部署关系的图是（B）。
    
    A. 组件图
    
    B. 部署图
    
    C. 对象图
    
    D. 类图
    
135. 部署图中的节点类型不包括（C）。
    
    A. 处理器
    
    B. 设备
    
    C. 对象
    
    D. 存储器
    
136. 表示节点之间通信路径的线通常带有（A）。
    
    A. 箭头
    
    B. 实心圆点
    
    C. 菱形
    
    D. 波浪线
    
137. 在 UML 中，用于描述系统的功能需求和交互行为的图是（B）。
    
    A. 类图
    
    B. 用例图
    
    C. 对象图
    
    D. 部署图
    
138. 用例图中的参与者可以是（D）。
    
    A. 人
    
    B. 设备
    
    C. 其他系统
    
    D. 以上都是
    
139. 在用例图中，用于表示参与者和用例之间关系的线是（A）。
    
    A. 实线
    
    B. 虚线
    
    C. 点划线
    
    D. 双线
    
140. 下列关于用例图的描述，正确的是（C）。
    
    A. 用例图中的用例表示系统内部的实现细节
    
    B. 用例图中的参与者必须是系统的用户
    
    C. 用例图用于描述系统的功能需求
    
    D. 用例图中的关系只能是关联关系
    
141. 在 UML 中，用于描述系统中类的层次结构和关系的图是（B）。
    
    A. 用例图
    
    B. 类图
    
    C. 对象图
    
    D. 状态图
    
142. 类图中的基本元素不包括（D）。
    
    A. 类
    
    B. 接口
    
    C. 关联关系
    
    D. 消息
    
143. 在类图中，表示类之间的关联关系的线通常带有（A）。
    
    A. 箭头
    
    B. 实心圆点
    
    C. 菱形
    
    D. 波浪线
    
144. 类图中的关联关系不包括（D）。
    
    A. 一对一
    
    B. 一对多
    
    C. 多对多
    
    D. 无关联
    
145. 表示类之间继承关系的线通常带有（A）。
    
    A. 空心三角形箭头
    
    B. 实心三角形箭头
    
    C. 菱形
    
    D. 波浪线
    
146. 在 UML 中，用于描述系统中对象的实例和它们之间的关系的图是（B）。
    
    A. 类图
    
    B. 对象图
    
    C. 用例图
    
    D. 状态图
    
147. 对象图中的链是（A）的实例。
    
    A. 关联关系
    
    B. 泛化关系
    
    C. 依赖关系
    
    D. 实现关系
    
148. 在 UML 中，用于描述对象之间的交互过程和消息传递的图是（B）。
    
    A. 类图
    
    B. 顺序图
    
    C. 对象图
    
    D. 状态图
    
149. 顺序图中的消息类型不包括（D）。
    
    A. 同步消息
    
    B. 异步消息
    
    C. 返回消息
    
    D. 控制消息
    
150. 在顺序图中，用来表示对象存在时间的垂直虚线是（A）。
    
    A. 生命线
    
    B. 激活期
    
    C. 消息线
    
    D. 对象线
    
151. 在 UML 中，用于描述系统功能需求的静态视图是（A）。
    
    A. 用例图
    
    B. 类图
    
    C. 状态图
    
    D. 组件图
    
152. 下列关于用例图的描述，正确的是（C）。
    
    A. 用例图展示了系统的内部数据结构
    
    B. 用例图中的参与者只能是系统的外部用户
    
    C. 用例图用于描述系统与用户之间的交互功能
    
    D. 用例图中的用例表示系统内部的具体算法
    
153. 在用例图中，用于表示系统功能模块的是（A）。
    
    A. 椭圆
    
    B. 矩形框
    
    C. 菱形
    
    D. 小人图标
    
154. 下列图中，用于描述系统中对象的静态结构和关系的是（B）。
    
    A. 用例图
    
    B. 对象图
    
    C. 状态图
    
    D. 活动图
    
155. 对象图中的基本元素是（B）。
    
    A. 用例和参与者
    
    B. 对象和链
    
    C. 状态和转移
    
    D. 活动和动作
    
156. 在 UML 中，用于描述对象之间交互顺序和时间的图是（B）。
    
    A. 类图
    
    B. 顺序图
    
    C. 协作图
    
    D. 状态图
    
157. 顺序图中的基本元素不包括（C）。
    
    A. 对象和生命线
    
    B. 消息和激活期
    
    C. 状态和转移
    
    D. 参与者和用例
    
158. 在顺序图中，表示对象激活的垂直矩形是（B）。
    
    A. 生命线
    
    B. 激活期
    
    C. 消息线
    
    D. 对象线
    
159. 顺序图中的消息类型不包括（D）。
    
    A. 同步消息
    
    B. 异步消息
    
    C. 返回消息
    
    D. 数据消息
    
160. 用于强调对象之间的空间组织结构和交互关系的图是（B）。
    
    A. 类图
    
    B. 协作图
    
    C. 顺序图
    
    D. 状态图
    
161. 协作图中的基本元素不包括（C）。
    
    A. 对象和链
    
    B. 消息和激活期
    
    C. 状态和转移
    
    D. 参与者和用例
    
162. 在协作图中，用来表示对象之间通信路径的元素是（B）。
    
    A. 关联关系
    
    B. 链
    
    C. 生命线
    
    D. 消息线
    
163. 协作图中的消息类型不包括（D）。
    
    A. 简单消息
    
    B. 返回消息
    
    C. 异步消息
    
    D. 控制消息
    
164. 用于描述对象生命周期行为的图是（B）。
    
    A. 类图
    
    B. 状态图
    
    C. 活动图
    
    D. 协作图
    
165. 状态图中的基本元素不包括（C）。
    
    A. 状态和转移
    
    B. 事件和动作
    
    C. 活动和泳道
    
    D. 初始节点和终止节点
    
166. 状态图中的状态不包括（D）。
    
    A. 初始状态
    
    B. 中间状态
    
    C. 终止状态
    
    D. 跳转状态
    
167. 在状态图中，用于描述状态之间转换的触发因素的是（C）。
    
    A. 状态
    
    B. 转移
    
    C. 事件
    
    D. 动作
    
168. 允许嵌套其他状态的状态是（B）。
    
    A. 简单状态
    
    B. 复合状态
    
    C. 初始状态
    
    D. 终止状态
    
169. 活动图主要用于描述系统的（B）。
    
    A. 静态结构
    
    B. 动态行为
    
    C. 对象交互
    
    D. 状态变化
    
170. 活动图中的基本元素不包括（D）。
    
    A. 活动和动作
    
    B. 判断节点和合并节点
    
    C. 泳道和消息
    
    D. 状态和转移
    
171. 在活动图中，用来表示流程分支的元素是（B）。
    
    A. 初始节点
    
    B. 判断节点
    
    C. 活动节点
    
    D. 终止节点
    
172. 活动图中的泳道用于表示（C）。
    
    A. 活动的执行顺序
    
    B. 并发活动
    
    C. 活动的责任者
    
    D. 活动的分支
    
173. 用于展示系统物理结构和组件之间关系的 UML 图是（B）。
    
    A. 类图
    
    B. 组件图
    
    C. 用例图
    
    D. 状态图
    
174. 组件图中的基本元素不包括（C）。
    
    A. 组件
    
    B. 接口
    
    C. 类
    
    D. 依赖关系
    
175. 表示组件之间依赖关系的线通常带有（A）。
    
    A. 箭头
    
    B. 实心圆点
    
    C. 菱形
    
    D. 波浪线
    
176. 在 UML 中，用于描述系统硬件和软件部署情况的图是（B）。
    
    A. 组件图
    
    B. 部署图
    
    C. 用例图
    
    D. 类图
    
177. 部署图中的基本元素不包括（D）。
    
    A. 节点
    
    B. 组件
    
    C. 关联关系
    
    D. 用例
    
178. 表示节点之间通信的线通常带有（A）。
    
    A. 箭头
    
    B. 实心圆点
    
    C. 菱形
    
    D. 波浪线
    
179. 下列关于 RUP 的描述，正确的是（C）。
    
    A. RUP 是一种线性开发过程
    
    B. RUP 不支持迭代开发
    
    C. RUP 包括多个阶段和迭代
    
    D. RUP 仅适用于大型项目
    
180. RUP 中的核心工作流包括（A）。
    
    A. 需求工作流
    
    B. 设计工作流
    
    C. 测试工作流
    
    D. 部署工作流
    
181. 在 RUP 中，用来划分开发周期的时间间隔是（B）。
    
    A. 阶段
    
    B. 迭代
    
    C. 用例
    
    D. 里程碑
    
182. RUP 中的细化阶段的主要任务是（B）。
    
    A. 确定项目范围和目标
    
    B. 进行详细设计
    
    C. 编写代码
    
    D. 测试系统
    
183. 下列图中，不属于 UML 的结构图的是（C）。
    
    A. 类图
    
    B. 对象图
    
    C. 状态图
    
    D. 组件图
    
184. 在 UML 中，用来表示系统中对象之间的物理部署关系的图是（B）。
    
    A. 组件图
    
    B. 部署图
    
    C. 对象图
    
    D. 类图
    
185. 部署图中的节点类型不包括（C）。
    
    A. 处理器
    
    B. 设备
    
    C. 对象
    
    D. 存储器
    
186. 表示节点之间通信路径的线通常带有（A）。
    
    A. 箭头
    
    B. 实心圆点
    
    C. 菱形
    
    D. 波浪线
    
187. 在 UML 中，用于描述系统的功能需求和交互行为的图是（B）。
    
    A. 类图
    
    B. 用例图
    
    C. 对象图
    
    D. 部署图
    
188. 用例图中的参与者可以是（D）。
    
    A. 人
    
    B. 设备
    
    C. 其他系统
    
    D. 以上都是
    
189. 在用例图中，用于表示参与者和用例之间关系的线是（A）。
    
    A. 实线
    
    B. 虚线
    
    C. 点划线
    
    D. 双线
    
190. 下列关于用例图的描述，正确的是（C）。
    
    A. 用例图中的用例表示系统内部的实现细节
    
    B. 用例图中的参与者必须是系统的用户
    
    C. 用例图用于描述系统的功能需求
    
    D. 用例图中的关系只能是关联关系
    
191. 在 UML 中，用于描述系统中类的层次结构和关系的图是（B）。
    
    A. 用例图
    
    B. 类图
    
    C. 对象图
    
    D. 状态图
    
192. 类图中的基本元素不包括（D）。
    
    A. 类
    
    B. 接口
    
    C. 关联关系
    
    D. 消息
    
193. 在类图中，表示类之间的关联关系的线通常带有（A）。
    
    A. 箭头
    
    B. 实心圆点
    
    C. 菱形
    
    D. 波浪线
    
194. 类图中的关联关系不包括（D）。
    
    A. 一对一
    
    B. 一对多
    
    C. 多对多
    
    D. 无关联
    
195. 表示类之间继承关系的线通常带有（A）。
    
    A. 空心三角形箭头
    
    B. 实心三角形箭头
    
    C. 菱形
    
    D. 波浪线
    
196. 在 UML 中，用于描述系统中对象的实例和它们之间的关系的图是（B）。
    
    A. 类图
    
    B. 对象图
    
    C. 用例图
    
    D. 状态图
    
197. 对象图中的链是（A）的实例。
    
    A. 关联关系
    
    B. 泛化关系
    
    C. 依赖关系
    
    D. 实现关系
    
198. 在 UML 中，用于描述对象之间的交互过程和消息传递的图是（B）。
    
    A. 类图
    
    B. 顺序图
    
    C. 对象图
    
    D. 状态图
    
199. 顺序图中的消息类型不包括（D）。
    
    A. 同步消息
    
    B. 异步消息
    
    C. 返回消息
    
    D. 控制消息
    
200. 在顺序图中，用来表示对象存在时间的垂直虚线是（A）。
    
    A. 生命线
    
    B. 激活期
    
    C. 消息线
    
    D. 对象线
    
201. 软件工程的概念是在 **1968** 年被首次提出的。
    
202. 软件工程的目标包括提高软件产品的 **质量**、可靠性、控制开发成本和按时交付等。
    
203. 软件危机的主要表现是软件 **需求** 增加、软件价格上升等。
    
204. 面向对象方法的优势包括封装性强、改善了软件 **结构**、软件的执行效率更高、抽象更符合人类的思维习惯等。
    
205. 封装是指将数据和 **操作** 封装在一起，形成一个独立的对象。
    
206. 继承是指子类继承父类的 **属性** 和方法，实现代码复用。
    
207. 多态是指同一个接口可以有 **不同** 的实现方式。
    
208. UML 的全称是 **Unified Modeling Language**。
    
209. UML 主要应用于基于对象的 **面向对象** 方法。
    
210. UML 所融合的方法中，**OOSE** 是以用例来驱动需求获取的。
    
211. 正式的 UML2.0 规范是在 **2005** 年通过的。
    
212. UML 的目标包括为用户提供更多信息请查看原文用户提供了一个可用的、富有表达力的、可视化的建模语言，支持独立于程序设计语言和开发过程的规范，推动面向对象建模工具市场的发展等。 (注意：此题原文答案为“模型元素”，但题目本身不完整，此处保留原文答案，未作填空处理)
    
213. UML 中的结构事物包括 **类**、接口、用例、协作、组件、节点等。
    
214. 构造块包括事物、**关系** 和图三方面的内容。
    
215. 关联关系描述不同类元的实例之间的 **连接**。
    
216. 聚合关系表示两个类元的实例具有 **整体** 和部分的关系。
    
217. 交互过程如何产生系统的行为，用来描述系统的动态行为模型，交互过程通过 **消息传递** 来产生系统的行为。
    
218. 结构图捕获事物与事物之间的静态关系，用来描述系统的静态结构模型；**行为图** 捕获事物的动态行为。
    
219. 在面向对象系统建模中，最常见的两种划分是类型 - 实例、**整体 - 部分**。
    
220. “4+1” 视图架构中，**开发** 视图将系统功能分解，反映系统内部是如何组织和协作来实现功能的。
    
221. 构造块就是 UML 中的事物。**错**（判断对错）
    
222. UML 中的行为事物通常用来描述模型中的动态部分。**对**（判断对错）
    
223. UML 中的注释可以隐藏起来。**对**（判断对错）
    
224. UML 中的关系负责连接两个同种类的模型元素。**错**（判断对错）
    
225. 所有的 UML 图都不依赖于元素符号的大小和位置。**对**（判断对错）
    
226. UML 的每个图形符号都暗示了该元素的规格说明。**对**（判断对错）
    
227. 类操作的可见性（公有、私有或保护）可以通过 UML 的通用划分来表示。**对**（判断对错）
    
228. UML 的用户可以随意对 UML 进行任意形式的扩展。**错**（判断对错）
    
229. UML 中的约束使用花括号中的文本来表示。**对**（判断对错）
    
230. “4+1” 视图模型中的开发视图将 4 个视图结合为一个整体。**对**（判断对错）
    
231. 在 UML 中，用于描述系统功能需求的图是 **用例图**。
    
232. 用例图中的参与者可以是人、**设备** 或其他系统。
    
233. 在用例图中，用于表示参与者和用例之间关系的线是 **实线**。
    
234. 用例图中的用例表示系统的 **一个** 功能单元。
    
235. 在 UML 中，用于描述系统中类的结构和关系的图是 **类图**。
    
236. 类图中，表示类之间的关联关系的线通常带有 **箭头**。
    
237. 在类图中，用来表示类的继承关系的是 **实线箭头，箭头端带有空心三角形**。
    
238. 表示类之间的关联关系的多重性不包括 **无限大**。
    
239. 在类图中，表示类的属性和操作的可见性，“+” 表示 **公有**。
    
240. 对象图是 **类图** 的实例，显示了系统在某一时刻的对象和它们之间的关系。
    
241. 对象图中的链是 **关联关系** 的实例。
    
242. 在 UML 中，用于描述对象之间的动态交互过程的图是 **顺序图**。
    
243. 顺序图中的基本元素不包括 **状态**。
    
244. 在顺序图中，用来表示对象存在时间的垂直虚线是 **生命线**。
    
245. 顺序图中的消息类型不包括 **紧急消息**。
    
246. 用来描述对象之间交互关系的空间组织结构的图是 **协作图**。
    
247. 协作图中的基本元素不包括 **生命线**。
    
248. 在协作图中，用来连接对象与对象的元素是 **链**。
    
249. 协作图中的消息的重复序列用 **循环** 表示。
    
250. 用于对对象的生命周期建模的图是 **状态图**。
    
251. 状态图中的基本元素不包括 **消息**。
    
252. 状态图中的状态不包括 **跳转态**。
    
253. 表示状态之间转换的触发条件的是 **事件**。
    
254. 在状态图中，允许嵌套其他状态的状态是 **复合状态**。
    
255. 活动图主要用于描述系统的 **动态行为**。
    
256. 活动图中的基本元素不包括 **状态**。
    
257. 在活动图中，用来表示决策点的元素是 **判断节点**。
    
258. 活动图中的泳道用于表示 **活动的责任者**。
    
259. 用于展示系统物理结构的 UML 图是 **组件图**。
    
260. 组件图中的基本元素不包括 **类**。
    
261. 表示组件之间依赖关系的线通常带有 **箭头**。
    
262. 在 UML 中，用于描述系统硬件部署的图是 **部署图**。
    
263. 部署图中的基本元素不包括 **用例**。
    
264. 表示节点之间通信的线通常带有 **箭头**。
    
265. 下列关于 RUP 的说法，不正确的是 **RUP 不适用于小型项目**。
    
266. RUP 中的核心工作流不包括 **设计工作流**。
    
267. 在 RUP 中，用来描述过程的是 **里程碑**。
    
268. RUP 中的起始阶段的主要任务是 **确定项目范围和目标**。
    
269. 下列图中，不属于 UML 的交互图的是 **活动图**。
    
270. 在 UML 中，用来表示系统中对象之间的通信关系的图是 **通信图**。
    
271. 通信图中的基本元素不包括 **生命线**。
    
272. 在通信图中，用来表示对象之间通信路径的线是 **链**。
    
273. 通信图中的消息类型不包括 **紧急消息**。
    
274. 用于描述系统中对象之间的结构关系的图是 **对象图**。
    
275. 下列关于对象图的说法，不正确的是 **对象图中的链是泛化关系的实例**。
    
276. 在 UML 中，用来表示系统中对象的物理部署的图是 **部署图**。
    
277. 部署图中的节点不包括 **对象**。
    
278. 表示节点之间通信路径的线通常带有 **箭头**。
    
279. 在 UML 中，用于描述系统的功能需求和行为的图是 **用例图**。
    
280. 在 UML 中，用于描述系统功能需求的静态视图是 **用例图**。
    
281. 在 UML 中，用于描述系统中对象的静态结构和关系的图是 **对象图**。
    
282. 在 UML 中，用于描述对象之间交互顺序和时间的图是 **顺序图**。
    
283. 在顺序图中，表示对象激活的垂直矩形是 **激活期**。
    
284. 用于强调对象之间的空间组织结构和交互关系的图是 **协作图**。
    
285. 在协作图中，用来表示对象之间通信路径的元素是 **链**。
    
286. 用于描述对象生命周期行为的图是 **状态图**。
    
287. 状态图中的基本元素不包括 **消息**。
    
288. 在状态图中，用于描述状态之间转换的触发因素的是 **事件**。
    
289. 活动图主要用于描述系统的 **动态行为**。
    
290. 活动图中的基本元素不包括 **状态**。
    
291. 在活动图中，用来表示流程分支的元素是 **判断节点**。
    
292. 活动图中的泳道用于表示 **活动的责任者**。
    
293. 用于展示系统物理结构和组件之间关系的 UML 图是 **组件图**。
    
294. 组件图中的基本元素不包括 **类**。
    
295. 表示组件之间依赖关系的线通常带有 **箭头**。
    
296. 在 UML 中，用于描述系统硬件和软件部署情况的图是 **部署图**。
    
297. 部署图中的基本元素不包括 **用例**。
    
298. 表示节点之间通信的线通常带有 **箭头**。
    
299. 在 UML 中，用于描述系统的功能需求和交互行为的图是 **用例图**。
    
300. 在 UML 中，用于描述系统中类的层次结构和关系的图是 类图。
    

# 判断题（一）
    
301. 软件工程的目标是提高软件产品的质量、可靠性和生产效率。（**√**）
    
302. 软件危机的产生与软件本身的复杂性无关。（**×**）
    
303. 面向对象方法的优势包括封装性强、改善软件结构、支持代码复用等。（**√**）
    
304. 封装是将数据和操作封装在一起，形成一个独立的对象。（**√**）
    
305. 继承是指子类继承父类的属性和方法，实现代码复用。（**√**）
    
306. 多态是指同一个接口可以有多种不同的实现方式。（**√**）
    
307. UML 是一种标准化的建模语言，用于描述系统的结构和行为。（**√**）
    
308. UML 主要应用于基于对象的面向对象方法。（**√**）
    
309. UML 的目标包括为用户提供一种可视化的建模语言，支持独立于程序设计语言和开发过程的规范。（**√**）
    
310. UML 中的结构事物包括类、接口、用例、协作、组件、节点等。（**√**）
    
311. 构造块包括事物、关系和图三方面的内容。（**√**）
    
312. 关联关系描述不同类元的实例之间的连接。（**√**）
    
313. 聚合关系表示两个类元的实例具有整体和部分的关系。（**√**）
    
314. 交互过程通过消息传递来产生系统的行为。（**√**）
    
315. 结构图捕获事物与事物之间的静态关系，用来描述系统的静态结构模型。（**√**）
    
316. 在面向对象系统建模中，常见的两种划分是类型 - 实例和整体 - 部分。（**√**）
    
317. “4+1” 视图架构中的开发视图将系统功能分解，反映系统内部的组织和协作。（**√**）
    
318. 构造块就是 UML 中的事物。（**×**）
    
319. UML 中的行为事物通常用来描述模型中的动态部分。（**√**）
    
320. UML 中的注释可以隐藏起来。（**√**）
    
321. UML 中的关系负责连接两个同种类的模型元素。（**×**）
    
322. 所有的 UML 图都不依赖于元素符号的大小和位置。（**√**）
    
323. UML 的每个图形符号都暗示了该元素的规格说明。（**√**）
    
324. 类操作的可见性（公有、私有或保护）可以通过 UML 的通用划分来表示。（**√**）
    
325. UML 的用户可以随意对 UML 进行任意形式的扩展。（**×**）
    
326. UML 中的约束使用花括号中的文本来表示。（**√**）
    
327. “4+1” 视图模型中的开发视图将 4 个视图结合为一个整体。（**√**）
    
328. 在 UML 中，用例图用于描述系统的功能需求。（**√**）
    
329. 用例图中的参与者只能是人。（**×**）
    
330. 用例图中的用例表示系统内部的实现细节。（**×**）
    
331. 用例图中的关系只能是关联关系。（**×**）
    
332. 在 UML 中，类图用于描述系统中类的结构和关系。（**√**）
    
333. 类图中的关联关系的线通常带有箭头。（**√**）
    
334. 类图中的继承关系用实线箭头，箭头端带有实心三角形表示。（**√**）
    
335. 对象图是类图的实例，显示了系统在某一时刻的对象和它们之间的关系。（**√**）
    
336. 对象图中的链是泛化关系的实例。（**×**）
    
337. 在 UML 中，顺序图用于描述对象之间的交互顺序和时间。（**√**）
    
338. 顺序图中的生命线表示对象的生存期，通常是一条垂直的虚线。（**√**）
    
339. 顺序图中的消息类型包括同步消息、异步消息和返回消息。（**√**）
    
340. 协作图强调对象之间的空间组织结构和交互关系。（**√**）
    
341. 协作图中的链用于连接对象，表示它们之间的通信路径。（**√**）
    
342. 协作图中的消息类型不包括紧急消息。（**√**）
    
343. 状态图用于描述对象的生命周期，包括各种状态和状态之间的转移。（**√**）
    
344. 状态图中的状态包括初态、中间态和终态。（**√**）
    
345. 活动图用于描述系统的动态行为，强调活动的流程和顺序。（**√**）
    
346. 活动图中的判断节点用于表示流程的分支条件。（**√**）
    
347. 活动图中的泳道用于表示活动的责任者。（**√**）
    
348. 组件图用于展示系统的物理结构和组件之间的关系。（**√**）
    
349. 组件图中的组件代表系统中的可执行模块。（**√**）
    
350. 表示组件之间依赖关系的线通常带有箭头。（**√**）
    
351. 在 UML 中，部署图用于描述系统的硬件和软件的部署情况。（**√**）
    
352. 部署图中的节点用于表示物理硬件设备。（**√**）
    
353. 表示节点之间通信的线通常带有箭头。（**√**）
    
354. RUP 是一种线性开发过程。（**×**）
    
355. RUP 包括多个阶段和迭代。（**√**）
    
356. RUP 中的起始阶段主要任务是确定项目范围和目标。（**√**）
    
357. RUP 的核心工作流包括需求获取、分析设计、实现和测试。（**√**）
    
358. 在 RUP 中，里程碑用于标记开发过程中的重要节点。（**√**）
    
359. RUP 中的细化阶段主要进行系统的架构设计和风险评估。（**√**）
    
360. RUP 的核心工作流中的测试工作流主要关注系统的测试和验证。（**√**）
    
361. 用例图中的参与者必须是系统的用户。（**×**）
    
362. 用例图中的用例表示系统内部的具体算法。（**×**）
    
363. 类图中的关联关系不包括多对多。（**×**）
    
364. 表示类之间继承关系的线通常带有空心三角形箭头。（**√**）
    
365. 对象图中的链是关联关系的实例。（**√**）
    
366. 顺序图中的消息类型不包括数据消息。（**×**）
    
367. 协作图中的消息的重复序列用循环表示。（**√**）
    
368. 状态图中的状态包括跳转态。（**×**）
    
369. 活动图中的基本元素包括活动、动作、判断节点和泳道。（**√**）
    
370. 组件图中的基本元素包括组件、接口和依赖关系。（**√**）
    
371. 部署图中的基本元素包括节点、组件和关联关系。（√）
    
# 填空题（优选）
    
372.软件危机的产生与软件本身的复杂性、开发方法的不成熟以及 **沟通不畅** 等因素密切相关。
    
373.面向对象方法的核心在于将现实世界中的事物抽象为 **对象**，并通过它们之间的交互来实现程序功能。
    
374.UML 提供了一套标准化的 **建模符号**，用于描述系统的结构和行为。
    
375.在 UML 的用例图中，**参与者** 通常用来表示系统的用户或其他与系统交互的实体。
    
376.用例之间可以存在包含关系、扩展关系和 **泛化关系**。
    
377.在类图中，**多重性** 用于表示类之间的关联类型，如一对一、一对多等。
    
378.**<<inherits>>** 是类图中用于表示类之间继承关系的关键字。
    
379.在顺序图中，**生命线** 用于表示对象的生存期，通常是一条垂直的虚线。
    
380.顺序图中的 **消息** 用于表示对象之间的交互顺序。
    
381.在协作图中，**链** 用于连接对象，表示它们之间的通信路径。
    
382.状态图用于描述对象的生命周期，包括各种 **状态** 和状态之间的转移。
    
383.在状态图中，**初态** 用于表示对象的初始状态。
    
384.活动图中的 **判断节点** 用于表示流程中的分支条件。
    
385.在活动图中，**终止节点** 用于表示流程的结束。
    
386.组件图用于展示系统的物理结构，其中 **组件** 代表系统中的可执行模块。
    
387.在组件图中，**依赖箭头** 用于表示组件之间的依赖关系。
    
388.部署图用于描述系统的硬件架构和软件组件的 **部署情况**。
    
389.在部署图中，**节点** 用于表示物理硬件设备。
    
390.RUP 是一种 **迭代** 开发过程，强调迭代和增量开发。
    
391.RUP 中的 **起始阶段** 阶段主要确定项目的范围和目标。
    
392.RUP 的核心工作流包括需求获取、分析设计、实现、**测试** 等。
    
393.在 UML 中，**部署图** 用于表示系统中的物理节点和部署在这些节点上的软件构件。
    
394.在 UML 中，**类图** 用于描述系统的静态结构，包括类及其相互关系。
    
395.在类图中，**连线** 用于表示类之间的关联，如关联、聚合、组合等。
    
396.在 UML 中，**泛化** 用于表示类之间的继承关系。
    
397.在类图中，**实现** 用于表示类之间的实现关系，常见于接口和类之间。
    
398.在 UML 中，**对象图** 用于描述系统中的对象及其相互关系，强调系统的静态结构。
    
399.对象图中的 **链** 用于表示对象之间的关联实例。
    
400.在 UML 中，**顺序图** 用于描述对象之间的动态交互，强调消息的传递顺序。
    
401.顺序图中的 **激活框** 用于表示对象的激活时间段。
    
402.在顺序图中，**异步消息** 用于表示对象之间的异步消息传递。
    
403.在 UML 中，**协作图** 用于描述对象之间的协作关系，强调对象之间的链接和消息传递。
    
404.在协作图中，**链** 用于表示对象之间的通信链接。
    
405.协作图中的 **消息** 用于表示对象之间的消息传递。
    
406.在 UML 中，**状态图** 用于描述对象的生命周期，包括状态和状态转移。
    
407.状态图中的 **终态** 用于表示对象的终止状态。
    
408.在状态图中，**转移** 用于表示从一个状态到另一个状态的转移。
    
409.在状态图中，**活动** 用于表示在某个状态下执行的操作或活动。
    
410.在 UML 中，**活动图** 用于描述系统的动态行为，强调活动的流程和顺序。
    
411.活动图中的 **判断节点和合并节点** 用于表示流程的分支和合并。
    
412.活动图中的 **初始节点** 用于表示流程的起点。
    
413.在 UML 中，**组件图** 用于描述系统的组件及其相互关系，强调系统的物理结构。
    
414.组件图中的 **接口** 用于表示组件之间的接口。
    
415.在组件图中，**依赖关系** 用于表示组件之间的依赖关系。
    
416.在 UML 中，**部署图** 用于描述系统的硬件和软件的部署关系，强调系统的物理架构。
    
417.部署图中的 **节点** 用于表示物理计算节点。
    
418.在部署图中，**构件** 用于表示部署在节点上的软件构件。
    
419.RUP 中的 **构建阶段** 阶段主要进行系统的详细设计和实现。
    
420.RUP 的核心工作流中的 **需求工作流** 工作流主要关注需求的获取和管理。
    
421.在 RUP 中，**里程碑** 用于标记开发过程中的重要里程碑。
    
422.在 UML 中，**用例图** 用于描述系统中的功能需求，强调用户与系统的交互。
    
423.用例图中的 **用例** 用于表示系统中的功能单元。
    
424.在用例图中，**关联线** 用于表示参与者与用例之间的交互关系。
    
425.用例之间的 **包含关系** 关系用于表示一个用例包含另一个用例的功能。
    
426.在用例图中，**泛化关系** 关系用于表示一个用例继承另一个用例的功能。
    
427.在 UML 中，**类图** 用于描述系统的静态结构和对象之间的关系。
    
428.类图中的 **类元素** 用于表示类的属性和操作。
    
429.在类图中，**关联多重性** 用于表示类之间的关联类型。
    
430.类图中的 **聚合** 用于表示类之间的整体与部分关系。
    
431.在类图中，**组合** 用于表示类之间的强整体与部分关系，部分不能脱离整体存在。
    
432.在 UML 中，**顺序图** 用于描述系统的动态交互，强调对象之间消息的传递顺序。
    
433.顺序图中的 **生命线** 用于表示对象的存在时间线。
    
434.顺序图中的 **消息线** 用于表示对象之间的消息传递。
    
435.在顺序图中，**激活条** 用于表示对象的激活时间段。
    
436.顺序图中的 **异步消息** 用于表示对象之间的异步消息传递。
    
437.在 UML 中，**协作图** 用于描述系统的协作关系，强调对象之间的链接和消息。
    
438.协作图中的 **链** 用于表示对象之间的通信路径。
    
439.在协作图中，**消息** 用于表示对象之间的消息传递。
    
440.协作图中的 **序号** 用于表示消息的发送顺序。
    
441.在 UML 中，**状态图** 用于描述系统的状态变化，强调对象的生命周期。
    
442.状态图中的 **初始状态** 用于表示对象的初始状态。
    
443.在状态图中，**终止状态** 用于表示对象的终止状态。
    
444.状态图中的 **转移** 用于表示从一个状态到另一个状态的转换。
    
445.在状态图中，**活动** 用于表示在某个状态下执行的动作。
    
446.在 UML 中，**活动图** 用于描述系统的活动流程，强调动态行为和工作流。
    
447.活动图中的 **判断节点** 用于表示流程的分支条件。
    
448.在活动图中，**初始节点** 用于表示流程的起点。
    
449.活动图中的 **终止节点** 用于表示流程的终点。
    
450.在 UML 中，**组件图** 用于描述系统的物理结构和组件之间的关系。
    
451.组件图中的 **组件** 用于表示系统的物理模块。
    
452.在组件图中，**接口** 用于表示组件之间的接口。
    
453.组件图中的 **依赖关系** 用于表示组件之间的依赖关系。
    
454.在 UML 中，**部署图** 用于描述系统的硬件架构和软件部署情况。
    
455.部署图中的 **节点** 用于表示物理计算节点。
    
456.在部署图中，**构件** 用于表示部署在节点上的软件构件。
    
457.RUP 中的 **细化阶段** 阶段主要进行系统的架构设计和风险评估。
    
458.RUP 的核心工作流中的 **测试工作流** 工作流主要关注系统的测试和验证。
    
459.在 RUP 中，**里程碑** 用于衡量项目的进展和质量。
    
460.在 UML 中，**用例图** 用于描述系统的功能需求和用户交互。
    
461.用例图中的 **参与者图标** 用于表示系统的参与者。
    
462.在用例图中，**关联线** 用于表示参与者与用例之间的关系。
    
463.用例图中的 **扩展关系** 关系用于表示一个用例扩展另一个用例的功能。
    
464.在用例图中，**泛化箭头** 用于表示用例之间的泛化关系。
    
465.在 UML 中，**类图** 用于描述系统的静态结构，包括类及其关系。
    
466.类图中的 **类元素** 用于表示类的属性和方法。
    
467.在类图中，**关联多重性** 用于表示类之间的关联类型和数量。
    
468.类图中的 **聚合** 用于表示类之间的整体与部分关系。
    
469.在类图中，**泛化** 用于表示类之间的继承关系。
    
470.在 UML 中，**顺序图** 用于描述系统的动态交互，强调消息的传递顺序。
    
471.顺序图中的**生命线**用于表示对象的生存期。