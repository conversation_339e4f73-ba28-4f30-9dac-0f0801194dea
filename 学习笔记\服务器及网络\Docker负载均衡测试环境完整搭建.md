# Docker负载均衡测试环境完整搭建

## 1. 项目结构

```
loadbalancer-test/
├── docker-compose.yml          # Docker编排文件
├── nginx/
│   └── nginx.conf             # Nginx配置文件
├── web-app/
│   ├── Dockerfile             # Web应用镜像
│   ├── app.js                 # Node.js应用
│   └── package.json           # 依赖配置
└── test-scripts/
    ├── test-loadbalancer.sh   # 测试脚本
    └── monitor.sh             # 监控脚本
```

## 2. 创建Web应用

### 2.1 创建Node.js应用 (`web-app/app.js`)

```javascript
const express = require('express');
const os = require('os');

const app = express();
const PORT = process.env.PORT || 3000;
const SERVER_ID = process.env.SERVER_ID || 'unknown';
const SERVER_COLOR = process.env.SERVER_COLOR || '#007bff';

// 简单的内存存储来模拟有状态应用
let visitCount = 0;
let serverStartTime = new Date();

app.use(express.json());

// 主页路由
app.get('/', (req, res) => {
    visitCount++;
    
    const serverInfo = {
        serverId: SERVER_ID,
        hostname: os.hostname(),
        visitCount: visitCount,
        timestamp: new Date().toISOString(),
        uptime: Math.floor((Date.now() - serverStartTime) / 1000),
        clientIP: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
        userAgent: req.headers['user-agent']
    };
    
    // 返回HTML页面，方便浏览器测试
    res.send(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>负载均衡测试 - 服务器 ${SERVER_ID}</title>
            <style>
                body { 
                    font-family: Arial, sans-serif; 
                    background-color: ${SERVER_COLOR}20;
                    padding: 20px;
                    text-align: center;
                }
                .server-info { 
                    background: white;
                    border-left: 5px solid ${SERVER_COLOR};
                    margin: 20px auto;
                    padding: 20px;
                    max-width: 600px;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }
                .highlight { color: ${SERVER_COLOR}; font-weight: bold; }
                .refresh-btn {
                    background: ${SERVER_COLOR};
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                    margin-top: 10px;
                }
            </style>
        </head>
        <body>
            <h1>🚀 负载均衡测试环境</h1>
            <div class="server-info">
                <h2>服务器信息</h2>
                <p><strong>服务器ID:</strong> <span class="highlight">${SERVER_ID}</span></p>
                <p><strong>容器主机名:</strong> ${serverInfo.hostname}</p>
                <p><strong>本服务器访问次数:</strong> ${serverInfo.visitCount}</p>
                <p><strong>服务器运行时间:</strong> ${serverInfo.uptime} 秒</p>
                <p><strong>客户端IP:</strong> ${serverInfo.clientIP}</p>
                <p><strong>访问时间:</strong> ${serverInfo.timestamp}</p>
            </div>
            <button class="refresh-btn" onclick="location.reload()">刷新测试</button>
            
            <script>
                // 每5秒自动刷新一次
                setTimeout(() => location.reload(), 5000);
            </script>
        </body>
        </html>
    `);
});

// API接口，返回JSON数据
app.get('/api/info', (req, res) => {
    visitCount++;
    res.json({
        serverId: SERVER_ID,
        hostname: os.hostname(),
        visitCount: visitCount,
        timestamp: new Date().toISOString(),
        uptime: Math.floor((Date.now() - serverStartTime) / 1000)
    });
});

// 健康检查接口
app.get('/health', (req, res) => {
    res.json({ 
        status: 'healthy', 
        serverId: SERVER_ID,
        timestamp: new Date().toISOString()
    });
});

// 模拟高负载接口（用于测试）
app.get('/heavy', (req, res) => {
    const start = Date.now();
    // 模拟CPU密集任务
    let result = 0;
    for (let i = 0; i < 1000000; i++) {
        result += Math.random();
    }
    
    res.json({
        serverId: SERVER_ID,
        processingTime: Date.now() - start,
        result: result,
        timestamp: new Date().toISOString()
    });
});

app.listen(PORT, '0.0.0.0', () => {
    console.log(`🚀 服务器 ${SERVER_ID} 启动成功！`);
    console.log(`   监听端口: ${PORT}`);
    console.log(`   容器主机名: ${os.hostname()}`);
    console.log(`   启动时间: ${serverStartTime.toISOString()}`);
});
```

### 2.2 包依赖文件 (`web-app/package.json`)

```json
{
  "name": "loadbalancer-test-app",
  "version": "1.0.0",
  "description": "负载均衡测试应用",
  "main": "app.js",
  "scripts": {
    "start": "node app.js"
  },
  "dependencies": {
    "express": "^4.18.2"
  },
  "author": "LoadBalancer Test",
  "license": "MIT"
}
```

### 2.3 Docker镜像文件 (`web-app/Dockerfile`)

```dockerfile
FROM node:18-alpine

WORKDIR /app

# 复制package.json并安装依赖
COPY package.json .
RUN npm install --production

# 复制应用代码
COPY app.js .

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["npm", "start"]
```

## 3. Nginx配置

### 3.1 负载均衡配置 (`nginx/nginx.conf`)

```nginx
# 全局配置
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    # 基础配置
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'upstream_addr=$upstream_addr '
                    'upstream_status=$upstream_status '
                    'upstream_response_time=$upstream_response_time '
                    'request_time=$request_time';
    
    access_log /var/log/nginx/access.log main;
    
    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # =================== 负载均衡配置 ===================
    
    # 后端服务器组1：轮询算法
    upstream web_backend_round_robin {
        server web1:3000 weight=1;
        server web2:3000 weight=1;
        server web3:3000 weight=1;
        
        # 健康检查
        # max_fails=2 fail_timeout=30s;
    }
    
    # 后端服务器组2：加权轮询
    upstream web_backend_weighted {
        server web1:3000 weight=3;  # 权重高，处理更多请求
        server web2:3000 weight=2;
        server web3:3000 weight=1;
    }
    
    # 后端服务器组3：IP哈希（会话保持）
    upstream web_backend_ip_hash {
        ip_hash;
        server web1:3000;
        server web2:3000;
        server web3:3000;
    }
    
    # 后端服务器组4：最少连接
    upstream web_backend_least_conn {
        least_conn;
        server web1:3000;
        server web2:3000;
        server web3:3000;
    }
    
    # =================== 虚拟主机配置 ===================
    
    # 默认轮询负载均衡
    server {
        listen 80;
        server_name localhost;
        
        location / {
            proxy_pass http://web_backend_round_robin;
            include /etc/nginx/proxy_params;
        }
        
        # API接口
        location /api/ {
            proxy_pass http://web_backend_round_robin;
            include /etc/nginx/proxy_params;
            
            # API特殊设置
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }
    }
    
    # 加权轮询测试
    server {
        listen 8081;
        server_name localhost;
        
        location / {
            proxy_pass http://web_backend_weighted;
            include /etc/nginx/proxy_params;
        }
    }
    
    # IP哈希测试（会话保持）
    server {
        listen 8082;
        server_name localhost;
        
        location / {
            proxy_pass http://web_backend_ip_hash;
            include /etc/nginx/proxy_params;
        }
    }
    
    # 最少连接测试
    server {
        listen 8083;
        server_name localhost;
        
        location / {
            proxy_pass http://web_backend_least_conn;
            include /etc/nginx/proxy_params;
        }
    }
    
    # Nginx状态监控
    server {
        listen 8080;
        server_name localhost;
        
        location /nginx_status {
            stub_status on;
            access_log off;
            allow all;  # 生产环境应限制访问
        }
        
        location / {
            return 200 "Nginx Load Balancer Status Page\n";
            add_header Content-Type text/plain;
        }
    }
}

# 代理参数配置文件内容
# 这些参数会被include到各个location中
```

### 3.2 代理参数文件 (`nginx/proxy_params`)

```nginx
proxy_set_header Host $http_host;
proxy_set_header X-Real-IP $remote_addr;
proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
proxy_set_header X-Forwarded-Proto $scheme;

proxy_connect_timeout 5s;
proxy_send_timeout 60s;
proxy_read_timeout 60s;

proxy_buffering on;
proxy_buffer_size 4k;
proxy_buffers 8 4k;

proxy_http_version 1.1;
proxy_set_header Connection "";

# 错误重试
proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
proxy_next_upstream_tries 2;
proxy_next_upstream_timeout 10s;
```

## 4. Docker Compose编排

### 4.1 主编排文件 (`docker-compose.yml`)

```yaml
version: '3.8'

services:
  # Nginx负载均衡器
  nginx:
    image: nginx:alpine
    container_name: lb-nginx
    ports:
      - "80:80"      # 主负载均衡
      - "8080:8080"  # 状态监控
      - "8081:8081"  # 加权轮询测试
      - "8082:8082"  # IP哈希测试
      - "8083:8083"  # 最少连接测试
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/proxy_params:/etc/nginx/proxy_params:ro
      - nginx-logs:/var/log/nginx
    depends_on:
      - web1
      - web2
      - web3
    networks:
      - lb-network
    restart: unless-stopped

  # Web服务器1
  web1:
    build: ./web-app
    container_name: lb-web1
    environment:
      - SERVER_ID=WEB-01
      - SERVER_COLOR=#ff6b6b
      - PORT=3000
    networks:
      - lb-network
    restart: unless-stopped

  # Web服务器2  
  web2:
    build: ./web-app
    container_name: lb-web2
    environment:
      - SERVER_ID=WEB-02
      - SERVER_COLOR=#4ecdc4
      - PORT=3000
    networks:
      - lb-network
    restart: unless-stopped

  # Web服务器3
  web3:
    build: ./web-app
    container_name: lb-web3
    environment:
      - SERVER_ID=WEB-03
      - SERVER_COLOR=#45b7d1
      - PORT=3000
    networks:
      - lb-network
    restart: unless-stopped

networks:
  lb-network:
    driver: bridge
    name: loadbalancer-network

volumes:
  nginx-logs:
    name: nginx-logs
```

## 5. 测试脚本

### 5.1 负载均衡测试脚本 (`test-scripts/test-loadbalancer.sh`)

```bash
#!/bin/bash

echo "🚀 开始负载均衡测试..."
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_endpoint() {
    local name="$1"
    local url="$2"
    local requests="$3"
    
    echo -e "${BLUE}📊 测试 $name${NC}"
    echo "URL: $url"
    echo "请求次数: $requests"
    echo "----------------------------------------"
    
    for i in $(seq 1 $requests); do
        response=$(curl -s "$url/api/info" | jq -r '.serverId // "ERROR"')
        echo -e "请求 $i: ${GREEN}$response${NC}"
        sleep 1
    done
    
    echo ""
}

# 检查jq是否安装
if ! command -v jq &> /dev/null; then
    echo -e "${RED}❌ 需要安装jq来解析JSON响应${NC}"
    echo "Ubuntu/Debian: sudo apt-get install jq"
    echo "macOS: brew install jq"
    exit 1
fi

# 检查服务是否启动
echo -e "${YELLOW}🔍 检查服务状态...${NC}"
if ! curl -s http://localhost:80/health > /dev/null; then
    echo -e "${RED}❌ 服务未启动，请先运行: docker-compose up -d${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 服务正常运行${NC}"
echo ""

# 测试不同的负载均衡算法
test_endpoint "轮询算法 (Round Robin)" "http://localhost:80" 10
test_endpoint "加权轮询 (Weighted)" "http://localhost:8081" 10  
test_endpoint "IP哈希 (IP Hash)" "http://localhost:8082" 10
test_endpoint "最少连接 (Least Connections)" "http://localhost:8083" 10

# 并发测试
echo -e "${BLUE}🔥 并发测试 (10个并发请求)${NC}"
echo "----------------------------------------"
for i in $(seq 1 10); do
    curl -s "http://localhost:80/api/info" | jq -r '.serverId' &
done
wait
echo ""

# 性能测试
echo -e "${BLUE}⚡ 性能测试 (高负载接口)${NC}"
echo "----------------------------------------"
for i in $(seq 1 5); do
    start_time=$(date +%s.%N)
    response=$(curl -s "http://localhost:80/heavy")
    end_time=$(date +%s.%N)
    duration=$(echo "$end_time - $start_time" | bc)
    server_id=$(echo "$response" | jq -r '.serverId')
    processing_time=$(echo "$response" | jq -r '.processingTime')
    
    echo -e "请求 $i: 服务器=${GREEN}$server_id${NC}, 总时间=${YELLOW}${duration}s${NC}, 处理时间=${YELLOW}${processing_time}ms${NC}"
done

echo ""
echo -e "${GREEN}✅ 测试完成！${NC}"
echo ""
echo -e "${YELLOW}💡 提示：${NC}"
echo "- 访问 http://localhost 查看Web界面"
echo "- 访问 http://localhost:8080/nginx_status 查看Nginx状态"
echo "- 使用 docker-compose logs -f 查看实时日志"
```

### 5.2 监控脚本 (`test-scripts/monitor.sh`)

```bash
#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}📊 负载均衡监控面板${NC}"
echo "==============================="

# 实时监控函数
monitor_services() {
    while true; do
        clear
        echo -e "${BLUE}📊 负载均衡监控面板 - $(date)${NC}"
        echo "==============================="
        
        # 检查容器状态
        echo -e "${YELLOW}🐳 容器状态:${NC}"
        docker-compose ps
        echo ""
        
        # 检查Nginx状态
        echo -e "${YELLOW}📈 Nginx状态:${NC}"
        curl -s http://localhost:8080/nginx_status 2>/dev/null || echo "Nginx状态获取失败"
        echo ""
        
        # 检查各服务器健康状态
        echo -e "${YELLOW}❤️ 服务器健康检查:${NC}"
        for port in 80 8081 8082 8083; do
            status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:$port/health)
            if [ "$status" = "200" ]; then
                echo -e "端口 $port: ${GREEN}✅ 健康${NC}"
            else
                echo -e "端口 $port: ${RED}❌ 异常 (HTTP $status)${NC}"
            fi
        done
        echo ""
        
        # 显示最近的访问日志
        echo -e "${YELLOW}📋 最近访问日志:${NC}"
        docker exec lb-nginx tail -n 5 /var/log/nginx/access.log 2>/dev/null | tail -5
        echo ""
        
        echo -e "${BLUE}按 Ctrl+C 退出监控${NC}"
        sleep 5
    done
}

# 启动监控
monitor_services
```

## 6. 快速启动指南

### 6.1 创建项目目录

```bash
# 创建项目根目录
mkdir loadbalancer-test
cd loadbalancer-test

# 创建子目录
mkdir -p nginx web-app test-scripts

# 创建所有必要的文件
touch docker-compose.yml
touch nginx/nginx.conf
touch nginx/proxy_params
touch web-app/Dockerfile
touch web-app/app.js
touch web-app/package.json
touch test-scripts/test-loadbalancer.sh
touch test-scripts/monitor.sh
```

### 6.2 启动步骤

```bash
# 1. 进入项目目录
cd loadbalancer-test

# 2. 构建并启动所有服务
docker-compose up -d --build

# 3. 查看服务状态
docker-compose ps

# 4. 查看日志
docker-compose logs -f

# 5. 运行测试脚本（需要安装jq）
chmod +x test-scripts/*.sh
./test-scripts/test-loadbalancer.sh

# 6. 运行监控（可选）
./test-scripts/monitor.sh
```

### 6.3 测试访问点

```bash
# Web界面测试
curl http://localhost          # 轮询算法
curl http://localhost:8081     # 加权轮询
curl http://localhost:8082     # IP哈希
curl http://localhost:8083     # 最少连接

# API接口测试  
curl http://localhost/api/info
curl http://localhost/health
curl http://localhost/heavy

# Nginx状态
curl http://localhost:8080/nginx_status
```

## 7. 扩展实验

### 7.1 动态扩缩容测试

```bash
# 扩展到5个Web服务器
docker-compose up -d --scale web1=2 --scale web2=2 --scale web3=1

# 缩减服务器
docker-compose stop web3

# 查看效果
./test-scripts/test-loadbalancer.sh
```

### 7.2 故障恢复测试

```bash
# 停止一个服务器
docker-compose stop web2

# 测试负载均衡是否正常
curl http://localhost/api/info

# 恢复服务器
docker-compose start web2
```

### 7.3 性能压测

```bash
# 使用ab进行压力测试
ab -n 1000 -c 10 http://localhost/

# 使用wrk进行压力测试
wrk -t12 -c400 -d30s http://localhost/
```

## 8. 清理环境

```bash
# 停止所有服务
docker-compose down

# 删除卷和网络
docker-compose down -v

# 清理镜像
docker system prune -f
```

## 9. 故障排查

### 常见问题解决：

1. **端口被占用**
    
    ```bash
    # 检查端口占用
    sudo netstat -tlnp | grep :80
    
    # 修改docker-compose.yml中的端口映射
    ```
    
2. **容器无法通信**
    
    ```bash
    # 检查网络
    docker network ls
    docker network inspect loadbalancer-network
    ```
    
3. **Nginx配置错误**
    
    ```bash
    # 测试配置
    docker exec lb-nginx nginx -t
    
    # 重新加载配置
    docker exec lb-nginx nginx -s reload
    ```
    

这个完整的测试环境让你可以：

- 🎯 理解不同负载均衡算法的区别
- 🔧 实践Nginx配置优化
- 📊 监控服务器性能和状态
- 🚀 测试高可用性和故障恢复
- 📈 进行性能压力测试

所有代码都可以直接使用，是一个完整的学习和实验平台！