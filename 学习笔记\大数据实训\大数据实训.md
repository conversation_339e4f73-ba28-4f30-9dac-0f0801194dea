# 启动的时候出现网卡消失
	networkmanager和network发生冲突
	解决方法：
		systemctl disable NetworkManager
		service restart network
		systemctl enable network



## mysql启动失败
	解决方法：
	mkdir /var/run/mysqld
	chown mysql:mysql /var/run/mysqld
	systemctl start mysqld

![[Pasted image 20250610143204.png]]

	运行/opt/mysql_init/下面的initdir.sh脚本
每次启动都要执行：有更好的办法。有的兄弟：
1. **创建配置文件**  
    在 /etc/tmpfiles.d/ 目录下创建一个新的配置文件，文件名可以随意，但最好以 .conf 结尾，例如 mysql.conf。
    
    ```
    sudo vi /etc/tmpfiles.d/mysql.conf
    ```
    
2. **写入配置内容**  
    在该文件中添加以下一行内容。这行配置告诉 systemd：
    
    - d: 创建一个目录 (directory)。
        
    - /var/run/mysqld: 目录的路径。
        
    - 0755: 目录的权限。
        
    - mysql: 目录的属主用户。
        
    - mysql: 目录的属主组。
        
    - -: 不进行基于时间的清理 (age)。
        
    
    ```
    d /var/run/mysqld 0755 mysql mysql -
    ```
    
    保存并退出文件。
    
3. **验证（可选）**  
    您可以立即执行此配置来创建目录，以验证配置是否正确，而无需重启。
    
    ```
    # 如果目录已存在，先手动删除测试
    # sudo rm -rf /var/run/mysqld
    
    # 执行创建
    sudo systemd-tmpfiles --create /etc/tmpfiles.d/mysql.conf
    
    # 检查结果
    ls -ld /var/run/mysqld
    ```
    
您应该能看到目录被正确创建，并且权限和所有权都设置好了。
    

现在，每次系统启动时，systemd 都会自动处理这个任务，比 MySQL 服务启动得更早，完美解决问题


## pom导包问题：htrace
![[20ae221d-1402-4837-970f-d4823198cb4f.png]]

解决：路径中不要出现中文以及空格

找不到依赖

![[c1fe787120b23999df9b4c4105f2e7b5.png]]

解决方法：将库添加为模块

或者直接添加maven依赖（省事
![[Pasted image 20250613034642.png]]



sql语句的表名不正确
