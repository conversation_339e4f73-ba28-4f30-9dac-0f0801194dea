# 整合版考试题目

## 一、单选题 (共30题)

1. 下列不属于软件工程的目标的一项是（ C ）。
    
    A.提高软件产品的质量
    
    B.提高软件产品的可靠性
    
    C.减少软件产品的需求
    
    D.控制软件产品的开发成本
    
2. UML主要应用于（ D ）。
    
    A.基于螺旋模型的结构化开发方法
    
    B.基于需求动态定义的原型化方法
    
    C.基于数据的数据流开发方法
    
    D.基于对象的面向对象的方法
    
3. 下列UML图中不属于结构图的一项是（ D ）。
    
    A.类图
    
    B.对象图
    
    C.组件图
    
    D.顺序图
    
4. 下列关于生命线的说法不正确的是（ B ）。
    
    A.生命线是一条垂直的虚线，用来表示顺序图中的对象在一段时间内存在
    
    B.在顺序图中，每个对象的底部中心的位置都带有生命线
    
    C.在顺序图中，生命线是一条时间线，从顺序图的顶部一直延伸到底部，所用时间取决于交互持续的时间，即生命线表现了对象存在的时段
    
    D.顺序图中的所有对象在程序一开始运行的时候，其生命线都必须存在
    
5. 下列关于包的用途，说法不正确的是（ C ）。
    
    A.描述需求和设计的高层概况
    
    B.组织源代码
    
    C.细化用例表达
    
    D.将复杂系统在逻辑层面上模块化
    
6. 在下列选项中，包元素之间可能形成的关系是（ B ）。
    
    A.关联关系
    
    B.依赖关系
    
    C.实现关系
    
    D.扩展关系
    
7. 下列关于顺序图的说法不正确的是（ D ）。
    
    A.顺序图是对象之间传送消息时间顺序的可视化表示
    
    B.顺序图比较详细地描述了用例表达的需求
    
    C.顺序图的目的在于描述系统中各个对象按照时间顺序的交互
    
    D.在顺序图中，消息表示一组在对象间传送的数据，不能代表调用
    
8. 顺序图中的消息是以（ A ）顺序排列的。
    
    A.时间
    
    B.调用
    
    C.发送者
    
    D.接收者
    
9. 下列不是状态机图组成要素的是（ D ）。
    
    A.状态
    
    B.转移
    
    C.初始状态
    
    D.组件
    
10. 假设一个转换被表示为“A[B]/C”，那么这个转换所表达的语义是（ B ）。
    
    A.该转换的触发器事件为B，监护条件为A，效果列表为C
    
    B.该转换的触发器事件为A，监护条件为B,效果列表为C
    
    C.该转换的触发器事件为C，监护条件为A，效果列表为B
    
    D.该转换的触发器事件为A，监护条件为C，效果列表为B
    
11. 下列选项中不属于面向对象方法的优势之一的是（ C ）。
    
    A.复用性强
    
    B.改善了软件结构
    
    C.软件的执行效率更高
    
    D.抽象更符合人类的思维习惯
    
12. 下列表述中不属于UML的目标的是（ C ）
    
    A.为建模者提供可用的、富有表达力的、可视化的建模语言
    
    B.支持独立于编程语言和开发过程的规范
    
    C.成为一门独立的编程语言
    
    D.推动面向对象建模工具市场的成长
    
13. 当需要表示某个元素的特性信息时，可以使用（ D ）
    
    A.约束
    
    B.构造型
    
    C.注释
    
    D.标记值
    
14. 下面不是用例图组成要素的是（ C ）。
    
    A．用例
    
    B.参与者
    
    C.泳道
    
    D.系统边界
    
15. 下列说法中，不正确的是（ B ）。
    
    A.用例和参与者之间的对应关系是关联关系，它表示参与者使用了系统的用例
    
    B.参与者指的是人，不能是子系统和时间等概念
    
    C.特殊需求指的是一个用例的非功能需求和设计约束
    
    D.在扩展关系中，基础用例提供了一个或多个插入点，扩展用例在这些插入点中提供了另外的行为
    
16. 下列对类和接口的描述不正确的是（ D ）。
    
    A.当使用子类去替换一处父类时，设计良好的软件应当可以正确实现功能
    
    B.接口的方法名必须是公开(public)的
    
    C.一个类可以实现多个接口
    
    D.当一个类拥有另外一个类的全部属性和方法的时候，它们之间是实现关系
    
17. 在下列选项中不属于面向对象的设计原则的是（ C ）。
    
    A.里氏替换原则
    
    B.单一职责原则
    
    C.后进先出原则
    
    D.接口分离原则
    
18. 下列选项中，不能直接放在包中的元素是（ B ）。
    
    A.类
    
    B.操作
    
    C.包
    
    D.对象图
    
19. 假设有两个包A与B，其中B包依赖于A包，且二者之间不构成任何嵌套关系。
    
    此外，A包中含有三个类元素：
    
    ① ClassA,可见性修饰为public。
    
    ②ClassB,可见性修饰为protected。
    
    ③ClassC,可见性修饰为privatte。
    
    那么在B包中可见的元素有（ A ）。
    
    A.①
    
    B.①②
    
    C.①②③
    
    D.②
    
20. 顺序图的作用有（ B ）。
    
    A.确认和丰富一个使用语境的逻辑表达
    
    B.细化用例的表达
    
    C.有效地描述如何分配各个类的职责，以及这些类具有相应职责的原因
    
    D.显示在交互过程中各个对象之间的组织交互关系以及对象彼此之间的连接
    
21. 下列选项中不是面向对象方法的相关原则的是( D )。
    
    A.封装
    
    B.继承
    
    C.多态
    
    D.结构
    
22. 在UML中表示一般事物与特殊事物之间的关系是 ( B )。
    
    A.关联关系
    
    B.泛化关系
    
    C.依赖关系
    
    D.实现关系
    
23. 下列关于顺序图的说法不正确的是 ( C )。
    
    A.顺序图是对象之间传送消息时间顺序的可视化表示
    
    B.顺序图比较详细地描述了用例表达的需求
    
    C.在顺序图中，消息表示一组在对象间传送的数据，不能代表调用
    
    D.顺序图的目的在于描述系统中各个对象按照时间顺序的交互
    
24. 下列关于统一软件开发过程(RUP)的说法正确的是 ( C )。
    
    A.RUP不能用二维图描述
    
    B.在描述RUP时，横轴代表不同的核心工作流，纵轴代表不同的阶段
    
    C.RUP四个主要阶段分别为起始阶段、细化阶段、构建阶段和转化阶段
    
    D.RUP的核心工作流有九种，分为七个工程工作流、两个辅助工作流
    
25. "假设在某个状态的内部的一行内容表示为“eventA/defer”,则这行内容所表示的是( A )。"
    
    A.可推迟事件
    
    B.触发器
    
    C.内部转换
    
    D.内部执行活动
    
26. 下列不属于通信图的主要构成的是( A )。
    
    A.状态
    
    B.链
    
    C.对象
    
    D.消息
    
27. 在活动图中，用于表示并发的节点是( D )。
    
    A.对象流
    
    B.分叉节点与合并节点
    
    C.合并节点与泳道
    
    D.分叉节点与结合节点
    
28. 软件部署的实质是( A )。
    
    A.部署软件制品
    
    B.部署软件模型
    
    C.部署软件程序
    
    D.部署软件组件
    
29. 下列关于迭代过程的说法正确的是( C )。
    
    A.一个完整的循环开发过程由多次迭代组成
    
    B.RUP的每一个阶段是原子的，不可以进一步分解为多次迭代
    
    C.与瀑布模型相比，迭代过程可以实现更高层次的复用
    
    D.迭代过程的缺点在于不能较早地弱化风险
    
30. 包图的组成不包括( B )。
    
    A.包中含有的元素
    
    B.包间的消息和发送者
    
    C.包与包之间的关系
    
    D.包的名称和构造型
    


## 二、判断题 (共30题)

1. 类图主要通过系统中的类及类之间的关系来描述系统的动态结构。 (F)
    
2. 任何一个类都必须具有一定数量的属性与操作。 (F)
    
3. 接口中的操作不应该包含其具体实现。 (T)
    
4. 用例的包含关系与扩展关系在表示法上相似，都是将虚线箭头从基用例指向包含用例（扩展用例）。 (F)
    
5. 参与者位于系统边界外，并不是系统的一部分。 (T)
    
6. 在用例图中，一个参与者一定对应于现实中的某个特定对象。 (F)
    
7. 用例图中的参与者可能对应于现实世界中的人，也可能是其他与系统有交互的事物。 (T)
    
8. 参与者就是那些为系统提供输入的人或事物。 (F)
    
9. 在用例图中，用例必须由相应的参与者来发起或执行。 (T)
    
10. 类图主要通过系统中的类及类之间的关系来描述系统的动态结构。 (F)
    
11. 顺序图从时间顺序上显示了交互过程中信息的交换。 (T)
    
12. 顺序图中元素的摆放顺序无关紧要。 (F)
    
13. 顺序图中的对象可以在交互开始时已经存在，也可以在交互过程中才被创建。 (T)
    
14. 在顺序图中，对象的生命线一定会贯穿整个交互过程。 (F)
    
15. 包内元素的可见性表示同一个包内的其他元素对该元素的访问权限。 (F)
    
16. 在UML中，每个元素只能被包含在一个包中。 (T)
    
17. 包之间表示依赖关系的虚线箭头指向被依赖的包的一方。 (T)
    
18. 包中可见性修饰为public的元素表示这些元素可以被项目中的所有包无条件地访问。 (F)
    
19. 当一个对象名表示为“A：B”时，表示这是一个A类的名称为B的对象。 (F)
    
20. 里氏替换原则的主要内容是“父类对于子类应该是完全可替换的”。 (F)
    
21. 泛化关系定义为一个较普通的元素与一个较特殊的元素之间的类元关系。 (T)
    
22. 通信图与顺序图都是用来表示一个交互过程的图。 (T)
    
23. 在统一软件开发过程中，转化阶段是以开发人员为主导的阶段。 (F)
    
24. 信号是对象之间通信的媒介，是一种异步机制。 (T)
    
25. 在顺序图中，激活又称为控制焦点，表示由一个对象调用另一个对象的操作。 (F)
    
26. “4+1”架构中的开发视图将四个视图结合为一个整体。 (F)
    
27. 开闭原则的核心内容是子类对于父类应该是完全可替换的。 (F)
    
28. 内部转换只有源状态而没有目标状态。 (T)
    
29. 阶段和迭代用于描述软件开发过程中随时间进行的动态组织变化。 (T)
    
30. 部署图与组件图都是用来对系统的物理方面进行建模，两者表达的语义是相同的。 (F)
    


## 三、简答题 (共12题)

1. 什么是用例图？用例图有什么作用？
    
    答：用例图是表示一个系统中用例与参与者关系之间的图。它描述了系统中相关的用户和系统对不同用户提供的功能和服务。用例图对系统的动态方面建模，是对系统、子系统和类的行为进行建模的核心。用例图就相当于从用户的视角来描述和建模整个系统，分析系统的功能与行为。用例图通过呈现元素在语境中如何被使用的外部视图，使得系统、子系统和类等概念更加易于探讨和理解。
    
2. 简述类和类之间的关系，说明它们分别用来描述什么情况。
    
    答：类图中的关系包括关联关系、依赖关系、泛化关系和实现关系。关联关系是两个或多个类元之间的关系，它描述了这些类元的实例间的连接。泛化关系定义为一个较普通的元素与一个较特殊的元素之间的类元关系。依赖关系表示的是两个元素之间语义上的连接关系。实现关系用来表示规格说明与实现之间的关系。
    
3. 顺序图中的消息分为哪些？
    
    答：顺序图中的消息包括简单消息以及产生动作的消息（调用、返回、创建、销毁）。根据消息的并发性来区分，消息可以分为同步消息和异步消息两种。
    
4. 简述状态机图的组成要素。
    
    答：状态机图的主要元素是状态，各状态由转移连接在一起。此外，伪状态和复合状态也是其组成元素。
    
5. 什么是实体类、控制类和边界类？
    
    答：边界类是一种用于对系统外部环境与其内部运作之间的交互进行建模的类。控制类是一种对一个或多个用例所特有的控制行为进行建模的类。实体类是用于对必须存储的信息和相关行为建模的类。
    
6. 简述活动图和普通流程图的异同。
    
    答：事实上活动图是在流程图的基础上添加了大量软件工程术语而成的改进版。具体地说，活动图的表达能力包括了逻辑判断、分支甚至并发，所以活动图的表达能力要远高于流程图：流程图仅仅展示一个固定的过程，而活动图可以展示并发和控制分支，并且可以对活动与活动之间信息的流动进行建模。可以说，活动图在表达流程的基础上继承了一部分协作图的特点，即可以适当表达活动之间的关系。
    
7. 顺序图中对象的创建和销毁操作怎样表现？
    
    答：一般地，对象创建于生命线开始时，销毁于生命线终止时。在顺序图中，我们也可以使用带构造型<>和<>的消息来表示对象在交互过程中的创建。
    
8. 谈谈活动图中使用泳道的意义。
    
    答：泳道是将活动中的具体活动按照负责进行该活动的对象进行分区，一条泳道中的所有活动由同一个对象来执行。
    
9. 简述在类图中，实体类、控制类和边界类的应用场景？
    
    答：实体类用于对必须存储的信息和相关行为建模；控制类用于对一个或多个用例所特有的控制行为进行建模；边界类用于对系统外部环境与其内部运作之间的交互进行建模。
    
10. 在统一软件开发过程中，起始阶段的主要任务有哪些？
    
    答：起始阶段是一个项目的开始，在这个阶段中软件开发团队需要建立好业务用例，并确定好该项目的范围，主要包括：识别出所有与系统存在交互的外部实体、在高层次上定义和说明这些交互的本质特征并确定项目标准、风险评估、资源需求估计、阶段计划等。
    
11. 通信图与顺序图的异同点有哪些？
    
    答：相同点：主要元素、表达语义、对象责任相同。不同点：通信图更适用于展示系统中的对象结构，而顺序图擅长表现交互中消息的顺序。通信图侧重于将对象的交互映射到连接它们的链上，顺序图则不表示对象之间的链。顺序图可以显示地表现对象创建与撤销的过程，而通信图中只能通过消息隐式地表现这一点。顺序图可以表示对象的激活情况，而消息图中缺少实践的信息，除了对消息进行解释，无法清晰地表示对象的激活情况。
    
12. 简述活动图中泳道的作用。
    
    答：泳道负责将活动中的具体活动按照负责进行该活动的对象进行分区，一条泳道中的所有活动由同一对象来执行。
    

    

## 四、应用题 (共9题)

1. 某个学生成绩管理系统的部分参与者和用例总结如下。
    
    教务管理人员：
    
    ①登录系统。
    
    ②教师、学生名单管理。
    
    ③学期教学计划管理。
    
    ④成绩管理。
    
    ⑤课程分配，每次课程分配时都必须打印任课通知书。
    
    学生：
    
    ①登录系统。
    
    ②选课。
    
    教师：
    
    ①登录系统。
    
    ②成绩管理，并且可以选择是否生成成绩单。
    
    请根据以上信息画出该系统的用例图。
    
2. 一个公司可以雇佣多个人，某个人在同一时刻只能为一家公司服务。每个公司只有一个总经理，总经理下有多个部门经理管理公司的雇员，公司的雇员只归一个经理管理。请为上面描述的关系建立类模型，注意捕捉类之间的关联并标明类之间的多重性。
    
3. 在一个习题库下，各科老师可以在系统中编写习题及标准答案，并将编写的习题和答案加入题库中，或者从题库中选取一组习题组成向学生布置的作业，并在适当的时间公布答案。学生可以在系统中完成作业，也可以从题库中选择更多的习题练习。老师可以通过系统检查学生的作业，学生可以在老师公布答案后对自己的练习进行核对。阅读这一情境，分析出该系统所包括的实体类并适当添加属性，绘制出分析类图。
    
4. 医院拟引入一款患者监护系统。基本要求是随时接收每个病人的生理信号（脉搏、体温、血压、心电图等），定时记录病人情况，以形成患者日志。当某个病人的生理信号超出医生规定的安全范围时，向值班护士发出警告信息。此外，护士在需要时还可以要求系统打印出某个指定病人的病情报告。
    请根据以上描述，绘制患者监护系统的状态机图。
    
5. 某学生选课系统的查询课程用例如下：学生首先进入选课系统，然后输入要查询的课程名，系统验证输入的课程名是否存在，若存在，则跳转到对应的显示课程信息的页面；若不存在，则给出提示信息，返回选课页面。
    
    请根据以上描述绘制活动图。
![[111.drawio.svg]]
    
6. 在机票预订系统中，使用系统的用户必须先注册一个自己的账号，其过程为输入注册信息、验证信息完整、提交信息、系统进行验证（是否重名等等），如果验证均通过，则注册成功，否则失败。
    
    请根据以上描述，对用户注册用例画出活动图。

![[Pasted image 20250528135238.png]]
    
7. 在某出版系统中，存在出版社、图书、作者三个实体类。绘制简单类图来描述三者之间的关系。
    
8. 对于Vehicle（车）类和Wheel（轮子）类，存在一个一对多的关联关系。试创建一个Vehicle类的三轮车对象，与三个轮子对象存在链，有一个前轮和两个后轮。用对象图表示三轮车对象与轮子对象之间的关系。
    
9. **题目场景：Smart Library智能图书管理系统**
    
    Smart Library是一个智能图书管理系统，意在优化图书馆管理流程，更好地为学生服务。只有管理员和学生可以登录使用系统。图书馆的管理员收到新的图书，会在系统上统一登记录入图书的名字、编号、数量、位置信息。此外，图书可分为杂志、报纸、书籍。管理员也会定期整理图书馆图书，更新图书安放位置，重新统计图书馆图书数量，更新系统信息。同学可以在系统中浏览当前可借图书列表，搜索图书，并前往图书馆的管理员处借书，管理员会更新图书可借数量。还书时，同学们只需把书放在管理台，由管理员统一登记录入更新借书记录。管理员会将书籍状态和借书时长记录在此次借书记录中，更新借书请求状态为“已还”。每天10点，系统会自动导出当日借书情况。
    
    某智能图书管理系统需求如上。已知有一个“更新可借的图书数量”用例，请根据以上需求，完成1~3题。
    
    1. 请绘制Smart Library系统的用例图（请注意绘制关系的方向）。

		![[Pasted image 20250528140216.png]]
        
    2. 请绘制Smart Library系统的类图（不需要绘制关联关系的方向以及关系的多重性，不需要绘制类的方法）。
		![[Pasted image 20250528140524.png]]
        
    3. 请绘制“更新可借的图书数量”用例的顺序图。
		![[借阅.svg]]
        

