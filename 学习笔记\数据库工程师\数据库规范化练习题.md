# 数据库规范化练习题

## 练习1：识别函数依赖

给定以下订单表，识别所有的函数依赖关系：

```
订单表
| 订单号 | 客户ID | 客户姓名 | 客户地址 | 商品ID | 商品名称 | 单价 | 数量 | 订单日期 |
|--------|--------|----------|----------|--------|----------|------|------|----------|
| O001   | C101   | 张三     | 北京市   | P201   | 笔记本   | 5000 | 2    | 2024-01-15 |
| O001   | C101   | 张三     | 北京市   | P202   | 鼠标     | 100  | 5    | 2024-01-15 |
| O002   | C102   | 李四     | 上海市   | P201   | 笔记本   | 5000 | 1    | 2024-01-16 |
```

**任务**：

1. 列出所有的函数依赖
2. 确定候选键
3. 这个表满足哪个范式？

## 练习2：规范化到1NF

将以下表规范化到第一范式：

```
员工表
| 员工号 | 姓名 | 技能 | 项目 |
|--------|------|------|------|
| E001   | 王五 | Java, Python | 项目A, 项目B |
| E002   | 赵六 | C++, Java | 项目B |
```

## 练习3：规范化到2NF

给定以下图书借阅表，将其规范化到第二范式：

```
借阅表
主键：(读者证号, 图书ISBN, 借阅日期)
| 读者证号 | 图书ISBN | 借阅日期 | 读者姓名 | 读者电话 | 书名 | 作者 | 出版社 |
|----------|----------|----------|----------|----------|------|------|--------|
| R001     | ISBN001  | 2024-01-10 | 陈七   | 13800138000 | 数据库原理 | 王教授 | 清华出版社 |
```

## 练习4：规范化到3NF

将以下员工表规范化到第三范式：

```
员工表
| 员工号 | 姓名 | 部门号 | 部门名称 | 部门经理工号 | 部门经理姓名 | 工资等级 | 基本工资 |
|--------|------|--------|----------|--------------|--------------|----------|----------|
| E001   | 张三 | D01    | 销售部   | E100         | 王经理       | 3        | 8000     |
| E002   | 李四 | D01    | 销售部   | E100         | 王经理       | 2        | 6000     |
| E003   | 王五 | D02    | 技术部   | E200         | 李经理       | 3        | 8000     |
```

假设：工资等级 → 基本工资

## 练习5：综合练习

某在线课程平台的数据表如下：

```
课程注册表
| 学生ID | 学生姓名 | 学生邮箱 | 课程ID | 课程名称 | 讲师ID | 讲师姓名 | 讲师职称 | 注册日期 | 完成进度 | 证书编号 | 证书颁发日期 |
|--------|----------|----------|--------|----------|--------|----------|----------|----------|----------|----------|--------------|
| S001   | 张同学   | zhang@email | C101 | Python基础 | T001 | 李老师 | 高级讲师 | 2024-01-01 | 100% | CERT001 | 2024-02-01 |
| S001   | 张同学   | zhang@email | C102 | 数据分析 | T002 | 王老师 | 讲师 | 2024-01-15 | 60% | NULL | NULL |
| S002   | 李同学   | li@email | C101 | Python基础 | T001 | 李老师 | 高级讲师 | 2024-01-10 | 100% | CERT002 | 2024-02-10 |
```

**任务**：

1. 识别所有函数依赖
2. 将此表规范化到3NF
3. 画出规范化后的ER图（用文字描述）

## 练习6：BCNF练习

考虑以下场景的预约表：

```
预约表
| 医生 | 患者 | 时间段 | 诊室 |
|------|------|--------|------|
| 张医生 | 患者A | 9:00-10:00 | 101室 |
| 李医生 | 患者B | 9:00-10:00 | 102室 |
| 张医生 | 患者C | 10:00-11:00 | 101室 |
```

约束条件：

- 一个医生在同一时间只能看一个患者
- 一个诊室在同一时间只能有一个医生使用
- 一个患者在同一时间只能看一个医生

**任务**：

1. 找出所有候选键
2. 这个表是否满足BCNF？如果不满足，请规范化

## 练习7：反规范化决策

某电商平台的订单查询非常频繁，当前的规范化设计需要连接5个表才能显示完整的订单信息：

- 订单表（订单ID, 客户ID, 订单日期, 总金额）
- 订单明细表（订单ID, 商品ID, 数量, 小计）
- 客户表（客户ID, 姓名, 地址, 电话）
- 商品表（商品ID, 商品名, 分类ID, 单价）
- 分类表（分类ID, 分类名）

**任务**：

1. 分析这种设计的优缺点
2. 如果查询性能成为瓶颈，你会如何进行反规范化？
3. 反规范化会带来什么问题？如何解决？

## 练习8：实战案例

设计一个图书馆管理系统的数据库，需要记录：

- 图书信息（ISBN、书名、作者、出版社、出版年份、分类、库存数量）
- 读者信息（读者证号、姓名、类型、院系、联系方式）
- 借阅记录（包括借阅日期、应还日期、实际归还日期）
- 预约记录（读者可以预约已被借出的图书）
- 罚款记录（超期罚款）

**任务**：

1. 设计初始表结构
2. 规范化到3NF
3. 考虑实际使用场景，是否需要适度反规范化？

## 答案提示

完成练习后，可以按以下步骤检查答案：

1. **函数依赖识别**：列出所有"决定"关系

2. **候选键确定**：找出能唯一标识记录的最小属性集

3. 范式检查

   ：

   - 1NF：检查是否有多值属性
   - 2NF：检查是否有部分依赖
   - 3NF：检查是否有传递依赖
   - BCNF：检查决定因素是否都是候选键

4. **规范化实施**：根据依赖关系拆分表

5. **性能考虑**：评估查询复杂度和频率
