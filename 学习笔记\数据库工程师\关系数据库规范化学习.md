# 关系数据库规范化教程

## 目录

1. [概述](https://claude.ai/chat/03fe1a9d-093a-4ed5-95ef-b046021074a9#概述)
2. [为什么需要规范化](https://claude.ai/chat/03fe1a9d-093a-4ed5-95ef-b046021074a9#为什么需要规范化)
3. [函数依赖理论](https://claude.ai/chat/03fe1a9d-093a-4ed5-95ef-b046021074a9#函数依赖理论)
4. [规范化过程](https://claude.ai/chat/03fe1a9d-093a-4ed5-95ef-b046021074a9#规范化过程)
5. [第一范式(1NF)](https://claude.ai/chat/03fe1a9d-093a-4ed5-95ef-b046021074a9#第一范式1nf)
6. [第二范式(2NF)](https://claude.ai/chat/03fe1a9d-093a-4ed5-95ef-b046021074a9#第二范式2nf)
7. [第三范式(3NF)](https://claude.ai/chat/03fe1a9d-093a-4ed5-95ef-b046021074a9#第三范式3nf)
8. [BCNF范式](https://claude.ai/chat/03fe1a9d-093a-4ed5-95ef-b046021074a9#bcnf范式)
9. [第四范式(4NF)](https://claude.ai/chat/03fe1a9d-093a-4ed5-95ef-b046021074a9#第四范式4nf)
10. [第五范式(5NF)](https://claude.ai/chat/03fe1a9d-093a-4ed5-95ef-b046021074a9#第五范式5nf)
11. [反规范化](https://claude.ai/chat/03fe1a9d-093a-4ed5-95ef-b046021074a9#反规范化)
12. [实践案例](https://claude.ai/chat/03fe1a9d-093a-4ed5-95ef-b046021074a9#实践案例)
13. [练习题](https://claude.ai/chat/03fe1a9d-093a-4ed5-95ef-b046021074a9#练习题)
14. [总结与建议](https://claude.ai/chat/03fe1a9d-093a-4ed5-95ef-b046021074a9#总结与建议)

------

## 概述

数据库规范化（Database Normalization）是关系数据库设计的核心理论，旨在消除数据冗余、避免更新异常，并确保数据的一致性和完整性。规范化过程通过将复杂的关系分解为更简单、更合理的关系来实现。

### 规范化的目标

- **消除数据冗余**：减少重复存储的数据
- **避免更新异常**：防止数据不一致
- **提高数据完整性**：确保数据的准确性
- **优化存储空间**：减少磁盘空间使用
- **简化维护**：降低数据维护的复杂性

### 规范化的层次

数据库规范化分为多个层次，从第一范式(1NF)到第五范式(5NF)，每个层次都有特定的要求和目标。

------

## 为什么需要规范化

### 数据异常问题

考虑以下未规范化的学生选课表：

```
StudentCourse
+------+----------+-----+--------+-------+------------+----------+
| SID  | SName    | Age | Major  | CID   | CName      | Credits  |
+------+----------+-----+--------+-------+------------+----------+
| S001 | 张三     | 20  | 计算机 | C001  | 数据库     | 3        |
| S001 | 张三     | 20  | 计算机 | C002  | 算法       | 4        |
| S002 | 李四     | 21  | 数学   | C001  | 数据库     | 3        |
| S003 | 王五     | 19  | 计算机 | C003  | 操作系统   | 3        |
+------+----------+-----+--------+-------+------------+----------+
```

这个表存在以下问题：

#### 1. 插入异常（Insertion Anomaly）

- 无法单独插入学生信息而不选课
- 无法单独插入课程信息而没有学生选择

#### 2. 删除异常（Deletion Anomaly）

- 删除王五的选课记录会丢失操作系统课程的信息
- 删除最后一个选某门课的学生会丢失课程信息

#### 3. 更新异常（Update Anomaly）

- 修改张三的年龄需要更新多条记录
- 修改数据库课程的学分需要更新多条记录
- 容易造成数据不一致

#### 4. 数据冗余（Data Redundancy）

- 学生信息重复存储
- 课程信息重复存储
- 浪费存储空间

------

## 函数依赖理论

函数依赖（Functional Dependency）是规范化理论的基础，用于描述属性之间的约束关系。

### 函数依赖的定义

设R(U)是一个关系模式，X和Y是U的子集。如果对于R的任意一个可能的关系r，r中不可能存在两个元组在X上的属性值相等，而在Y上的属性值不等，则称X函数确定Y，或Y函数依赖于X，记作X→Y。

**通俗理解**：如果知道了X的值，就能唯一确定Y的值。

### 函数依赖的类型

#### 1. 完全函数依赖

如果X→Y，且对于X的任何真子集X'，都有X'↛Y，则称Y完全函数依赖于X。

**示例**：

- (学号,课程号) → 成绩（完全函数依赖）
- 学号 ↛ 成绩，课程号 ↛ 成绩

#### 2. 部分函数依赖

如果X→Y，但Y不完全函数依赖于X，则称Y部分函数依赖于X。

**示例**：

- (学号,课程号) → 学生姓名（部分函数依赖）
- 学号 → 学生姓名

#### 3. 传递函数依赖

如果X→Y，Y→Z，且Y↛X，Y↛Z，则称Z传递函数依赖于X。

**示例**：

- 学号 → 专业，专业 → 院长
- 则学号 → 院长（传递函数依赖）

### 函数依赖的推理规则（Armstrong公理）

#### 基本规则

1. **自反律**：如果Y⊆X，则X→Y
2. **增广律**：如果X→Y，则XZ→YZ
3. **传递律**：如果X→Y，Y→Z，则X→Z

#### 导出规则

1. **合并律**：如果X→Y，X→Z，则X→YZ
2. **分解律**：如果X→YZ，则X→Y，X→Z
3. **伪传递律**：如果X→Y，WY→Z，则XW→Z

------

## 规范化过程

规范化是一个逐步分解的过程，每一步都要求满足更严格的条件。

### 规范化步骤

1. 识别函数依赖关系
2. 找出候选键
3. 检查当前范式级别
4. 根据规范化规则进行分解
5. 验证分解结果

### 分解的基本原则

1. **无损连接分解**：分解后的关系能够通过自然连接恢复原关系
2. **保持函数依赖**：分解后仍能推导出原有的函数依赖关系

------

## 第一范式(1NF)

### 定义

关系R的所有属性都是不可分的原子值，则称关系R满足第一范式。

### 主要要求

1. 每个属性都是原子的（不可再分）
2. 每个属性域中的值都是同一类型
3. 每个属性都有唯一的名字
4. 行和列的顺序无关紧要

### 违反1NF的示例

**违反1NF的表**：

```
Student
+------+----------+-------------------+
| SID  | SName    | Phones            |
+------+----------+-------------------+
| S001 | 张三     | 13812345678,13987654321 |
| S002 | 李四     | 13611111111       |
+------+----------+-------------------+
```

**问题**：Phones字段包含多个电话号码，违反了原子性。

### 转换为1NF

**方法1：扩展为多列**

```
Student
+------+----------+-------------+-------------+
| SID  | SName    | Phone1      | Phone2      |
+------+----------+-------------+-------------+
| S001 | 张三     | 13812345678 | 13987654321 |
| S002 | 李四     | 13611111111 | NULL        |
+------+----------+-------------+-------------+
```

**方法2：分离为多行**

```
Student              StudentPhone
+------+----------+   +------+-------------+
| SID  | SName    |   | SID  | Phone       |
+------+----------+   +------+-------------+
| S001 | 张三     |   | S001 | 13812345678 |
| S002 | 李四     |   | S001 | 13987654321 |
+------+----------+   | S002 | 13611111111 |
                      +------+-------------+
```

------

## 第二范式(2NF)

### 定义

关系R满足1NF，且每个非主属性完全函数依赖于主键，则称关系R满足第二范式。

### 主要要求

1. 满足1NF
2. 消除部分函数依赖
3. 每个非主属性必须完全依赖于主键

### 违反2NF的示例

```
StudentCourse
+------+-------+----------+------------+----------+
| SID  | CID   | SName    | CName      | Grade    |
+------+-------+----------+------------+----------+
| S001 | C001  | 张三     | 数据库     | 85       |
| S001 | C002  | 张三     | 算法       | 90       |
| S002 | C001  | 李四     | 数据库     | 78       |
+------+-------+----------+------------+----------+
```

**函数依赖分析**：

- 主键：(SID, CID)
- SID → SName（部分函数依赖）
- CID → CName（部分函数依赖）
- (SID, CID) → Grade（完全函数依赖）

**问题**：SName和CName部分依赖于主键，违反了2NF。

### 转换为2NF

**分解方案**：

```
Student                Course                 Enrollment
+------+----------+     +-------+----------+  +------+-------+-------+
| SID  | SName    |     | CID   | CName    |  | SID  | CID   | Grade |
+------+----------+     +-------+----------+  +------+-------+-------+
| S001 | 张三     |     | C001  | 数据库   |  | S001 | C001  | 85    |
| S002 | 李四     |     | C002  | 算法     |  | S001 | C002  | 90    |
+------+----------+     +-------+----------+  | S002 | C001  | 78    |
                                              +------+-------+-------+
```

------

## 第三范式(3NF)

### 定义

关系R满足2NF，且每个非主属性都不传递依赖于主键，则称关系R满足第三范式。

### 主要要求

1. 满足2NF
2. 消除传递函数依赖
3. 非主属性之间不能存在函数依赖关系

### 违反3NF的示例

```
Student
+------+----------+--------+----------+
| SID  | SName    | Major  | Dean     |
+------+----------+--------+----------+
| S001 | 张三     | 计算机 | 王教授   |
| S002 | 李四     | 数学   | 李教授   |
| S003 | 王五     | 计算机 | 王教授   |
+------+----------+--------+----------+
```

**函数依赖分析**：

- SID → SName, Major（直接依赖）
- Major → Dean（非主属性间的依赖）
- SID → Dean（传递依赖）

**问题**：Dean传递依赖于SID，违反了3NF。

### 转换为3NF

**分解方案**：

```
Student                   Major
+------+----------+--------+    +--------+----------+
| SID  | SName    | Major  |    | Major  | Dean     |
+------+----------+--------+    +--------+----------+
| S001 | 张三     | 计算机 |    | 计算机 | 王教授   |
| S002 | 李四     | 数学   |    | 数学   | 李教授   |
| S003 | 王五     | 计算机 |    +--------+----------+
+------+----------+--------+
```

------

## BCNF范式

### 定义

关系R满足3NF，且每个决定因素都是候选键，则称关系R满足BCNF（Boyce-Codd Normal Form）。

### 主要要求

1. 满足3NF
2. 每个函数依赖X→Y中，X必须是超键
3. 消除主属性对非主属性的部分和传递依赖

### BCNF与3NF的区别

- 3NF允许非主属性决定主属性
- BCNF不允许任何属性决定主属性（除非它本身是候选键）

### 违反BCNF的示例

```
Teaching
+-------+--------+----------+
| SID   | Course | Teacher  |
+-------+--------+----------+
| S001  | 数学   | 张老师   |
| S002  | 数学   | 张老师   |
| S003  | 物理   | 李老师   |
| S004  | 物理   | 王老师   |
+-------+--------+----------+
```

**函数依赖**：

- (SID, Course) → Teacher
- Teacher → Course（一个老师只教一门课）

**候选键**：(SID, Course) 和 (SID, Teacher)

**问题**：Teacher → Course，但Teacher不是候选键，违反BCNF。

### 转换为BCNF

**分解方案**：

```
TeacherCourse             StudentTeacher
+----------+--------+     +-------+----------+
| Teacher  | Course |     | SID   | Teacher  |
+----------+--------+     +-------+----------+
| 张老师   | 数学   |     | S001  | 张老师   |
| 李老师   | 物理   |     | S002  | 张老师   |
| 王老师   | 物理   |     | S003  | 李老师   |
+----------+--------+     | S004  | 王老师   |
                          +-------+----------+
```

------

## 第四范式(4NF)

### 定义

关系R满足BCNF，且消除了多值依赖，则称关系R满足第四范式。

### 多值依赖

设R(U)是关系模式，X、Y、Z是U的子集，且Z=U-X-Y。如果对R的任一关系r，给定的一对(x,z)值，有一组Y的值与之对应，而不依赖于Z的值，则称Y多值依赖于X，记作X→→Y。

### 违反4NF的示例

```
StudentSkillHobby
+------+----------+----------+
| SID  | Skill    | Hobby    |
+------+----------+----------+
| S001 | Java     | 篮球     |
| S001 | Java     | 游泳     |
| S001 | Python   | 篮球     |
| S001 | Python   | 游泳     |
| S002 | C++      | 阅读     |
+------+----------+----------+
```

**多值依赖**：

- SID →→ Skill（学生的技能与爱好无关）
- SID →→ Hobby（学生的爱好与技能无关）

**问题**：Skill和Hobby之间存在不必要的笛卡尔积。

### 转换为4NF

**分解方案**：

```
StudentSkill              StudentHobby
+------+----------+       +------+----------+
| SID  | Skill    |       | SID  | Hobby    |
+------+----------+       +------+----------+
| S001 | Java     |       | S001 | 篮球     |
| S001 | Python   |       | S001 | 游泳     |
| S002 | C++      |       | S002 | 阅读     |
+------+----------+       +------+----------+
```

------

## 第五范式(5NF)

### 定义

关系R满足4NF，且不存在连接依赖，则称关系R满足第五范式（也称为完美范式PNF）。

### 连接依赖

如果关系R可以无损地分解为多个关系，且只有通过这种特定的分解才能重构原关系，则存在连接依赖。

### 应用场景

5NF主要处理涉及三个或更多实体的多对多关系，在实际应用中较少遇到。

### 示例场景

**供应商-零件-项目关系**：

- 供应商S可以供应零件P
- 零件P可以用于项目J
- 供应商S可以承接项目J

如果存在规则"供应商S能供应项目J所需的零件P，当且仅当S能供应P且P用于J且S承接J"，则需要分解为三个二元关系以满足5NF。

------

## 反规范化

### 什么是反规范化

反规范化是有意违反规范化规则，通过增加冗余数据来提高查询性能的设计技术。

### 反规范化的原因

1. **性能优化**：减少连接操作，提高查询速度
2. **简化查询**：避免复杂的多表连接
3. **减少I/O操作**：一次查询获取所需所有数据
4. **应对高并发**：减少数据库负载

### 反规范化的方法

#### 1. 增加冗余列

```sql
-- 规范化设计
Order(OrderID, CustomerID, OrderDate)
Customer(CustomerID, CustomerName, Address)

-- 反规范化：在Order表中增加CustomerName
Order(OrderID, CustomerID, CustomerName, OrderDate)
```

#### 2. 增加派生列

```sql
-- 增加计算字段
OrderSummary(OrderID, ItemCount, TotalAmount)
```

#### 3. 预连接表

```sql
-- 将常用的连接结果存储为一个表
CustomerOrderView(OrderID, CustomerID, CustomerName, OrderDate, TotalAmount)
```

### 反规范化的代价

1. **数据冗余增加**：存储空间增大
2. **更新复杂性**：需要同时更新多处数据
3. **数据一致性风险**：可能出现数据不一致
4. **维护成本增加**：需要额外的同步机制

------

## 实践案例

### 案例：学生管理系统设计

#### 原始需求

设计一个学生管理系统，需要存储以下信息：

- 学生信息：学号、姓名、年龄、专业、班级
- 课程信息：课程号、课程名、学分、教师
- 选课信息：学生选课及成绩
- 教师信息：教师号、教师名、所属院系

#### 未规范化的设计

```
StudentCourseInfo
+------+------+-----+------+-----+-----+------+--------+-------+------+------+
| SID  |SName | Age |Major |Class| CID |CName |Credits | Grade | TID  |TName |
+------+------+-----+------+-----+-----+------+--------+-------+------+------+
| S001 | 张三 | 20  |计算机| C1  |C001 |数据库|   3    |  85   | T001 |王老师|
| S001 | 张三 | 20  |计算机| C1  |C002 |算法  |   4    |  90   | T002 |李老师|
| S002 | 李四 | 21  |数学  | M1  |C001 |数据库|   3    |  78   | T001 |王老师|
+------+------+-----+------+-----+-----+------+--------+-------+------+------+
```

#### 规范化过程

**第一步：转换为1NF**

- 表已满足1NF（所有属性都是原子值）

**第二步：转换为2NF** 识别函数依赖：

- SID → SName, Age, Major, Class
- CID → CName, Credits, TID, TName
- TID → TName
- (SID, CID) → Grade

存在部分函数依赖，分解为：

```sql
Student(SID, SName, Age, Major, Class)
Course(CID, CName, Credits, TID)
Teacher(TID, TName)
Enrollment(SID, CID, Grade)
```

**第三步：转换为3NF** 检查传递依赖：

- Course表中：CID → TID → TName

进一步分解：

```sql
Student(SID, SName, Age, Major, Class)
Course(CID, CName, Credits, TID)
Teacher(TID, TName, Department)
Enrollment(SID, CID, Grade)
```

**最终的3NF设计**：

```sql
-- 学生表
CREATE TABLE Student (
    SID VARCHAR(10) PRIMARY KEY,
    SName VARCHAR(50) NOT NULL,
    Age INT,
    Major VARCHAR(50),
    Class VARCHAR(20)
);

-- 教师表
CREATE TABLE Teacher (
    TID VARCHAR(10) PRIMARY KEY,
    TName VARCHAR(50) NOT NULL,
    Department VARCHAR(50)
);

-- 课程表
CREATE TABLE Course (
    CID VARCHAR(10) PRIMARY KEY,
    CName VARCHAR(100) NOT NULL,
    Credits INT,
    TID VARCHAR(10),
    FOREIGN KEY (TID) REFERENCES Teacher(TID)
);

-- 选课表
CREATE TABLE Enrollment (
    SID VARCHAR(10),
    CID VARCHAR(10),
    Grade INT,
    PRIMARY KEY (SID, CID),
    FOREIGN KEY (SID) REFERENCES Student(SID),
    FOREIGN KEY (CID) REFERENCES Course(CID)
);
```

------

## 练习题

### 基础练习

**练习1**：判断以下关系是否满足各范式要求

```
Employee
+-----+--------+------------+--------+----------+
| EID | EName  | Department | Salary | Manager  |
+-----+--------+------------+--------+----------+
| E01 | 张三   | IT         | 8000   | 李经理   |
| E02 | 李四   | HR         | 6000   | 王经理   |
| E03 | 王五   | IT         | 7500   | 李经理   |
+-----+--------+------------+--------+----------+
```

**分析**：

- 1NF：满足（所有属性都是原子值）
- 2NF：满足（只有一个主键EID，不存在部分依赖）
- 3NF：不满足（Department → Manager，存在传递依赖）

### 中级练习

**练习2**：将以下关系规范化到3NF

```
StudentProject
+------+--------+------+--------+-------+--------+----------+
| SID  | SName  | PID  | PName  | Hours | Advisor| AdvisorDept |
+------+--------+------+--------+-------+--------+----------+
| S001 | 张三   | P001 | 系统A  | 100   | 王教授 | 计算机   |
| S001 | 张三   | P002 | 系统B  | 80    | 李教授 | 软件     |
| S002 | 李四   | P001 | 系统A  | 120   | 王教授 | 计算机   |
+------+--------+------+--------+-------+--------+----------+
```

**解答**：

1. 识别函数依赖：
   - SID → SName
   - PID → PName, Advisor, AdvisorDept
   - Advisor → AdvisorDept
   - (SID, PID) → Hours
2. 分解方案：

```sql
Student(SID, SName)
Advisor(Advisor, AdvisorDept)
Project(PID, PName, Advisor)
StudentProject(SID, PID, Hours)
```

### 高级练习

**练习3**：分析以下多值依赖并转换为4NF

```
EmployeeSkillLanguage
+-----+--------+----------+
| EID | Skill  | Language |
+-----+--------+----------+
| E01 | Java   | 英语     |
| E01 | Java   | 日语     |
| E01 | Python | 英语     |
| E01 | Python | 日语     |
| E02 | C++    | 英语     |
+-----+--------+----------+
```

**解答**： 存在多值依赖：EID →→ Skill，EID →→ Language

分解为：

```sql
EmployeeSkill(EID, Skill)
EmployeeLanguage(EID, Language)
```

------

## 总结与建议

### 规范化的优势

1. **消除数据冗余**：减少存储空间，降低成本
2. **提高数据一致性**：避免更新异常
3. **简化数据维护**：结构清晰，逻辑简单
4. **提高数据完整性**：通过约束保证数据质量

### 规范化的限制

1. **查询复杂度增加**：需要更多的连接操作
2. **性能可能下降**：多表连接影响查询速度
3. **设计复杂性**：需要深入理解业务逻辑

### 实践建议

#### 1. 渐进式规范化

- 从1NF开始，逐步提升规范化级别
- 根据实际需求选择合适的规范化程度
- 大多数情况下，3NF就足够了

#### 2. 平衡规范化与性能

- 对于读密集的系统，考虑适度反规范化
- 对于写密集的系统，坚持高度规范化
- 使用索引、视图等技术弥补性能损失

#### 3. 业务驱动的设计

- 深入理解业务需求和数据关系
- 识别真正的函数依赖关系
- 考虑未来业务发展的可能性

#### 4. 工具和方法

- 使用ER图辅助设计
- 利用CASE工具进行建模
- 进行设计评审和测试

### 规范化检查清单

**1NF检查**：

- [x] 所有属性都是原子值？
- [x] 没有重复的列？
- [x] 每行都唯一？

**2NF检查**：

- [x] 满足1NF？
- [x] 确定了主键？
- [x] 消除了部分函数依赖？

**3NF检查**：

- [x] 满足2NF？
- [x] 消除了传递函数依赖？
- [x] 非主属性不依赖于其他非主属性？

**BCNF检查**：

- [x] 满足3NF？
- [x] 每个函数依赖的决定因素都是候选键？

### 常见误区

1. **过度规范化**：不考虑性能盲目追求高范式
2. **忽视业务逻辑**：机械应用规范化规则
3. **一刀切**：对所有表都采用相同的规范化程度
4. **忽视维护成本**：只考虑设计时的完美性

数据库规范化是一个需要理论与实践相结合的过程。掌握规范化理论的同时，也要根据具体的业务场景和性能要求做出合理的权衡和选择。通过持续的学习和实践，可以设计出既符合理论要求又满足实际需求的优秀数据库系统。