# 第6章 键值对数据库Redis

## 一、简答题

1、简述键值对数据库的基本原理及其适用场景。
**答案：** **键值对数据库通过键值对存储数据，适用于缓存、会话存储等场景。**

2、简述Redis事务机制（multi/exec/discard/watch）的工作原理及使用场景。
**答案：** **事务机制通过multi/exec实现批处理，watch用于监视键变更。**

3、解释Redis的LRU缓存机制及maxmemory配置的作用。
**答案：** **LRU缓存机制通过maxmemory策略淘汰最久未使用的键。**

4、简述Redis持久化存储机制（RDB/AOF）的优缺点及恢复方法。
**答案：** **RDB是快照持久化，AOF是日志持久化，恢复时优先读取AOF。**

5、描述Redis多副本机制（主从复制及故障转移）的实现原理。
**答案：** **主从复制通过异步同步数据，故障转移由哨兵或集群自动完成。**

## 二、综合题

1、设计一个基于Redis的电商购物车系统，说明其数据模型和核心操作。
**答案：** **数据模型：使用哈希表存储用户购物车，列表存储商品库存。**

2、结合实际案例，分析Redis分片机制（Redis集群方案）的优劣势。
**答案：** **Redis集群通过哈希槽分配数据，支持自动分片和故障转移。**

3、如何利用Redis的发布订阅机制实现分布式系统中的消息队列？
**答案：** **通过publish/subscribe实现跨服务的消息通信，结合持久化保证可靠性。**

## 三、单选题

1、下列关于Redis配置文件参数的描述中，哪一项是正确的（）？
A) daemonize no表示以守护进程方式启动服务
**B) bind 127.0.0.1表示绑定本地IP地址**
C) protected-mode yes表示关闭保护模式
D) maxmemory 100mb表示最大内存限制为100GB

2、Redis默认端口号是（）？
A) 3306
B) 27017
**C) 6379**
D) 8080

3、下列关于Redis列表类型命令的描述中，哪一项是正确的（）？
A) lpush将元素添加到列表尾部
B) rpop移除并返回列表第一个元素
**C) lrange用于获取列表指定范围的元素**
D) llen用于设置列表长度

4、Redis中用于删除键的命令是（）？
**A) del**
B) delete
C) remove
D) drop

## 四、多选题

1、Redis支持的持久化存储方式包括（）？
**A) RDB快照**
**B) AOF日志**
C) LRU缓存
D) 内存快照

2、Redis的集合类型支持的操作包括（）？
**A) sadd**
**B) smembers**
**C) sdiff**
D) zadd

3、Redis集群方案的特点包括（）？
**A) 基于哈希槽分片**
**B) 支持主从复制**
**C) 自动故障转移**
D) 客户端分片

4、Redis客户端管理命令包括（）？
**A) client list**
**B) client kill**
**C) client setname**
**D) client getname**

## 五、填空题

1、Redis的默认配置文件名称是**redis.conf**。
2、Redis中用于获取键类型的命令是**type**。
3、Redis中用于设置键过期时间的命令是**expire**。
4、Redis中用于获取有序集合分值的命令是**zscore**。

## 六、判断题（正确打√，错误打×）

1、Redis的AOF持久化方式实时性优于RDB。（**√**）
2、Redis的主从复制是同步的。（**×**）
3、Redis的SCAN命令可以迭代式扫描键空间。（**√**）
4、Redis的ZADD命令可以添加有序集合元素。（**√**）