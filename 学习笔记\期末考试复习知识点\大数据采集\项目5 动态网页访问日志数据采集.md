# 项目5 动态网页访问日志数据采集

## 1.单选题
(1) Flume中不属于Agent核心组件的是（ ）。
A. Source
B. Channel
**C. syslog**
D. Sink

(2) Flume使用Kafka作为数据源时，通过（ ）设置主题列表。
A. channels
**B. Kafka.topics**
C. kafka.consumer.group.id
D. batchDurationMillis

(3) Flume的内存通道中使用（ ）设置通道中所有事件在内存中的大小总和。
A. type
B. capacity
C. byteCapacityBufferPercentage
**D. byteCapacity**

(4) Kafka生产者脚本中使用（ ）选择压缩编解码器。
A. --bootstrap-server
B. --batch-size
**C. --compression-codec**
D. --producer-property

(5) 下列不属于Hadoop核心组件的是（ ）。
**A. --delete**
B. --describe
C. --drop
D. –remove

6、下列关于Kafka的描述中，哪一项是正确的（）？
A) Kafka是一种关系型数据库
**B) Kafka是一个分布式流处理平台**
C) Kafka主要用于批处理任务
D) Kafka不支持消息持久化

7、下列关于Flume的描述中，哪一项是正确的（）？
A) Flume只能用于收集日志数据
B) Flume不支持自定义Sink
**C) Flume可以将数据导入HDFS**
D) Flume无法扩展

8、以下哪项是Kafka中Partition的主要作用（）？
**A) 提高消息处理的并行度**
B) 减少磁盘I/O操作
C) 实现数据的顺序写入
D) 保证消息的全局有序

9、Flume中的Source组件可以使用哪种方式收集日志数据（）？
A) Exec Source
B) Spooling Directory Source
C) Kafka Source
**D) 以上全部**

10、下列关于Kafka Consumer的说法，正确的是（）？
A) 消费者通过push机制从broker获取消息
B) 同一个消费组内的消费者可以消费相同的消息
**C) 消费者可以通过offset管理来追踪已读取消息的位置**
D) 消费者不能跨多个分区消费

## 2.多选题
1、以下哪些是Kafka的核心组件（）？
**A) Broker**
**B) Topic**
**C) Partition**
**D) ZooKeeper**

2、以下哪些是Flume的特性（）？
**A) 支持多种数据源**
**B) 高可靠性**
C) 不支持事务
**D) 可扩展性**

3、下列关于Kafka Consumer Group的说法，正确的有（）？
**A) 同一个Consumer Group下的消费者共同消费同一个Topic的所有Partition**
**B) 不同Consumer Group之间互不影响**
**C) 在一个Consumer Group内，每个Partition只能被一个消费者消费**
**D) 当消费者数量超过Partition数量时，多余的消费者处于闲置状态**

4、Flume配置文件中包含哪些基本元素（）？
**A) Agent**
**B) Source**
**C) Channel**
**D) Sink**

5、以下哪些拦截器可以在Flume中使用（）？
**A) 时间拦截器(Timestamp Interceptor)**
**B) 主机拦截器(Host Interceptor)**
**C) 正则表达式过滤拦截器(Regex Filtering Interceptor)**
D) JSON解析拦截器(JSON Parsing Interceptor)

## 3.填空题
1、Kafka是一个**高吞吐量、低延迟**的分布式发布订阅消息系统。
2、Flume中的Channel位于Source和Sink之间，用于**缓存**事件。
3、Flume支持的Sink类型包括HDFS Sink、Logger、Null以及**File Roll Sink**等。
4、在Kafka中，Producer向Broker发送消息时，可以通过设置**requiredAcks**来确保消息可靠传输。
5、Flume启动Agent使用的命令是**flume-ng agent --conf $FLUME_HOME/conf --conf-file /root/inspur/code/flume-code/access_log-HDFS-LocalFile.conf --name a1 -Dflume.root.logger=DEBUG,console**。

## 4.判断题
Kafka中使用--replication-factor来表示在创建生产者或增加分区时指定的分区数。（**×**）
Flume实现数据采集的方式非常简单，只需编辑相应的配置文件即可完成特定数据的采集与存储。（**√**）
 Producer表示消费者，用于接收消息。消费者连接到Kafka并接收消息，从而进行相应的业务逻辑处理。（**×**）
Topic表示分区，每个分区属于单个主题。（**×**）
 Kafka是由Apache软件基金会开发的一个开源流处理平台，是一个快速、可扩展的、分布式的、分区的、可复制的且基于ZooKeeper协调的分布式日志系统。（**√**）
Kafka能够实现高吞吐量的数据传输。（**√**）
Flume默认采用Log4j作为其日志记录框架。（**×**）
Kafka允许消费者直接从任意位置开始消费消息。（**√**）
Flume的Avro Source可以接收来自其他Flume节点的数据。（**√**）
Kafka的Consumer需要手动提交偏移量才能更新消费进度。（**×**）

## 5.简答题
(1) Flume中的Source、Channel以及Sink分别表示什么含义？
答案：
- **Source：负责接收或收集数据。它可以是从外部来源（如日志文件、网络流等）获取数据的接口。
- Channel：位于Source和Sink之间，作为一个临时存储区，确保数据能够可靠地从Source传递到Sink。Channels提供了一种机制来缓冲事件直到它们被Sink消费。
- Sink：负责将数据发送到目的地。这可能包括另一个存储系统（如HDFS）、数据库或其他服务。

(2) Kafka中都有哪些核心组件？
答案：
- **Broker：Kafka集群中的服务器实例。每个Broker可以容纳多个Topic的Partition。**
- **Topic：一种类别或名称，用于组织消息。每个Topic由一个或多个Partition组成。**
- **Partition：Topic的细分单元，允许并行处理和存储大量消息。每个Partition都是有序且不可变的消息序列。**
- **Producer：负责发布消息到Kafka的Broker上。Producer可以选择将消息发送到特定的Topic和Partition。**
- **Consumer：从Kafka Broker中拉取消息的应用程序。Consumer可以订阅一个或多个Topic，并根据需要处理这些消息。**
- **ZooKeeper：用于维护和协调Kafka Brokers的状态。它帮助管理和服务发现，例如监控Kafka Broker的健康状况、管理和通知关于Leader选举的信息等。**

3、简述Kafka的基本架构及其工作原理。
答案：
**Kafka由Broker、Topic、Partition组成，Producer向Topic发送消息，Consumer从Topic消费消息。Kafka通过Partition实现了并行处理，提高了系统的吞吐量。

4、解释Flume的工作流程，并说明其中各个组件的作用。
答案：
**Flume的工作流程包括Source接收数据、通过Channel传递给Sink。Source负责收集数据，Channel用于缓存事件，Sink负责将数据输出到目的地。

5、阐述Kafka如何实现消息的高可靠性和容错性。
答案：
**Kafka通过消息复制、Leader选举机制和ISR(In-Sync Replicas)集合来保证高可靠性和容错性。

6、描述Flume中不同类型的Source和Sink的应用场景。
答案：
**如Exec Source适用于实时监控日志文件，Kafka Source可用于从Kafka集群中读取数据；HDFS Sink适合将数据存储至HDFS，而Logger Sink可用于调试。

7、比较Kafka和传统消息队列的区别，并指出它们各自的优缺点。
答案：
**Kafka相比传统消息队列具有更高的吞吐量和更低的延迟，同时支持消息持久化和副本机制，但传统消息队列在某些场景下可能更易于管理和维护。

## 6.综合题
1、结合实际应用场景，设计一个使用Flume和Kafka进行动态网页访问日志数据采集与分析的方案。要求详细说明各组件的配置和功能。
答案：
**设计思路：首先使用Flume的Exec Source监听Web服务器的日志文件变化，然后通过Memory Channel将数据传递给Kafka Sink，最终由Kafka Consumer进行数据处理和分析。