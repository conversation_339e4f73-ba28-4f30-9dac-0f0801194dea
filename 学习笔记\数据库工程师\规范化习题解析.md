# 数据库规范化练习题解析

## 练习1：识别函数依赖

给定以下订单表，识别所有的函数依赖关系：

```
订单表
| 订单号 | 客户ID | 客户姓名 | 客户地址 | 商品ID | 商品名称 | 单价 | 数量 | 订单日期 |
|--------|--------|----------|----------|--------|----------|------|------|----------|
| O001   | C101   | 张三     | 北京市   | P201   | 笔记本   | 5000 | 2    | 2024-01-15 |
| O001   | C101   | 张三     | 北京市   | P202   | 鼠标     | 100  | 5    | 2024-01-15 |
| O002   | C102   | 李四     | 上海市   | P201   | 笔记本   | 5000 | 1    | 2024-01-16 |
```

**解析**：

1. **列出所有的函数依赖**：

   - 订单号 → 客户ID, 客户姓名, 客户地址, 订单日期
     - (O001 → C101, 张三, 北京市, 2024-01-15)
     - (O002 → C102, 李四, 上海市, 2024-01-16)
   - 客户ID → 客户姓名, 客户地址
     - (C101 → 张三, 北京市)
     - (C102 → 李四, 上海市)
   - 商品ID → 商品名称, 单价
     - (P201 → 笔记本, 5000)
     - (P202 → 鼠标, 100)
   - (订单号, 商品ID) → 数量 (这是一个完整的函数依赖，因为数量取决于特定的订单和特定的商品)

   *传递依赖*:

   - 订单号 → 客户ID，并且 客户ID → 客户姓名，所以 订单号 → 客户姓名 (传递依赖)
   - 订单号 → 客户ID，并且 客户ID → 客户地址，所以 订单号 → 客户地址 (传递依赖)

2. **确定候选键**：

   - 能够唯一标识表中的每一条记录的最小属性集。
   - 从数据来看，(订单号, 商品ID) 可以唯一确定一条记录。
   - 因此，候选键是 **(订单号, 商品ID)**。

3. **这个表满足哪个范式？**

   - **1NF**：所有属性都是原子的，不可再分。该表满足1NF。
   - **2NF**：在满足1NF的基础上，所有非主属性完全函数依赖于候选键。
     - 候选键是 (订单号, 商品ID)。
     - 非主属性有：客户ID, 客户姓名, 客户地址, 商品名称, 单价, 订单日期。
     - 客户ID, 客户姓名, 客户地址, 订单日期 部分依赖于 订单号 (候选键的一部分)。例如，订单日期仅由订单号决定，与商品ID无关。
     - 商品名称, 单价 部分依赖于 商品ID (候选键的一部分)。例如，商品名称仅由商品ID决定，与订单号无关。
     - 由于存在部分函数依赖，该表**不满足2NF**。
   - 既然不满足2NF，那么它也不满足3NF和BCNF。
   - 所以，这个表只满足 **1NF**。

## 练习2：规范化到1NF

将以下表规范化到第一范式：

```
员工表
| 员工号 | 姓名 | 技能 | 项目 |
|--------|------|------|------|
| E001   | 王五 | Java, Python | 项目A, 项目B |
| E002   | 赵六 | C++, Java | 项目B |
```

**解析**：

第一范式 (1NF) 的要求是表中的每一列都是不可分割的原子值。在给定的员工表中，“技能”和“项目”列包含了多个值，违反了1NF。

为了将其规范化到1NF，我们需要消除这些多值属性。可以有多种方法，常见的是将多值属性拆分成独立的行，或者创建新的关联表。

**方法一：拆分成独立的行 (如果技能和项目之间没有直接的多对多关系)**

如果一个员工的每个技能可以独立于项目，或者每个项目可以独立于技能，我们可以简单地为每个技能和每个项目创建新行。但这通常会导致数据冗余和更新异常。

**方法二：创建独立的表来表示多对多关系 (更常见和推荐的做法)**

我们可以将技能和项目分别拆分成独立的表，并通过一个连接表来管理员工、技能和项目之间的关系。但题目只要求到1NF，最直接的方式是消除非原子列。

**规范化到1NF的结果**：

我们可以将每个技能和每个项目拆分成单独的行，并重复其他信息。

员工技能表 (1NF)

| 员工号 | 姓名 | 技能    |

|--------|------|---------|

| E001   | 王五 | Java    |

| E001   | 王五 | Python  |

| E002   | 赵六 | C++     |

| E002   | 赵六 | Java    |

员工项目表 (1NF)

| 员工号 | 姓名 | 项目   |

|--------|------|--------|

| E001   | 王五 | 项目A  |

| E001   | 王五 | 项目B  |

| E002   | 赵六 | 项目B  |

或者，如果技能和项目是同时发生的（例如，员工E001在项目A中使用了Java和Python，在项目B中也使用了Java和Python），那么1NF的表可能是这样的：

员工技能项目表 (1NF)

| 员工号 | 姓名 | 技能    | 项目   |

|--------|------|---------|--------|

| E001   | 王五 | Java    | 项目A  |

| E001   | 王五 | Python  | 项目A  |

| E001   | 王五 | Java    | 项目B  |

| E001   | 王五 | Python  | 项目B  |

| E002   | 赵六 | C++     | 项目B  |

| E002   | 赵六 | Java    | 项目B  |

这种方式消除了非原子列，满足了1NF。但请注意，这可能会引入其他问题，后续的范式会解决这些问题。对于本题，仅要求到1NF，上述拆分即可。

更简洁的1NF转换，如果只考虑消除非原子性，可以为每个技能和项目组合创建一行：

员工表 (1NF)

| 员工号 | 姓名 | 技能    | 项目   |

|--------|------|---------|--------|

| E001   | 王五 | Java    | 项目A  |

| E001   | 王五 | Java    | 项目B  |

| E001   | 王五 | Python  | 项目A  |

| E001   | 王五 | Python  | 项目B  |

| E002   | 赵六 | C++     | 项目B  |

| E002   | 赵六 | Java    | 项目B  |

或者，如果技能和项目是独立的属性，可以拆分成：

员工技能表 (1NF)

| 员工号 | 姓名 | 技能    |

|--------|------|---------|

| E001   | 王五 | Java    |

| E001   | 王五 | Python  |

| E002   | 赵六 | C++     |

| E002   | 赵六 | Java    |

员工项目表 (1NF)

| 员工号 | 姓名 | 项目   |

|--------|------|--------|

| E001   | 王五 | 项目A  |

| E001   | 王五 | 项目B  |

| E002   | 赵六 | 项目B  |

题目没有明确技能和项目的关系，最直接的1NF转换是确保每个单元格只有一个值。通常会将原表拆分为多个表。

**最终1NF方案 (拆分为多个表，更符合后续规范化思路)**：

员工表 (Employee)

| 员工号 | 姓名 |

|--------|------|

| E001   | 王五 |

| E002   | 赵六 |

员工技能表 (EmployeeSkill)

| 员工号 | 技能    |

|--------|---------|

| E001   | Java    |

| E001   | Python  |

| E002   | C++     |

| E002   | Java    |

(主键: (员工号, 技能))

员工项目表 (EmployeeProject)

| 员工号 | 项目   |

|--------|--------|

| E001   | 项目A  |

| E001   | 项目B  |

| E002   | 项目B  |

(主键: (员工号, 项目))

这样每个表都满足1NF。

## 练习3：规范化到2NF

给定以下图书借阅表，将其规范化到第二范式：

```
借阅表
主键：(读者证号, 图书ISBN, 借阅日期)
| 读者证号 | 图书ISBN | 借阅日期 | 读者姓名 | 读者电话 | 书名 | 作者 | 出版社 |
|----------|----------|----------|----------|----------|------|------|--------|
| R001     | ISBN001  | 2024-01-10 | 陈七   | 13800138000 | 数据库原理 | 王教授 | 清华出版社 |
```

**解析**：

第二范式 (2NF) 的要求是：

1. 表必须满足1NF。
2. 表中的所有非主属性必须完全函数依赖于候选键。不能存在部分函数依赖。

**1. 识别函数依赖**：

- (读者证号, 图书ISBN, 借阅日期) → 读者姓名, 读者电话, 书名, 作者, 出版社 (这是主键决定的，所以是完全依赖)
- 读者证号 → 读者姓名, 读者电话 (读者信息由读者证号唯一确定)
- 图书ISBN → 书名, 作者, 出版社 (图书信息由图书ISBN唯一确定)
- 确定候选键：

题目已给出主键为 (读者证号, 图书ISBN, 借阅日期)。这是一个复合主键。

3. 检查是否存在部分函数依赖：

非主属性有：读者姓名, 读者电话, 书名, 作者, 出版社。

- 读者姓名, 读者电话 依赖于 读者证号。而 读者证号 只是主键 (读者证号, 图书ISBN, 借阅日期) 的一部分。因此，存在部分函数依赖：(读者证号) → (读者姓名, 读者电话)。
- 书名, 作者, 出版社 依赖于 图书ISBN。而 图书ISBN 只是主键 (读者证号, 图书ISBN, 借阅日期) 的一部分。因此，存在部分函数依赖：(图书ISBN) → (书名, 作者, 出版社)。
- 规范化到2NF：

为了消除部分函数依赖，我们需要将表拆分。

- 读者表 (Reader)：包含只依赖于读者证号的属性。

  | 读者证号 (主键) | 读者姓名 | 读者电话 |

  |-----------------|----------|----------|

  | R001            | 陈七     | 13800138000 |

- 图书表 (Book)：包含只依赖于图书ISBN的属性。

  | 图书ISBN (主键) | 书名     | 作者   | 出版社     |

  |-----------------|----------|--------|------------|

  | ISBN001         | 数据库原理 | 王教授 | 清华出版社 |

- 借阅记录表 (BorrowingRecord)：包含完全依赖于整个主键 (读者证号, 图书ISBN, 借阅日期) 的属性。在本例中，原始表除了被拆分出去的属性外，没有其他非主属性完全依赖于整个主键。因此，这个表只包含主键本身，或者如果还有其他属性（比如借阅状态等）是完全依赖于整个主键的，则会包含进来。

  | 读者证号 (外键) | 图书ISBN (外键) | 借阅日期   |

  |-----------------|-----------------|------------|

  | R001            | ISBN001         | 2024-01-10 |

  (主键: (读者证号, 图书ISBN, 借阅日期))

这样，每个表都满足了2NF，因为消除了部分函数依赖。

## 练习4：规范化到3NF

将以下员工表规范化到第三范式：

```
员工表
| 员工号 | 姓名 | 部门号 | 部门名称 | 部门经理工号 | 部门经理姓名 | 工资等级 | 基本工资 |
|--------|------|--------|----------|--------------|--------------|----------|----------|
| E001   | 张三 | D01    | 销售部   | E100         | 王经理       | 3        | 8000     |
| E002   | 李四 | D01    | 销售部   | E100         | 王经理       | 2        | 6000     |
| E003   | 王五 | D02    | 技术部   | E200         | 李经理       | 3        | 8000     |
```

假设：工资等级 → 基本工资

**解析**：

第三范式 (3NF) 的要求是：

1. 表必须满足2NF。
2. 表中的所有非主属性不能传递函数依赖于候选键。即，任何非主属性都不能依赖于其他非主属性。

**1. 确定主键和函数依赖**：

- 主键：员工号 (假设员工号唯一标识一个员工)
- 函数依赖：
  - 员工号 → 姓名, 部门号, 部门名称, 部门经理工号, 部门经理姓名, 工资等级, 基本工资 (完全依赖于主键)
  - 部门号 → 部门名称, 部门经理工号 (部门信息由部门号决定)
  - 部门经理工号 → 部门经理姓名 (经理姓名由经理工号决定)
  - 工资等级 → 基本工资 (题目给定)
- 检查是否满足2NF：

由于主键是单一属性 (员工号)，不存在部分函数依赖。因此，该表满足2NF。

**3. 检查是否存在传递函数依赖**：

- 员工号 → 部门号，并且 部门号 → 部门名称。所以，员工号 → 部门名称 是一个传递依赖。
- 员工号 → 部门号，并且 部门号 → 部门经理工号。所以，员工号 → 部门经理工号 是一个传递依赖。
- 员工号 → 部门号，并且 部门号 → 部门经理工号，并且 部门经理工号 → 部门经理姓名。所以，员工号 → 部门经理姓名 是一个更长的传递依赖。
- 员工号 → 工资等级，并且 工资等级 → 基本工资。所以，员工号 → 基本工资 是一个传递依赖。

由于存在传递依赖，该表不满足3NF。

4. 规范化到3NF：

为了消除传递依赖，我们需要将表拆分。

- 员工表 (Employee)：

  | 员工号 (主键) | 姓名 | 部门号 (外键) | 工资等级 (外键) |

  |---------------|------|---------------|-----------------|

  | E001          | 张三 | D01           | 3               |

  | E002          | 李四 | D01           | 2               |

  | E003          | 王五 | D02           | 3               |

- 部门表 (Department)：

  | 部门号 (主键) | 部门名称 | 部门经理工号 (外键，指向员工表) |

  |---------------|----------|---------------------------------|

  | D01           | 销售部   | E100                            |

  | D02           | 技术部   | E200                            |

  (这里假设部门经理也是员工，所以部门经理工号可以作为外键指向员工表，但如果经理信息不全在员工表中，则需要单独的经理表或直接存储经理姓名)

  考虑到部门经理姓名，更合理的拆分是：

  部门表 (Department)：

  | 部门号 (主键) | 部门名称 | 部门经理工号 |

  |---------------|----------|--------------|

  | D01           | 销售部   | E100         |

  | D02           | 技术部   | E200         |

  经理表 (Manager) 或在员工表中体现：

  如果经理本身也是员工，并且其姓名在员工表中有，则不需要单独的经理姓名列在部门表。

  原始表中 部门经理姓名 依赖于 部门经理工号。

  (部门经理工号 → 部门经理姓名)

  如果 E100 和 E200 也是员工，那么他们的姓名应该在员工表中。

  假设 部门经理工号 → 部门经理姓名 是一个独立的依赖，可以创建一个 经理表，或者如果经理就是员工，则此信息已在员工表中。

  为了简单起见，我们先拆分部门和工资等级。

- 工资等级表 (SalaryGrade)：

  | 工资等级 (主键) | 基本工资 |

  |-----------------|----------|

  | 3               | 8000     |

  | 2               | 6000     |

- 关于部门经理姓名：

  部门经理工号 → 部门经理姓名。

  如果部门经理也是员工，那么这个依赖应该通过员工表来体现，即 (员工号 → 姓名)。

  如果 E100 王经理 和 E200 李经理 也在员工表中，那么部门表只需要存储 部门经理工号。

  假设 E100 和 E200 是员工表中的员工号：

  员工表 (Employee) 最终版：

  | 员工号 (主键) | 姓名   | 部门号 (外键) | 工资等级 (外键) |

  |---------------|--------|---------------|-----------------|

  | E001          | 张三   | D01           | 3               |

  | E002          | 李四   | D01           | 2               |

  | E003          | 王五   | D02           | 3               |

  | E100          | 王经理 | (假设其部门)  | (假设其工资等级) |

  | E200          | 李经理 | (假设其部门)  | (假设其工资等级) |

  部门表 (Department) 最终版：

  | 部门号 (主键) | 部门名称 | 部门经理工号 (外键，指向员工表员工号) |

  |---------------|----------|---------------------------------------|

  | D01           | 销售部   | E100                                  |

  | D02           | 技术部   | E200                                  |

  工资等级表 (SalaryGrade) 最终版：

  | 工资等级 (主键) | 基本工资 |

  |-----------------|----------|

  | 3               | 8000     |

  | 2               | 6000     |

这样，每个表都满足了3NF，消除了传递依赖。

## 练习5：综合练习

某在线课程平台的数据表如下：

```
课程注册表
| 学生ID | 学生姓名 | 学生邮箱 | 课程ID | 课程名称 | 讲师ID | 讲师姓名 | 讲师职称 | 注册日期 | 完成进度 | 证书编号 | 证书颁发日期 |
|--------|----------|----------|--------|----------|--------|----------|----------|----------|----------|----------|--------------|
| S001   | 张同学   | zhang@email | C101 | Python基础 | T001 | 李老师 | 高级讲师 | 2024-01-01 | 100% | CERT001 | 2024-02-01 |
| S001   | 张同学   | zhang@email | C102 | 数据分析 | T002 | 王老师 | 讲师 | 2024-01-15 | 60% | NULL | NULL |
| S002   | 李同学   | li@email | C101 | Python基础 | T001 | 李老师 | 高级讲师 | 2024-01-10 | 100% | CERT002 | 2024-02-10 |
```

**任务**：

1. 识别所有函数依赖
2. 将此表规范化到3NF
3. 画出规范化后的ER图（用文字描述）

**解析**：

1. **识别所有函数依赖**：

   - 学生ID → 学生姓名, 学生邮箱
   - 课程ID → 课程名称, 讲师ID (假设一门课固定一个主讲老师)
   - 讲师ID → 讲师姓名, 讲师职称
   - (学生ID, 课程ID) → 注册日期, 完成进度, 证书编号, 证书颁发日期 (一个学生注册一门特定课程有唯一的注册信息)
   - 证书编号 → 证书颁发日期 (如果证书编号是唯一的，那么它也决定了颁发日期，但通常证书是针对某个学生某门课的完成情况)
     - 更准确地说，(学生ID, 课程ID) → 证书编号 (如果完成的话)
     - 如果证书编号是全局唯一的，则 证书编号 → 学生ID, 课程ID, 证书颁发日期。但从表结构看，证书编号可能与学生和课程相关。我们先假设 (学生ID, 课程ID) 决定证书相关信息。

   *传递依赖*:

   - 课程ID → 讲师ID, 讲师ID → 讲师姓名  => 课程ID → 讲师姓名 (传递依赖)
   - 课程ID → 讲师ID, 讲师ID → 讲师职称  => 课程ID → 讲师职称 (传递依赖)
   - (学生ID, 课程ID) → 课程ID (平凡依赖), 课程ID → 课程名称 => (学生ID, 课程ID) → 课程名称 (部分依赖，因为课程名称仅依赖于课程ID)
   - (学生ID, 课程ID) → 课程ID (平凡依赖), 课程ID → 讲师ID => (学生ID, 课程ID) → 讲师ID (部分依赖)
   - (学生ID, 课程ID) → 课程ID (平凡依赖), 课程ID → 讲师ID, 讲师ID → 讲师姓名 => (学生ID, 课程ID) → 讲师姓名 (部分且传递依赖)
   - (学生ID, 课程ID) → 课程ID (平凡依赖), 课程ID → 讲师ID, 讲师ID → 讲师职称 => (学生ID, 课程ID) → 讲师职称 (部分且传递依赖)
   - (学生ID, 课程ID) → 学生ID (平凡依赖), 学生ID → 学生姓名 => (学生ID, 课程ID) → 学生姓名 (部分依赖)
   - (学生ID, 课程ID) → 学生ID (平凡依赖), 学生ID → 学生邮箱 => (学生ID, 课程ID) → 学生邮箱 (部分依赖)

2. **将此表规范化到3NF**：

   - **确定主键**：对于原始的“课程注册表”，能够唯一标识一条注册记录的是 (学生ID, 课程ID)。

   - **1NF**：表中属性都是原子的，满足1NF。

   - **2NF (消除部分依赖)**：

     - 非主属性：学生姓名, 学生邮箱, 课程名称, 讲师ID, 讲师姓名, 讲师职称, 注册日期, 完成进度, 证书编号, 证书颁发日期。
     - 部分依赖于 学生ID：学生姓名, 学生邮箱。
     - 部分依赖于 课程ID：课程名称, 讲师ID (进而间接依赖讲师姓名、职称)。
     - 完全依赖于 (学生ID, 课程ID)：注册日期, 完成进度, 证书编号, 证书颁发日期。

     拆分：

     - 学生表 (Student)

       | 学生ID (主键) | 学生姓名 | 学生邮箱    |

       |---------------|----------|-------------|

       | S001          | 张同学   | zhang@email |

       | S002          | 李同学   | li@email    |

     - 课程表 (Course) (先包含与课程直接相关的)

       | 课程ID (主键) | 课程名称   | 讲师ID (外键) |

       |---------------|------------|---------------|

       | C101          | Python基础 | T001          |

       | C102          | 数据分析   | T002          |

     - 注册表 (Enrollment)

       | 学生ID (外键) | 课程ID (外键) | 注册日期   | 完成进度 | 证书编号 | 证书颁发日期 |

       |---------------|---------------|------------|----------|----------|--------------|

       | S001          | C101          | 2024-01-01 | 100%     | CERT001  | 2024-02-01   |

       | S001          | C102          | 2024-01-15 | 60%      | NULL     | NULL         |

       | S002          | C101          | 2024-01-10 | 100%     | CERT002  | 2024-02-10   |

       (主键: (学生ID, 课程ID))

   - 3NF (消除传递依赖)：

     检查现有表是否存在传递依赖。

     - 学生表：学生ID → 学生姓名, 学生ID → 学生邮箱。没有非主属性依赖于其他非主属性。满足3NF。

     - 注册表：(学生ID, 课程ID) 是主键。非主属性有注册日期, 完成进度, 证书编号, 证书颁发日期。

       - 如果 证书编号 → 证书颁发日期，且证书编号不是候选键，则存在传递依赖。假设证书编号是针对特定学生特定课程的，那么它不是独立的决定因素。如果证书编号是全局唯一的，则需要进一步拆分。从数据看，CERT001 和 CERT002 不同，可能不是全局唯一标识学生和课程的。
       - 假设 (学生ID, 课程ID) → 证书编号，并且 (学生ID, 课程ID) → 证书颁发日期。如果完成课程才有证书，那么证书编号和颁发日期都依赖于 (学生ID, 课程ID) 以及完成状态。
       - 如果一个学生完成一门课只有一个证书，那么 (学生ID, 课程ID) → 证书编号, 证书颁发日期。没有传递依赖。注册表满足3NF。

     - 课程表：课程ID → 课程名称, 讲师ID。

       我们还有依赖：讲师ID → 讲师姓名, 讲师职称。

       所以在课程表中，课程ID → 讲师ID，并且 讲师ID → 讲师姓名, 讲师职称。

       这是一个传递依赖：课程ID → 讲师姓名, 讲师职称。

       需要拆分讲师信息。

     最终3NF拆分：

     - 学生表 (Student)

       | 学生ID (主键) | 学生姓名 | 学生邮箱    |

       |---------------|----------|-------------|

       | S001          | 张同学   | zhang@email |

       | S002          | 李同学   | li@email    |

     - 讲师表 (Instructor)

       | 讲师ID (主键) | 讲师姓名 | 讲师职称 |

       |---------------|----------|----------|

       | T001          | 李老师   | 高级讲师 |

       | T002          | 王老师   | 讲师     |

     - 课程表 (Course)

       | 课程ID (主键) | 课程名称   | 讲师ID (外键) |

       |---------------|------------|---------------|

       | C101          | Python基础 | T001          |

       | C102          | 数据分析   | T002          |

     - 注册表 (Enrollment)

       | 学生ID (外键) | 课程ID (外键) | 注册日期   | 完成进度 | 证书编号 | 证书颁发日期 |

       |---------------|---------------|------------|----------|----------|--------------|

       | S001          | C101          | 2024-01-01 | 100%     | CERT001  | 2024-02-01   |

       | S001          | C102          | 2024-01-15 | 60%      | NULL     | NULL         |

       | S002          | C101          | 2024-01-10 | 100%     | CERT002  | 2024-02-10   |

       (主键: (学生ID, 课程ID))

       (如果证书编号是全局唯一的，并且可以决定学生和课程，那么注册表可能需要进一步调整，但根据当前信息，此结构是合理的3NF)

3. **画出规范化后的ER图（用文字描述）**：

   - **实体1：学生 (Student)**
     - 属性：学生ID (主键), 学生姓名, 学生邮箱
   - **实体2：讲师 (Instructor)**
     - 属性：讲师ID (主键), 讲师姓名, 讲师职称
   - **实体3：课程 (Course)**
     - 属性：课程ID (主键), 课程名称
     - 关系：课程 **由 (is taught by)** 一名讲师 (一对多关系，课程指向讲师，外键为讲师ID)
   - **实体4：注册 (Enrollment)** (这是一个关联实体，表示学生和课程之间的多对多关系)
     - 属性：注册日期, 完成进度, 证书编号, 证书颁发日期
     - 关系：
       - 一名学生 **可以注册 (enrolls in)** 多门课程 (通过注册表连接，学生ID为外键)
       - 一门课程 **可以被 (is enrolled by)** 多名学生注册 (通过注册表连接，课程ID为外键)
     - 主键：(学生ID, 课程ID)

   **关系描述**：

   - 一个 **学生** 可以有多次 **注册** 记录 (一对多，通过学生ID关联)。
   - 一个 **课程** 可以有多次 **注册** 记录 (一对多，通过课程ID关联)。
   - 因此，学生和课程之间是多对多的关系，通过 **注册** 表实现。
   - 一个 **课程** 由一个 **讲师** 讲授 (一对多关系，课程表中的讲师ID是外键，指向讲师表)。或者说，一个讲师可以讲授多门课程。

## 练习6：BCNF练习

考虑以下场景的预约表：

```
预约表
| 医生 | 患者 | 时间段 | 诊室 |
|------|------|--------|------|
| 张医生 | 患者A | 9:00-10:00 | 101室 |
| 李医生 | 患者B | 9:00-10:00 | 102室 |
| 张医生 | 患者C | 10:00-11:00 | 101室 |
```

约束条件：

- 一个医生在同一时间只能看一个患者
- 一个诊室在同一时间只能有一个医生使用
- 一个患者在同一时间只能看一个医生

**任务**：

1. 找出所有候选键
2. 这个表是否满足BCNF？如果不满足，请规范化

**解析**：

1. 找出所有候选键：

   候选键是能唯一确定一条记录的最小属性集。

   根据约束条件：

   - (医生, 时间段) → 患者, 诊室 (一个医生在特定时间段只能看一个患者，并使用一个诊室)
   - (诊室, 时间段) → 医生, 患者 (一个诊室在特定时间段只能被一个医生使用，看一个患者)
   - (患者, 时间段) → 医生, 诊室 (一个患者在特定时间段只能被一个医生看，在一个诊室)

   从这些函数依赖可以看出，以下组合都可以作为候选键：

   - **(医生, 时间段)**
   - **(患者, 时间段)**
   - **(诊室, 时间段)**

   我们来验证一下：

   - (医生, 时间段)：张医生在 9:00-10:00 只能是 患者A 和 101室。唯一。
   - (患者, 时间段)：患者A 在 9:00-10:00 只能是 张医生 和 101室。唯一。
   - (诊室, 时间段)：101室 在 9:00-10:00 只能是 张医生 和 患者A。唯一。

   因此，候选键有三个：**(医生, 时间段)**, **(患者, 时间段)**, **(诊室, 时间段)**。

2. 这个表是否满足BCNF？如果不满足，请规范化：

   BCNF (Boyce-Codd Normal Form) 的要求是：对于表中每一个非平凡的函数依赖 X → Y，X 都必须是候选键（或超键）。

   我们已知的函数依赖（基于约束）：

   1. (医生, 时间段) → 患者
   2. (医生, 时间段) → 诊室
   3. (患者, 时间段) → 医生
   4. (患者, 时间段) → 诊室
   5. (诊室, 时间段) → 医生
   6. (诊室, 时间段) → 患者

   这些依赖的决定因素 (医生, 时间段), (患者, 时间段), (诊室, 时间段) 都是候选键。到目前为止，看起来满足BCNF。

   但是，还需要考虑是否存在其他可能的函数依赖。

   例如，从数据中，我们是否可以推断出其他依赖？

   - 医生 → 诊室? 不一定，张医生用了101室，但李医生也可能用101室在不同时间。

   - (医生, 患者) → 时间段, 诊室?

     - 张医生, 患者A → 9:00-10:00, 101室

     - 张医生, 患者C → 10:00-11:00, 101室

       这个依赖的决定因素 (医生, 患者) 不是我们之前确定的候选键。如果这个依赖成立，那么表就不满足BCNF。

   让我们重新审视约束：

   - "一个医生在同一时间只能看一个患者" => (医生, 时间段) → 患者
   - "一个诊室在同一时间只能有一个医生使用" => (诊室, 时间段) → 医生
   - "一个患者在同一时间只能看一个医生" => (患者, 时间段) → 医生

   从这些基本依赖，可以推导出：

   - 因为 (诊室, 时间段) → 医生，并且 (医生, 时间段) → 患者，所以 (诊室, 时间段) → 患者 (通过传递性，如果医生是唯一的)。
   - 因为 (患者, 时间段) → 医生，并且 (医生, 时间段) → 诊室 (如果一个医生在特定时间段也只用一个诊室)，那么 (患者, 时间段) → 诊室。

   考虑一个更深层次的依赖：如果一个医生通常在固定的诊室工作，例如：

   - 医生 → 诊室 (例如，张医生总是在101室，李医生总是在102室)

     如果这个隐藏的依赖存在，并且 医生 不是候选键，那么就会违反BCNF。

     从给出的数据看：

   - 张医生 → 101室 (在两个时间段都是)

   - 李医生 → 102室

   假设存在函数依赖： **医生 → 诊室**。

   - 决定因素是 “医生”。
   - “医生” 本身不是候选键 (例如，仅凭“张医生”不能唯一确定一行记录，因为他有多个预约)。
   - 由于存在一个非平凡函数依赖 (医生 → 诊室)，其决定因素“医生”不是候选键，所以该表**不满足BCNF**。

   规范化到BCNF：

   如果存在 医生 → 诊室，我们需要分解表来消除这个不满足BCNF的依赖。

   - 医生诊室表 (DoctorRoom)

     | 医生 (主键) | 诊室   |

     |-------------|--------|

     | 张医生      | 101室  |

     | 李医生      | 102室  |

   - 预约记录表 (Appointment)

     剩下的属性是 (医生, 患者, 时间段)。

     候选键可以是 (医生, 时间段) 或 (患者, 时间段)。

     | 医生 (外键) | 患者   | 时间段     |

     |-------------|--------|------------|

     | 张医生      | 患者A  | 9:00-10:00 |

     | 李医生      | 患者B  | 9:00-10:00 |

     | 张医生      | 患者C  | 10:00-11:00|

     (主键: (医生, 时间段) 或 (患者, 时间段))

   检查新的预约记录表是否满足BCNF：

   - 候选键：(医生, 时间段), (患者, 时间段)

   - 函数依赖：

     - (医生, 时间段) → 患者 (决定因素是候选键)

     - (患者, 时间段) → 医生 (决定因素是候选键)

       这个表现在满足BCNF了。

   结论：

   如果存在隐藏的依赖如 “医生 → 诊室”，则原表不满足BCNF。规范化后的表如上所示。

   如果不存在 “医生 → 诊室” 这个依赖（即医生可以在不同时间使用不同诊室，只是恰好这次数据如此），那么原表的候选键是 (医生, 时间段), (患者, 时间段), (诊室, 时间段)，并且所有基本函数依赖的决定因素都是候选键，那么原表就满足BCNF。

   通常在BCNF分析中，我们会寻找所有可能的函数依赖。从给定的约束和数据来看，"医生 → 诊室" 是一个合理的推断，导致了不满足BCNF。

## 练习7：反规范化决策

某电商平台的订单查询非常频繁，当前的规范化设计需要连接5个表才能显示完整的订单信息：

- 订单表（订单ID, 客户ID, 订单日期, 总金额）
- 订单明细表（订单ID, 商品ID, 数量, 小计）
- 客户表（客户ID, 姓名, 地址, 电话）
- 商品表（商品ID, 商品名, 分类ID, 单价）
- 分类表（分类ID, 分类名）

**任务**：

1. 分析这种设计的优缺点
2. 如果查询性能成为瓶颈，你会如何进行反规范化？
3. 反规范化会带来什么问题？如何解决？

**解析**：

1. **分析这种设计的优缺点**：

   **优点 (规范化设计)**：

   - **减少数据冗余**：每个数据项只存储一次。例如，客户的地址只在客户表中存储，商品名称只在商品表中存储。这节省了存储空间。
   - **提高数据一致性**：由于数据冗余少，当数据需要更新时，只需要修改一个地方，避免了因多处存储导致的数据不一致风险。例如，客户更改地址，只需更新客户表。
   - **数据完整性更容易维护**：通过外键约束等，可以更好地保证数据的有效性和关联性。
   - **插入、删除、更新异常少**：规范化的表结构可以避免因数据组织不当导致的更新异常（如修改一个信息需要改多行）、插入异常（如没有某个实体就无法插入另一个相关实体的信息）、删除异常（如删除一个实体导致另一个不相关实体的信息丢失）。
   - **表结构更清晰，易于理解和维护**：每个表关注一个特定的实体或关系。

   **缺点 (规范化设计)**：

   - **查询性能可能较低**：获取完整信息通常需要连接多个表 (JOIN操作)。当数据量大、连接复杂时，查询会变得缓慢。在本例中，显示完整订单信息需要连接5个表。
   - **查询语句更复杂**：需要编写包含多个JOIN的SQL语句。

2. **如果查询性能成为瓶颈，你会如何进行反规范化？**

   反规范化是通过引入受控的冗余数据来减少表连接，从而提高查询性能。具体方法：

   - **增加冗余字段**：将经常一起查询的字段从一个表复制到另一个表。

     - 例如，在 **订单表** 中增加 `客户姓名`。这样查询订单列表时，不需要再连接客户表来获取客户姓名。

       ```
       订单表_反规范化 (OrderID, CustomerID, CustomerName, OrderDate, TotalAmount)
       ```

     - 例如，在 **订单明细表** 中增加 `商品名称` 和 `单价` (当时的单价)。这样查询订单明细时，不需要连接商品表。

       ```
       订单明细表_反规范化 (OrderID, ProductID, ProductName, UnitPrice, Quantity, Subtotal)
       ```

       注意：这里的 `单价` 应该是下单时的单价，因为商品表中的单价可能会变动。

   - **合并表**：如果两个表总是一起被查询，并且它们之间是一对一或紧密的一对多关系，可以考虑合并它们。

     - 例如，如果商品分类信息不常变动且分类不多，并且总是和商品一起查询，可以考虑将 `分类名` 直接加入到 **商品表** 中，消除分类表。

       ```
       商品表_反规范化 (ProductID, ProductName, CategoryName, UnitPrice)
       ```

       但这会增加商品表的数据冗余，如果一个分类下有很多商品。

   - **创建汇总表或预计算表**：对于复杂的聚合查询，可以预先计算结果并存储在新的表中。

     - 例如，创建一个每日销售汇总表，包含每日的总销售额、订单数等。

   - **针对特定查询优化**：分析最频繁、性能最差的查询，针对性地进行反规范化。例如，如果“显示订单详情页”是最关键的查询，就优先优化这个查询所需的数据。

   在本例中，一个常见的反规范化策略是：

   - 在 `订单表` 中加入 `客户姓名`。

   - 在 订单明细表 中加入 商品名称 和下单时的 单价。

     这样，查询一个订单及其所有明细时，可能只需要连接 订单表 和 订单明细表_反规范化，或者甚至可以将订单头信息也冗余到订单明细中（如果订单明细是主要查询对象）。

3. **反规范化会带来什么问题？如何解决？**

   **带来的问题**：

   - **数据冗余增加**：同一个数据可能存储在多个地方，浪费存储空间。
   - **数据一致性维护困难**：当冗余数据发生变化时，需要更新所有存储该数据的地方。如果更新不及时或遗漏，就会导致数据不一致。例如，客户修改了姓名，不仅要更新客户表，还要更新所有包含该客户姓名的订单表记录。
   - **插入、更新、删除操作更复杂**：因为需要维护冗余数据的一致性，这些操作的逻辑会变复杂，性能也可能受影响。
   - **可能引入新的更新异常**：如果冗余数据管理不当，可能会出现新的数据不一致问题。

   **如何解决/缓解这些问题**：

   - **应用程序层面维护一致性**：在应用程序代码中实现逻辑，确保当主数据更新时，所有相关的冗余数据也同步更新。这增加了应用程序的复杂性。
   - **使用触发器 (Triggers)**：在数据库层面设置触发器，当主表数据发生变化时，自动更新其他表中的冗余数据。这可以保证数据一致性，但会增加数据库的负载，并可能使调试复杂化。
   - **批处理更新**：定期运行批处理作业来同步冗余数据。这种方式数据一致性不是实时的，会有一定的延迟。
   - **权衡利弊，适度反规范化**：只在确实能带来显著性能提升且必要的地方进行反规范化。不要过度反规范化。
   - **选择性冗余**：只冗余那些变化不频繁但查询频繁的字段。例如，商品名称通常比商品价格变化少。
   - **记录历史快照**：对于像订单明细中的商品价格，存储的是下单那一刻的快照价格，这本身就是一种有意义的冗余，因为它记录了历史状态，而不是简单地复制当前商品价格。
   - **文档数据库或NoSQL方案**：对于某些高度非结构化或需要极高性能读的场景，可以考虑使用NoSQL数据库，它们通常采用非规范化的数据模型。

   总的来说，反规范化是一种用空间换时间、用增加维护复杂性换取查询性能的策略，需要仔细评估和设计。

## 练习8：实战案例

设计一个图书馆管理系统的数据库，需要记录：

- 图书信息（ISBN、书名、作者、出版社、出版年份、分类、库存数量）
- 读者信息（读者证号、姓名、类型、院系、联系方式）
- 借阅记录（包括借阅日期、应还日期、实际归还日期）
- 预约记录（读者可以预约已被借出的图书）
- 罚款记录（超期罚款）

**任务**：

1. 设计初始表结构
2. 规范化到3NF
3. 考虑实际使用场景，是否需要适度反规范化？

**解析**：

1. **设计初始表结构 (未完全规范化)**：

   - **图书表 (Books_Initial)**
     - ISBN (主键)
     - 书名
     - 作者 (可能有多个作者，需要考虑如何处理)
     - 出版社名称
     - 出版年份
     - 分类名称
     - 库存数量
   - **读者表 (Readers_Initial)**
     - 读者证号 (主键)
     - 姓名
     - 读者类型名称 (如：本科生, 研究生, 教师)
     - 院系名称
     - 联系方式 (电话, 邮箱)
   - **借阅表 (Borrowings_Initial)**
     - 借阅ID (主键)
     - ISBN (外键)
     - 读者证号 (外键)
     - 借阅日期
     - 应还日期
     - 实际归还日期
     - 书名 (冗余)
     - 读者姓名 (冗余)
   - **预约表 (Reservations_Initial)**
     - 预约ID (主键)
     - ISBN (外键)
     - 读者证号 (外键)
     - 预约日期
     - 通知状态 (如：已通知, 未通知)
     - 书名 (冗余)
     - 读者姓名 (冗余)
   - **罚款表 (Fines_Initial)**
     - 罚款ID (主键)
     - 借阅ID (外键，关联到某次超期借阅)
     - 读者证号 (外键)
     - ISBN (外键)
     - 罚款金额
     - 缴纳状态 (如：未缴纳, 已缴纳)
     - 产生日期

2. **规范化到3NF**：

   **a. 处理多值属性和基础实体**

   - **作者表 (Authors)**
     - 作者ID (主键)
     - 作者姓名
   - **图书作者表 (BookAuthors)** (连接图书和作者，处理多对多关系)
     - ISBN (外键, 主键一部分)
     - 作者ID (外键, 主键一部分)
   - **出版社表 (Publishers)**
     - 出版社ID (主键)
     - 出版社名称
   - **分类表 (Categories)**
     - 分类ID (主键)
     - 分类名称
   - **读者类型表 (ReaderTypes)**
     - 读者类型ID (主键)
     - 读者类型名称
   - **院系表 (Departments)**
     - 院系ID (主键)
     - 院系名称

   **b. 规范化核心实体表**

   - **图书表 (Books)** - 3NF
     - ISBN (主键)
     - 书名
     - 出版社ID (外键)
     - 出版年份
     - 分类ID (外键)
     - 库存数量 (这个字段需要特别管理，每次借阅/归还都会变动)
     - *函数依赖：ISBN → 书名, 出版社ID, 出版年份, 分类ID, 库存数量*
     - *没有部分依赖 (主键单一)，没有传递依赖 (出版社名称、分类名称已分离)*
   - **读者表 (Readers)** - 3NF
     - 读者证号 (主键)
     - 姓名
     - 读者类型ID (外键)
     - 院系ID (外键)
     - 联系方式 (假设联系方式是单一字段，或可拆分为电话、邮箱等)
     - *函数依赖：读者证号 → 姓名, 读者类型ID, 院系ID, 联系方式*
     - *没有部分依赖，没有传递依赖 (读者类型名称、院系名称已分离)*
   - **借阅记录表 (BorrowingRecords)** - 3NF
     - 借阅ID (主键，可选，也可以用 (ISBN, 读者证号, 借阅日期) 作为复合主键，但单一代理主键更简单)
     - ISBN (外键)
     - 读者证号 (外键)
     - 借阅日期
     - 应还日期
     - 实际归还日期
     - *函数依赖：借阅ID → ISBN, 读者证号, 借阅日期, 应还日期, 实际归还日期*
     - *没有部分依赖，没有传递依赖*
   - **预约记录表 (ReservationRecords)** - 3NF
     - 预约ID (主键)
     - ISBN (外键)
     - 读者证号 (外键)
     - 预约日期
     - 通知状态
     - 预约到期日 (可选，表示预约保留期限)
     - *函数依赖：预约ID → ISBN, 读者证号, 预约日期, 通知状态, 预约到期日*
     - *没有部分依赖，没有传递依赖*
   - **罚款记录表 (FineRecords)** - 3NF
     - 罚款ID (主键)
     - 借阅ID (外键，指向某次具体的借阅记录)
     - 罚款金额
     - 缴纳状态
     - 产生日期
     - 缴纳日期 (可选)
     - *函数依赖：罚款ID → 借阅ID, 罚款金额, 缴纳状态, 产生日期, 缴纳日期*
     - *没有部分依赖，没有传递依赖。读者证号和ISBN可以通过借阅ID间接获得，不需要直接存储，避免传递依赖。*

   **规范化后的表清单**：

   1. `Authors` (作者ID, 作者姓名)
   2. `Publishers` (出版社ID, 出版社名称)
   3. `Categories` (分类ID, 分类名称)
   4. `ReaderTypes` (读者类型ID, 读者类型名称)
   5. `Departments` (院系ID, 院系名称)
   6. `Books` (ISBN, 书名, 出版社ID, 出版年份, 分类ID, 库存数量)
   7. `BookAuthors` (ISBN, 作者ID) - 主键(ISBN, 作者ID)
   8. `Readers` (读者证号, 姓名, 读者类型ID, 院系ID, 联系方式)
   9. `BorrowingRecords` (借阅ID, ISBN, 读者证号, 借阅日期, 应还日期, 实际归还日期)
   10. `ReservationRecords` (预约ID, ISBN, 读者证号, 预约日期, 通知状态, 预约到期日)
   11. `FineRecords` (罚款ID, 借阅ID, 罚款金额, 缴纳状态, 产生日期, 缴纳日期)

3. **考虑实际使用场景，是否需要适度反规范化？**

   是的，在实际使用场景中，纯粹的3NF可能会导致某些常用查询性能不佳，可以考虑适度反规范化。

   **可能的反规范化点**：

   - **图书列表显示**：在显示图书列表时，经常需要显示书名、作者、出版社、分类。

     - 当前3NF设计需要连接 `Books`, `BookAuthors`, `Authors`, `Publishers`, `Categories` 表。如果作者很多，`BookAuthors` 和 `Authors` 的连接可能开销较大。

     - **反规范化方案1**：在 `Books` 表中冗余一个 `主要作者姓名` 字段。或者冗余一个拼接好的 `作者列表字符串`（不推荐，难以查询）。

     - **反规范化方案2**：在 `Books` 表中冗余 `出版社名称` 和 `分类名称`。

       ```
       Books_Denorm (ISBN, 书名, 出版社ID, 出版社名称, 出版年份, 分类ID, 分类名称, 库存数量, 主要作者姓名)
       ```

       这会增加更新时的复杂性（当出版社或分类名称更改时），但如果这些名称不常变，可以接受。

   - **借阅/预约/罚款记录查询**：在查看这些记录时，通常需要显示图书的书名和读者姓名。

     - **反规范化方案**：在 `BorrowingRecords`, `ReservationRecords`, `FineRecords` 中冗余 `书名` 和 `读者姓名`。

       ```
       BorrowingRecords_Denorm (借阅ID, ISBN, 书名, 读者证号, 读者姓名, 借阅日期, 应还日期, 实际归还日期)
       ```

       这样做的好处是查询这些记录时不需要再连接 Books 和 Readers 表，显著提高查询效率。

       坏处是当书名或读者姓名更改时（虽然不常见），需要同步更新这些记录中的冗余字段。但对于历史记录，通常记录的是当时的信息快照，所以这种冗余有时是合理的。

   - **库存数量 (Books.库存数量)**：

     - 这是一个高频更新的字段。每次借书、还书都会影响它。
     - 如果并发量大，直接更新这个字段可能会有锁竞争。
     - **替代方案/优化**：
       - 可以通过计算未归还的借阅记录数来动态计算可用库存：`总入库数 - SUM(已借出未还)`。但这会使查询库存变慢。
       - 保留 `库存数量` 字段，并通过事务严格控制其更新。
       - 对于热门图书，`库存数量` 的实时准确性非常重要。

   - **常用统计报表**：

     - 例如，“热门借阅图书排行”、“读者借阅量排行”等。
     - **反规范化方案**：创建预计算的汇总表，定期更新。例如，`BookBorrowStats (ISBN, 书名, 借阅次数)`。

   **决策依据**：

   - **查询频率和性能要求**：对于非常频繁且对性能要求高的查询，优先考虑反规范化。
   - **数据更新频率**：冗余的数据项更新越不频繁，反规范化的维护成本越低。
   - **数据一致性要求**：如果对数据一致性要求极高且实时，反规范化需要更复杂的机制来保证。
   - **系统复杂度**：反规范化会增加系统的设计和维护复杂度。

   **结论**：对于图书馆系统，在 `借阅记录表`、`预约记录表` 中冗余 `书名` 和 `读者姓名` 是比较常见的反规范化实践，因为这些记录一旦生成，其关联的图书和读者信息通常作为历史快照存在。在 `图书表` 中冗余 `出版社名称` 和 `分类名称` 也可以考虑，如果这些信息不常变动。`库存数量` 的处理需要特别小心，确保数据准确性。